{% set data = {'page': 'util'} %}
{% extends "template/base.html"%}

{% block content %}
<section id="layout">
    <h1 class="title">LAYOUT</h1>
    <div class="demo-item">
        <p class="demo-desc">平铺</p>
        <div class="demo-block">
            <ul class="ui-tiled">
                <li><div>菜单</div><i>1</i></li>
                <li><div>菜单</div><i>2</i></li>
                <li><div>菜单</div><i>3</i></li>
            </ul>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">flex布局排列，默认横排</p>
        <div class="demo-block">
            <div class="ui-flex ui-flex-pack-start">
                <div>左</div><div>左</div>
            </div>
            <div class="ui-flex ui-flex-pack-center">
                <div>中</div><div>中</div>
            </div>
            <div class="ui-flex ui-flex-pack-end">
                <div>右</div><div>右</div>
            </div>
            <div class="ui-flex ui-flex-align-start">
                <div>上</div><div>上</div>
            </div>
            <div class="ui-flex  ui-flex-align-center">
                <div>中</div><div>中</div>
            </div>
            <div class="ui-flex ui-flex-align-end">
                <div>下</div><div>下</div>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">flex布局排列纵向,以此类推</p>
        <div class="demo-block">
            <div class="ui-flex ui-flex-ver ui-flex-pack-center ui-flex-align-start">
                <div>左中</div>
                <div>左中</div>
            </div>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">flex网格布局</p>
        <div class="demo-block">
            <ul class="ui-row-flex">
                <li class="ui-col ui-flex ui-flex-pack-start"><div>左</div><div>左</div></li>
                <li class="ui-col ui-flex ui-flex-pack-center"><div>中</div><div>中</div></li>
                <li class="ui-col ui-flex ui-flex-pack-end"><div>右</div><div>右</div></li>
                <li class="ui-col ui-flex ui-flex-ver  ui-flex-align-end ui-flex-pack-end"><div>竖排右下</div><div>竖排右下</div></li>
            </ul>
        </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">布局1</p>
        <div class="demo-block">
            <header class="ui-header ui-header-stable ui-border-b">
                <i class="ui-icon-return"></i><h1>layout</h1><button class="ui-btn">回首页</button>
            </header>
            <footer class="ui-footer ui-footer-stable ui-border-t">
                <ul class="ui-tiled">
                    <li><div>菜单</div><i>1</i></li>
                    <li><div>菜单</div><i>2</i></li>
                    <li><div>菜单</div><i>3</i></li>
                </ul>
            </footer>
            <section class="ui-container ui-center">
                内容
            </section>
         </div>
    </div>
    <div class="demo-item">
        <p class="demo-desc">布局2</p>
        <div class="demo-block">
            <header class="ui-header ui-header-positive ui-border-b">
                <h1>标题</h1>
            </header>
            <div class="ui-footer ui-footer-stable ui-btn-group ui-border-t">
                <button class="ui-btn-lg">
                    拒绝
                </button>
                <button class="ui-btn-lg">
                    拒绝
                </button>
                <button class="ui-btn-lg ui-btn-primary">
                    同意
                </button>
            </div>
            <section class="ui-container ui-center">
                内容
            </section>
         </div>
    </div>
</section>
{% endblock%}
