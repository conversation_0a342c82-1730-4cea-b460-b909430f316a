
<svg width="80px" height="80px" viewBox="0 0 80 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 39.1 (31720) - http://www.bohemiancoding.com/sketch -->
    <desc>Created with Sketch.</desc>
    <defs>
        <circle id="path-1" cx="40" cy="40" r="40"></circle>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="80" height="80" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <use id="Oval" stroke="#956F6F" mask="url(#mask-2)" stroke-width="4" fill="none" xlink:href="#path-1"></use>
    <path d="M39,39 L19.0016723,39 C18.4498156,39 18,39.4477153 18,40 C18,40.5561352 18.448464,41 19.0016723,41 L39,41 L39,60.9983277 C39,61.5501844 39.4477153,62 40,62 C40.5561352,62 41,61.551536 41,60.9983277 L41,41 L60.9983277,41 C61.5501844,41 62,40.5522847 62,40 C62,39.4438648 61.551536,39 60.9983277,39 L41,39 L41,19.0016723 C41,18.4498156 40.5522847,18 40,18 C39.4438648,18 39,18.448464 39,19.0016723 L39,39 Z" id="Combined-Shape" stroke="none" fill="#783A3A" fill-rule="evenodd" transform="translate(40.000000, 40.000000) rotate(45.000000) translate(-40.000000, -40.000000) "></path>
</svg>
