<link rel="stylesheet" type="text/css" href="__STATIC__/css/swiper.min.css">
<script src="__STATIC__/js/swiper.min.js" type="text/javascript"></script>
<script src="__COMMON__/layer/layer.js"></script>
<style>
    .chenggong {
        color: aliceblue !important;
        padding: 4px 8px !important;
        border-radius: 3px;
        background-color: green;
    }
</style>
<div class="header-sy">
    <h4><input type="text" name="" id="souval" value="" placeholder="搜素物品/宠物/武器"><i  id="sousou" class="iconfont">&#xe6c8;</i></h4>
</div>
<div class="index-content">
    <div class="banner-border">
        <div class="swiper-container swiper-container-horizontal">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <img src="__STATIC__/picture/banner/tp0.png">
                </div>
                <div class="swiper-slide">
                    <img src="__STATIC__/picture/banner/tp1.png">
                </div>
                <div class="swiper-slide">
                    <img src="__STATIC__/picture/banner/tp1-1.png">
                </div>
                <div class="swiper-slide">
                    <img src="__STATIC__/picture/banner/tp2.png">
                </div>
                <div class="swiper-slide">
                    <img src="__STATIC__/picture/banner/tp3.png">
                </div>
                <div class="swiper-slide">
                    <img src="__STATIC__/picture/banner/tp4.png">
                </div>
                <div class="swiper-slide">
                    <img src="__STATIC__/picture/banner/tp5.png">
                </div>
                <div class="swiper-slide">
                    <img src="__STATIC__/picture/banner/tp6.png">
                </div>
                <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span>
            </div>
        </div>
    </div>
    <div class="gonggao">
        <span><img src="__STATIC__/picture/sytu9.png"></span>
        <marquee><span>{$notice_new.content|default=''}</span></marquee>
    </div>
    <div class="header-sy-nr">
        <ul>

            <a href="{:url('itemlist',array('type'=>1))}">
                <li>
                    <img src="__STATIC__/picture/zawu.png">
                    <p>杂货物品</p>
                </li>
            </a>
            <a href="https://mhxy.netlify.app/yq/qun1.html">
                <li>
                    <img src="__STATIC__/picture/wuqi.png">
                    <p>微信群</p>
                </li>
            </a>
            <a href="{:url('Common/download')}">
                <li>
                    <img src="__STATIC__/picture/chongwu.png">
                    <p>下 载</p>
                </li>
            </a>
            <a href="{:url('Common/strategy')}">
                <li>
                    <img src="__STATIC__/picture/gonglue.png">
                    <p>攻 略</p>
                </li>
            </a>
        </ul>
    </div>
</div>
<div class="header-sy-nr2">
    <p class="hwhd">特价物品</p>
    <i class="iconfont">&#xe6a7;</i>
    <span>更多</span>
</div>

<div class="header-sy-nr3">
    <ul style="">
        {notempty name='shop_list'}
        {volist name='shop_list' id='vo'}
        <a href="javascript:" class="infoto" info="{$vo.info}">

            <li style="margin-top: 10px;">
                <div class="spwai">
                    <img style="width: auto;" src="/upload/picture/{$vo['image']}">
                    <div class="spxinxi">
                        <p>{$vo.name}*{$vo.num}</p>
                        <span>{$vo.price} 币</span>
                        <p style="margin-top: 4px;"><span class="chenggong" name="" id="{$vo.id}">下单</span></p>
                    </div>
                </div>
            </li>
        </a>
        {/volist}
        {/notempty}
    </ul>
</div>
<!--轮播图-->
<script type="text/javascript">
    var mySwiper = new Swiper('.swiper-container', {
        direction: 'horizontal',
        loop: true,
        autoplay: true
    });
    $(document).ready(function () {

        $('.infoto').click(function () {
            var info = $(this).attr('info');
            layer.open({
                type: 0,
                title: '详细',
                content: info
            })
        });
        $('#sousou').click(function () {
            var souval = $("#souval").val();
            if(souval.length<2){
                layer.open({
                    type: 0,
                    title: '提示',
                    content: '搜索词不能少于两个字'
                })
                return false;
            }

            window.location.href = "{:url('index/itemlist')}?search="+$("#souval").val();

        });
        var xiadan = false;
        $('.chenggong').click(function () {
            var id = $(this).attr('id');
            event.stopPropagation();    //  阻止事件冒泡

            if("{$userAuth_sign}"==0){
                layer.msg('您还未登录', {
                    icon: 1, time: 1500, end: function () {
                        window.location.href = "{:url('login/login')}";
                    }
                });
            }

            if(!confirm("确定下单吗！！！")) {
                return false;
            }
            if (!xiadan) {
                xiadan=true;
				$.ajax({
					url: "{:url('shop/goOrder')}",
					type: 'post',
					dataType: "json",
					data: {id: id},
					success: function (res) {
						if (res.code > 0) {
							layer.alert(res.msg, {
								icon: 5,
								title: "提示"
							});
						} else if (res.code == 0) {
							layer.msg(res.msg, {
								icon: 1, time: 1500, end: function () {
									// window.location.href = "{:url('user/index')}";
								}
							});
						}
                        xiadan=false;
					}
				});
            }
        });

    })
</script>