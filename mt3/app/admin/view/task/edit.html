<link rel="stylesheet" href="__STATIC__/module/admin/ext/datetimepicker/css/datetimepicker.css">
<form action="{:url()}" method="post" class="form_single">
    <div class="box">
        <div class="box-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>任务名称</label>
                        <span>（任务名称）</span>
                        <input class="form-control" name="name" placeholder="请输入任务名称"
                               value="{$info['name']|default=''}" type="text">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>类型</label>
                        <span>（类型）</span>
                        <select class="form-control" name="type">
                            <option value="1">后台币</option>
                            <option value="2">游戏物品</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>后台币</label>
                        <span>（后台币）</span>
                        <input class="form-control" name="money" placeholder=""
                               value="{$info['money']|default='0'}" type="number">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>物品</label>
                        <span>（物品）</span>
                        <select class="form-control" name="itemid">
                            {volist name='shop_column' id='vo'}
                            <option value="{$vo.itemid}">{$vo.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>物品数量</label>
                        <span>（物品数量）</span>
                        <input class="form-control" name="num"
                               value="{$info['num']|default='1'}" type="number">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>服区</label>
                        <span>（服区）</span>
                        <select class="form-control" name="server_id">
                            {volist name='server_column' id='vo'}
                            <option value="{$vo.server_id}">{$vo.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>结束时间</label>
                        <span class="">（结束时间）</span><input name="end_time" size="16" type="text"
                                                           value='{$info.end_time}' class="form_datetime form-control">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>总人数量</label>
                        <span>（满人无法参与）</span>
                        <input class="form-control" name="limit_number" placeholder="请输入默认浏览量"
                               value="{$info['limit_number']|default='9999'}" type="number">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>轮播图</label>
                        <span class="">（轮播图 分辨率宽高比率  375:154  可以等比例放大）</span>{assign name="img_json"
                        value="$info.img_json|default=''" /}
                        {:widget('file/index', ['name' => 'img_json', 'value' => $img_json, 'type' => 'imgs'])}
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>介绍</label>
                            <textarea class="form-control textarea_editor" name="info" placeholder="请输入文章内容">{$info['info']|default=''}</textarea>
                            {:widget('editor/index', array('name'=> 'info','value'=>''))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="box-footer">
            <input type="hidden" name="id" value="{$info['id']|default='0'}"/>
            {include file="layout/edit_btn_group"/}
        </div>

    </div>

</form>
<script src="__STATIC__/module/admin/ext/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $(".form_datetime").datetimepicker({format: 'yyyy-mm-dd hh:ii'});
        ob.setValue("type", {$info.type |default= 1});
    });
</script>