<form action="{:url()}" method="post" class="form_single">
	<div class="box">
		<div class="box-body">
			<div class="row">

				<div class="col-md-6">
					<div class="form-group">
						<label>类型</label>
						<span>（类型）</span>
						<select class="form-control" name="type">
							<option value="1">后台币</option>
							<option value="2">游戏物品</option>
						</select>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>后台币</label>
						<span>（后台币）</span>
						<input class="form-control" name="money"
							   value="{$info['money']|default='0'}" type="number">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>物品</label>
						<span>（物品）</span>
						<select class="form-control" name="itemid">
							{volist name='shop_column' id='vo'}
							<option value="{$vo.itemid}">{$vo.name}</option>
							{/volist}
						</select>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>物品数量</label>
						<span>（物品数量）</span>
						<input class="form-control" name="num"
							   value="{$info['num']|default='1'}" type="number">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>生成个数</label>
						<span class="">（最大个数10000，超出可能卡死）</span>
						<input class="form-control" name="number" placeholder="请输入整数" value="" type="number">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>备注</label>
						<span class="">（备注信息）</span>
						<input class="form-control" name="info" placeholder="备注使用信息" value="" type="text">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>渠道id</label>
						<span class="">（渠道id）</span>
						<select class="form-control" name="server_id">
							{notempty name='server_id'}
							{volist name='server_id' id='vo'}
							<option value="{$vo.server_id}">{$vo.name}</option>
							{/volist}
							{/notempty}
							
						</select>
					</div>
				</div>

			</div>
			
			<div class="box-footer">
				<input type="hidden" name="id" value="{$info['id']|default='0'}"/>
				{include file="layout/edit_btn_group"/}
			</div>
		</div>
	</div>
</form>
<script type="text/javascript">
</script>