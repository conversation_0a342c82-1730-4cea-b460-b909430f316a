<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

class Draw extends LogicBase
{

      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }


     public function getDrawInfo($where = [], $field = '*')
     {

        return $this->modelDraw->getInfo($where, $field);
     }

    public function getDrawList($where = [], $field = '', $order = 'id desc', $paginate = 0,$limit=0)
    {
        if(!empty($limit)){
            $this->modelDraw->limit=$limit;
        }
        return $this->modelDraw->getList($where, $field, $order, $paginate);
    }


    public function getDrawColumn($where = [], $field = '', $key = '')
    {
        return $this->modelDraw->getColumn($where, $field , $key);
    }


    public function clickEdit($data = [])
    {
		
        $result = $this->modelDraw->setInfo($data);
        
        return $result ? $result : $this->modelDraw->getError();
    }

    public function ClickDel($where = [], $is_true = false)
    {

        $result = $this->modelDraw->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelDraw->getError();
    }

    public function setDrawIncDec($where = [], $field = 'score', $number = 1, $setType = 'setInc')
    {
        $this->modelDraw->setIncDecInfo($where, $field, $number, $setType);
    }
	
	//Admin

    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    public function ClickAmindEdit($data = [])
    {


        $validate_result = $this->validateDraw->scene('edit')->check($data);

        if (!$validate_result) {
            return [RESULT_ERROR, $this->validateDraw->getError()];
        }

        $url = url('clickList');
        
        $data['member_id'] = MEMBER_ID;

        $this->logicDrawDirectory->DrawDirectorySetList($DrawDirectory);

		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '课程' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '课程操作成功', $url] : [RESULT_ERROR, $this->modelDraw->getError()];
    }

    public function DrawAdminDel($where = [])
    {

        $result = $this->modelDraw->deleteInfo($where);
        
        $result && action_log('删除', '课程删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '课程删除成功'] : [RESULT_ERROR, $this->modelDrawClick->getError()];
    }
}
