<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tbp\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CreateBot请求参数结构体
 *
 * @method string getBotName() 获取机器人名称
 * @method void setBotName(string $BotName) 设置机器人名称
 * @method string getBotCnName() 获取机器人中文名称
 * @method void setBotCnName(string $BotCnName) 设置机器人中文名称
 */
class CreateBotRequest extends AbstractModel
{
    /**
     * @var string 机器人名称
     */
    public $BotName;

    /**
     * @var string 机器人中文名称
     */
    public $BotCnName;

    /**
     * @param string $BotName 机器人名称
     * @param string $BotCnName 机器人中文名称
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("BotName",$param) and $param["BotName"] !== null) {
            $this->BotName = $param["BotName"];
        }

        if (array_key_exists("BotCnName",$param) and $param["BotCnName"] !== null) {
            $this->BotCnName = $param["BotCnName"];
        }
    }
}
