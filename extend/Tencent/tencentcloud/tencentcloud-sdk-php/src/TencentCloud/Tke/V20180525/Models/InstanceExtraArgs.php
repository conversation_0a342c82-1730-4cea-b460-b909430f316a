<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Tke\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * 节点自定义参数
 *
 * @method array getKubelet() 获取kubelet自定义参数
注意：此字段可能返回 null，表示取不到有效值。
 * @method void setKubelet(array $Kubelet) 设置kubelet自定义参数
注意：此字段可能返回 null，表示取不到有效值。
 */
class InstanceExtraArgs extends AbstractModel
{
    /**
     * @var array kubelet自定义参数
注意：此字段可能返回 null，表示取不到有效值。
     */
    public $Kubelet;

    /**
     * @param array $Kubelet kubelet自定义参数
注意：此字段可能返回 null，表示取不到有效值。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("Kubelet",$param) and $param["Kubelet"] !== null) {
            $this->Kubelet = $param["Kubelet"];
        }
    }
}
