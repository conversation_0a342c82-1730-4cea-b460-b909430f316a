<?php
/*
 * Copyright (c) 2017-2018 THL A29 Limited, a Tencent company. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
namespace TencentCloud\Cms\*********\Models;
use TencentCloud\Common\AbstractModel;

/**
 * CreateTextSample返回参数结构体
 *
 * @method string getErrMsg() 获取操作样本失败时返回的错误信息示例：  "样本1":错误码，"样本2":错误码
 * @method void setErrMsg(string $ErrMsg) 设置操作样本失败时返回的错误信息示例：  "样本1":错误码，"样本2":错误码
 * @method integer getProgress() 获取任务状态
1：已完成
2：处理中
 * @method void setProgress(integer $Progress) 设置任务状态
1：已完成
2：处理中
 * @method string getRequestId() 获取唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 * @method void setRequestId(string $RequestId) 设置唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
 */
class CreateTextSampleResponse extends AbstractModel
{
    /**
     * @var string 操作样本失败时返回的错误信息示例：  "样本1":错误码，"样本2":错误码
     */
    public $ErrMsg;

    /**
     * @var integer 任务状态
1：已完成
2：处理中
     */
    public $Progress;

    /**
     * @var string 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    public $RequestId;

    /**
     * @param string $ErrMsg 操作样本失败时返回的错误信息示例：  "样本1":错误码，"样本2":错误码
     * @param integer $Progress 任务状态
1：已完成
2：处理中
     * @param string $RequestId 唯一请求 ID，每次请求都会返回。定位问题时需要提供该次请求的 RequestId。
     */
    function __construct()
    {

    }

    /**
     * For internal only. DO NOT USE IT.
     */
    public function deserialize($param)
    {
        if ($param === null) {
            return;
        }
        if (array_key_exists("ErrMsg",$param) and $param["ErrMsg"] !== null) {
            $this->ErrMsg = $param["ErrMsg"];
        }

        if (array_key_exists("Progress",$param) and $param["Progress"] !== null) {
            $this->Progress = $param["Progress"];
        }

        if (array_key_exists("RequestId",$param) and $param["RequestId"] !== null) {
            $this->RequestId = $param["RequestId"];
        }
    }
}
