.date_btn,.date_grid {
	box-sizing: border-box
}

.date_grid,.date_info_box {
	border-bottom: 1px solid rgb(201, 23, 35);
}

.date_btn,.date_class,.date_class_box,.date_info_box,.tooth {
	text-align: center
}

.gearDate {
	font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
	font-size: 10px;
	background-color: rgba(0,0,0,.7);
	display: block;
	position: absolute;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 9900;
	overflow: hidden;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	-webkit-transform: translateZ(0);
}

.date_ctrl {
	vertical-align: middle;
	background-color: #F0F0F0;
	box-shadow: 0 0 2px rgba(0,0,0,.4);
	color: #363837;
	height: auto;
	max-width:500px;
	width: 95%;
	border-radius: 5px;
	position: absolute;
	left: 0;
	right: 0;
	top: 20%;
	z-index: 9902;
	overflow: hidden;
	margin: 0 auto;
	-webkit-transform: translateY(0);
	transform: translateY(0);
	-ms-transform: translateY(0);
	-moz-transform: translateY(0);
}

.date_roll,.date_roll>div {
	background-color: transparent;
	overflow: hidden;
}

@-webkit-keyframes slideInUp {
	0% {
		-webkit-transform: translateY(-100%);
		transform: translateY(-100%);
		-moz-transform: translateY(-100%);
		-ms-transform: translateY(-100%);
		-o-transform: translateY(-100%)
	}

	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		-moz-transform: translateY(0);
		-o-transform: translateY(0);
		-ms-transform: translateY(0)
	}
}

@keyframes slideInUp {
	0% {
		-webkit-transform: translateY(-100%);
		transform: translateY(-100%);
		-moz-transform: translateY(-100%);
		-ms-transform: translateY(-100%);
		-o-transform: translateY(-100%)
	}

	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		-moz-transform: translateY(0);
		-o-transform: translateY(0);
		-ms-transform: translateY(0)
	}
}

@-moz-keyframes slideInUp {
	0% {
		-webkit-transform: translateY(-100%);
		transform: translateY(-100%);
		-moz-transform: translateY(-100%);
		-ms-transform: translateY(-100%);
		-o-transform: translateY(-100%)
	}

	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		-moz-transform: translateY(0);
		-o-transform: translateY(0);
		-ms-transform: translateY(0)
	}
}

@-ms-keyframes slideInUp {
	0% {
		-webkit-transform: translateY(-100%);
		transform: translateY(-100%);
		-moz-transform: translateY(-100%);
		-ms-transform: translateY(-100%);
		-o-transform: translateY(-100%)
	}

	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		-moz-transform: translateY(0);
		-o-transform: translateY(0);
		-ms-transform: translateY(0)
	}
}

.date_roll {
	position: relative;
	width: 100%;
	height: auto;
	font-weight: 700;
	-webkit-mask: -webkit-gradient(linear,0 50%,0 100%,from(#debb47),to(rgba(36,142,36,0)));
	-webkit-mask: -webkit-linear-gradient(top,#debb47 50%,rgba(36,142,36,0));
}

.date_roll>div {
	font-size: 2em;
	height: 6em;
	float: left;
	position: relative;
	width: 33.33%
}

.date_roll_more>div {
	width: 20%
}
.date_roll_more>div:last-child {
	width: 40%
}
.date_roll_mask {
	padding: 0 0.1em;
	-webkit-mask: -webkit-gradient(linear,0 40%,0 0,from(#debb47),to(rgba(36,142,36,0)));
	-webkit-mask: -webkit-linear-gradient(bottom,#debb47 50%,rgba(36,142,36,0));
}

.date_grid {
	position: relative;
	top: 2em;
	width: 100%;
	height: 2em;
	margin: 0;
	z-index: 0;
	background-color: #ffffff;
	border-top: 1px solid rgb(201, 23, 35);
	box-shadow: rgb(201, 23, 35) 0px 0px 6px;
}

.date_grid>div {
	color: #000;
	position: absolute;
	right: 0;
	top: 0;
	font-size: .8em;
	line-height: 2.5em
}

.date_info_box {
	color: #363837;
	font-size: 1.8em;
	padding: .6em 0;
	font-weight: 700
}

.date_class_box {
	padding: 1em 2em
}

.date_class {
    display: inline-block;
    font-size: 16px;
    width: 48%;
    padding: .4em 0;
    color: #c91723;
    border: 1px solid #c91723;
    cursor: pointer;
    line-height: 1.6em;
    font-family: Microsoft YaHei, "\5FAE\8F6F\96C5\9ED1", Helvetica, STHeiti, Droid Sans Fallback;
}

.date_class_box .date_class:first-child {
	border-top-left-radius: .2em;
	border-bottom-left-radius: .2em
}

.date_class_box .date_class:last-child {
	border-top-right-radius: .2em;
	border-bottom-right-radius: .2em
}

.date_class_box .active {
	background-color: #c91723;
	color: #fff
}

.date_btn_box {
	overflow: hidden;
	position: relative;
	border-top: 1px solid #d4d4d4
}

.date_btn {
	color: #000;
	font-size: 1.6em;
	line-height: 1em;
	padding: .8em 1em;
	cursor: pointer;
	float: left;
	width: 50%
}

.date_btn:first-child {
	border-right: 1px solid #d4d4d4;
	background-color: #d00b0b;
	color: #fff
}

.gear {
	float: left;
	position: absolute;
	z-index: 9902;
	width: 5.5em;
	margin-top: -6em;
	cursor: pointer
}

.date_roll>div .gear {
	width: 100%;
	font-size: 20px;
}

.tooth {
	height: 2em;
	line-height: 2em
}

.date_confirm_wrap {
	padding: 1em 2em;
	text-align: center;
	display: none
}

.confirm_tit {
	display: inline-block;
	font-size: 18px;
	color: #d00b0b;
	cursor: pointer;
	line-height: 1.6em;
	margin-bottom: 1em;
}

.confirm_p {
	font-size: 1.6em;
	line-height: 2em
}

.confirm_p b {
	color: #c91723;
	font-weight: 400
}

.date_hh .tooth{
	line-height: 40px;
	height: 40px;
	font-size: 18px !important;
}