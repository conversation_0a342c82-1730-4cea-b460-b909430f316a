{"name": "weui", "version": "2.3.0", "description": "A UI library by WeChat official design team, includes the most useful widgets/modules in mobile web applications.", "keywords": ["weui", "wechat", "weixin", "css", "less", "mobile"], "style": "dist/style/weui.css", "less": "src/style/weui.less", "main": "dist/style/weui.css", "scripts": {"start": "gulp -ws", "test": "gulp build", "changelog": "picklog -l -w CHANGELOG.md", "build": "gulp build", "version": "git add -A .", "postversion": "git push && git push --tags", "lint": "stylelint src", "lint:fix": "stylelint src --fix"}, "author": "wechat ui team", "repository": {"type": "git", "url": "https://github.com/weui/weui.git"}, "homepage": "https://github.com/weui/weui", "bugs": {"url": "https://github.com/weui/weui/issues"}, "license": "MIT", "devDependencies": {"autoprefixer": "^6.3.1", "browser-sync": "^2.9.11", "gulp": "^3.9.1", "gulp-convert-css-var": "^0.1.3", "gulp-cssnano": "^2.0.0", "gulp-header": "^1.7.1", "gulp-less": "^4.0.1", "gulp-postcss": "^6.0.1", "gulp-rename": "^1.2.2", "gulp-replace": "^0.5.2", "gulp-sourcemaps": "^2.2.0", "gulp-tap": "^0.1.3", "picklog": "^2.1.3", "postcss-discard-comments": "^2.0.4", "stylelint": "^13.0.0", "stylelint-config-standard": "^19.0.0", "yargs": "^1.3.3"}}