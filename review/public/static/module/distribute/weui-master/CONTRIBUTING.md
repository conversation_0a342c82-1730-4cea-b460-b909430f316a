### Contributing

欢迎参与 WeUI 的贡献，你可以给我们提出意见、建议，报告 bug，或者贡献代码。在参与贡献之前，请阅读以下指引。

#### 关于命名规范
WeUI 1.0.0与以往版本不同，采用了新的命名规范。但为了大家便于理解，可以按BEM来看WeUI的代码。其中 横杠是连词符，双下划线区分B与E，单下划线后面跟Modify。

#### 咨询问题

简单的咨询，如询问如何使用、询问示例是如何实现的或其他和 WeUI 无关的技术问题，请在官方 QQ 群（478234996）中询问，效率更高。

#### 关于 issue

WeUI 的发展，离不开社区的贡献。如果你对 WeUI 的现状有意见、建议或者发现了 bug，欢迎通过 issue 给我们提出。提 issue 之前，请阅读以下指引。

- 搜索以往的 issue ，看是否已经提过，避免重复提出；
- 请确认你遇到的问题，是否在最新版本已被修复；
- 提出意见或建议时，请描述：
    - 现状
    - 给你带来了什么问题
    - 你的期望结果
    - 可能的实现方式（可选）
- 如果是报告 bug，请提供可以复现的条件：
    - 机型
    - 平台
    - 系统版本
    - 微信客户端版本
    - WeUI 版本
    - bug 表现
    - 是否必现
    - 最好可以提供截图
    - 最好可以提供示例代码，推荐使用 http://codepen.io
- 如果你的问题已经得到解决，请关闭你的 issue。

#### 贡献者名单

非常感谢以下几位贡献者对weui做出的贡献：

- 感谢@Paranoid_K、@AmMRLi、@qichhhhh 在交流群内积极参与weui相关问题的讨论和答疑解惑。

- 感谢@n7best（<EMAIL>） 积极参与`react-weui`的开发工作，贡献了大量代码，使得`react-weui`依赖的`weui`版本升级到 1.x ，同时提升了 `react-weui` 代码质量。

*@Paranoid_K、@AmMRLi、@qichhhhh 的QB奖励以及 @n7best 优秀贡献者证书已由腾讯开源工作人员联系发放。

#### 参与贡献

如果你有好的意见或建议，欢迎给我们提 Issues 或 Pull Requests，为提升微信 Web 体验贡献力量。<br>详见：[CONTRIBUTING.md](https://github.com/Tencent/weui/blob/master/CONTRIBUTING.md)

[腾讯开源激励计划](https://opensource.tencent.com/contribution) 鼓励开发者的参与和贡献，期待你的加入。
