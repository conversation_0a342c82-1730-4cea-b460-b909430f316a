<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wexc\controller;

/**
 * 首页模块控制器
 */
class Homemodule extends UserBase
{

    /**
     * 首页模块列表
     */
    public function homeModuleList()
    {

        $where = $this->logicHomeModule->getWhere($this->param_data);

        $data=$this->logicHomeModule->getHomeModuleList($where, '', '');

		return $this->apiReturn($data);
    }
    /**
     * 首页模块无分页列表
     */
    public function homeModuleColumn()
    {

        $where = $this->logicHomeModule->getWhere($this->param_data);

        $where['status']=1;

        $data['list']=$this->logicHomeModule->getHomeModuleColumn($where, $field = '', $key = '','cover_id desc');

        $data['imgs']=$this->logicFile->getPictureListUrl($data['list'],'cover_id');

        array_multisort(array_column($data['list'],'sort'),SORT_NUMERIC,SORT_ASC,$data['list']);


		return $this->apiReturn($data);
    }
    /**
     * 首页模块数据
     */
    public function homeModuleDataColumn()
    {

        $data = $this->logicThemeData->themeColumnData($this->param_data['theme_id']);

        return $this->apiReturn($data);
    }
    /**
     * 首页模块添加
     */
    public function homeModuleAdd()
    {
	  
	   $regit=$this->logicHomeModule->homeModuleEdit($this->param_data);
		
        return $this->apiReturn(['id'=>$regit]);
		
    }



    /**
     * 首页模块删除
     */
    public function homeModuleDel()
    {

       $regit=$this->logicHomeModule->homeModuleDel(['id' => $id]);
	
	   return $this->apiReturn(['id'=>$regit]);
	   
    }
}
