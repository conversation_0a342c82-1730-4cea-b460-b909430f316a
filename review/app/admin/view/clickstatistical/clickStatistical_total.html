<link rel="stylesheet" href="__STATIC__/module/admin/ext/datetimepicker/css/datetimepicker.css">
<script src="__STATIC__/module/admin/ext/datetimepicker/js/bootstrap-datetimepicker.min.js"></script>
<div class="box">
	<div class="box-header">
		<div class="row search-form">
			<div class="col-sm-2">
				<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">开始时间</button>
						</span>
					<input name="start_time" size="16" type="text"
					       value="{$Think.get.start_time|default=''}" readonly
					       class="form_datetime form-control">
				</div>
			</div>
			<div class="col-sm-2">
				<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">结束时间</button>
						</span>
					<input name="end_time" size="16" type="text"
					       value="{$Think.get.end_time|default=''}" readonly
					       class="form_datetime form-control">
				</div>
			</div>
			<div class="col-sm-8">
				<div class="row">
					<div class="col-xs-2">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">渠道</button>
						</span>
							<select class="form-control" name="channel">
								<option value="all">不限</option>
								{notempty name='channel'}
								{volist name='channel' id='vo'}
								<option value="{$vo.code}">{$vo.name}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-xs-2">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">活动</button>
						</span>
							<select class="form-control" name="activity">
								<option value="0">所有</option>
								{notempty name='activity'}
								{volist name='activity' id='vo'}
								<option value="{$vo.activity}">{$vo.activity}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-xs-2">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">页面</button>
						</span>
							<select class="form-control" name="url">
								<option value="0">所有</option>
								{notempty name='url'}
								{volist name='url' id='vo'}
								<option value="{$vo.url}">{$vo.url}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-xs-2">
						<div class="input-group input-group-sm">
						<span class="input-group-btn">
                           <button class="btn btn-default" type="button">事件</button>
						</span>
							<select class="form-control" name="button">
								<option value="0">所有</option>
								{notempty name='button'}
								{volist name='button' id='vo'}
								<option value="{$vo.button}">{$vo.button}</option>
								{/volist}
								{else/}
								{/notempty}
							</select>
						</div>
					</div>
					<div class="col-xs-2">
						<div class="input-group input-group-sm">
							<input type="text" name="search_data" style="" class="form-control pull-right"
							       value="{:input('search_data')}" placeholder="支持标题|描述">
							<div class="input-group-btn">
								<button type="button" id="search" url="{:url('clickStatisticalTotal')}"
								        class="btn btn-info btn-flat"><i
										class="fa fa-search"></i></button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover">
			<thead>
			<th>openid</th>
			<th>活动</th>
			<th>渠道code</th>
			<th>页面名字</th>
			<th>访问次数</th>
			</thead>
			
			{notempty name='list'}
			<tbody>
			
			{volist name='list' id='vo'}
			<tr>
				<td>{$vo.openid}</td>
				<td>{$vo.activity}</td>
				<td>{$vo.channel}</td>
				<td>{$vo.url}</td>
				<td>{$vo.count_p}</td>
			</tr>
			{/volist}
			
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="7" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
			{include file="layout/special"/}
		</table>
	</div>
	<div class="box-footer clearfix text-center">
		{$list->render()}
	</div>
</div>
<script type="text/javascript">
    ob.setValue("channel", "{present name='$_GET["channel"]'}{$Think.get.channel}{else}all{/present}");
    ob.setValue("activity", "{present name='$_GET["activity"]'}{$Think.get.activity}{else}0{/present}");
    ob.setValue("url", "{present name='$_GET["url"]'}{$Think.get.url}{else}0{/present}");
    ob.setValue("button", "{present name='$_GET["button"]'}{$Think.get.button}{else}0{/present}");
    $(document).ready(function () {

        $(".form_datetime").datetimepicker({format: 'yyyy-mm-dd hh:ii'});
        //导出功能
        $(".export").click(function () {

            window.location.href = searchFormUrl($(".export"));
        });
    })

</script>