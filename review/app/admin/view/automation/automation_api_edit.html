<form action="{:url()}" method="post" class="form_single">
    <div class="box">
        <div class="box-body">
            <div class="row">
                <div class="col-md-12">
                    <div class="box box-success">
                        <div class="box-header with-border">
                            <h3 class="box-title">数据库/模型 选择</h3>

                            <div class="box-tools pull-right">
                                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i
                                        class="fa fa-minus"></i>
                                </button>
                            </div>
                        </div>
                        <!-- /.box-header -->
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>模型名称</label>
                                        <span class="">（模型名称，自己认识而已啊）</span>
                                        <input type="text" class="form-control" name="title"  placeholder="请输入模型名称">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>模型标识</label>
                                        <span class="">（模型标识，也是数据表名，自动转换控制器名，唯一性，多个_隔开，格式  ***_***）</span>
                                        <input type="text" class="form-control" name="name"  placeholder="请输入模型标识">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>数据库引擎</label>
                                        <span>（不懂百度,或选第一个，活动，日志表一定选对）</span>
                                        <select name="engine" class="form-control">
                                            <option value="InnoDB" title="Percona-XtraDB, Supports transactions, row-level locking, foreign keys and encryption for tables" selected="selected">
                                                InnoDB
                                            </option>
                                            <option value="MyISAM" title="MyISAM storage engine">
                                                MyISAM
                                            </option>
                                            <option value="CSV" title="CSV storage engine">
                                                CSV
                                            </option>
                                            <option value="MEMORY" title="Hash based, stored in memory, useful for temporary tables">
                                                MEMORY
                                            </option>
                                            <option value="MRG_MyISAM" title="Collection of identical MyISAM tables">
                                                MRG_MyISAM
                                            </option>
                                            <option value="Aria" title="Crash-safe tables with MyISAM heritage">
                                                Aria
                                            </option>
                                            <option value="SEQUENCE" title="Generated tables filled with sequential values">
                                                SEQUENCE
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>模型</label>
                                        <span>（自定义module文件置放文件模快）</span>
                                        <select name="module" class="form-control">
                                            {volist name=':parse_config_array("api_module")' id='vo'}
                                            <option value="{$key}">{$vo}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>控制器</label>
                                        <span>（自定义controller文件置放文件模快）</span>
                                        <select name="controller" class="form-control">
                                            {volist name=':parse_config_array("api_module")' id='vo'}
                                            <option value="{$key}">{$vo}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>model</label>
                                        <span>（自定义model文件置放文件模快）</span>
                                        <select name="model" class="form-control">
                                            {volist name=':parse_config_array("api_module")' id='vo'}
                                            <option value="{$key}">{$vo}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>logic</label>
                                        <span>（自定义logic文件置放文件模块）</span>
                                        <select name="logic" class="form-control">
                                            {volist name=':parse_config_array("api_module")' id='vo'}
                                            <option value="{$key}">{$vo}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>validate</label>
                                        <span>（自定义validate文件置放文件模快）</span>
                                        <select name="validate" class="form-control">
                                            {volist name=':parse_config_array("api_module")' id='vo'}
                                            <option value="{$key}">{$vo}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>描述</label>
                                        <span>（介绍此表单内容）</span>
                                        <textarea name="describes" class="form-control"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>生成后台</label>
                                        <span>（会主动生成后台controller和view和菜单）</span>
                                        <div>
                                            <label class="margin-r-5"><input type="radio" name="is_admin" value="1"> 是</label>
                                            <label><input type="radio" checked="checked" name="is_admin" value="0"> 否</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>上级菜单</label>
                                        <span>（只有选择生成后台才生效）</span>
                                        <select name="pid" class="form-control">
                                            <option value="0">顶级菜单</option>
                                            {volist name='menu_select' id='vo'}
                                            <option value="{$vo.id}">{$vo.name}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                </div>
                                <!-- /.col -->
                            </div>
                            <!-- /.row -->
                        </div>

                        <!-- /.box-footer -->
                    </div>
                    <!-- /.box -->
                </div>
                <!-- /.col -->


                <div class="col-md-12">
                    <div class="box box-danger">
                        <div class="box-header with-border">
                            <h3 class="box-title">字段属性设置</h3>
                        </div>
                        <div class="box-body">

                            <div class="box-body" style="display: block;">
                                <span class="badge" style="background-color: red"><i class="fa fa-exclamation" style="font-size:1.3em"></i></span> 默认自动创建自动增长主键字段 <span class="label label-default">id</span>，创建时间字段 <span class="label label-info">create_time</span>，更新时间字段 <span class="label label-info">update_time</span>，状态字段 <span class="label label-info">status</span>，操作用户字段 <span class="label label-info">member_id</span>
                            </div>


                            <div class="form-inline" style="margin-top: 5px;border-top:1px dashed lightseagreen">
                                <div class="form-group" style="margin-top: 10px;">
                                    <label for="exampleInputName2">字段名称：</label>
                                    <input type="text" name="thead[]" class="form-control" placeholder="字段名称，列表字段名">
                                </div>
                                <div class="form-group" style="margin-top: 6px">
                                    <label for="exampleInputName2"> 字段标识：</label>
                                    <input type="text" name="field[]" class="form-control" placeholder="字段标识，数据库标识，必须英文">
                                </div>
                                <div class="form-group" style="margin-top: 10px">
                                    <label for="exampleInputName2"> 字段类型：</label>
                                    <select name="type[]" class="form-control">
                                        <option value="varchar">字符串</option>
                                        <option value="text">文本框</option>
                                        <option value="int">数字型</option>
                                        <option value="float">浮点型</option>
                                        <option value="file_upload">文件上传</option>
                                        <option value="img_upload">图片上传</option>
                                        <option value="imgs_upload">多图片上传</option>
                                    </select>
                                </div>
                                <div class="form-group" style="margin-top: 10px">
                                    <label for="exampleInputName2"> 长 度：</label>
                                    <input type="text" name="length[]" class="form-control" placeholder="数据库字段长度">
                                </div>
                                <div class="form-group" style="margin-top: 10px">
                                    <label for="exampleInputName2"> 默认值：</label>
                                    <input type="text" name="default[]" class="form-control" placeholder="字段默认值">
                                </div>
                                <div class="form-group" style="margin-top: 10px">
                                    <label for="exampleInputName2">列表显示：</label>
                                    <select name="is_show[]" class="form-control">
                                        <option value="1">显示</option>
                                        <option value="0">不显示</option>
                                    </select>
                                </div>
                                <div class="form-group" style="margin-top: 10px">
                                    <i class="fa fa-remove field_remove" style="color:red;font-size:1.3em"></i>
                                </div>
                                <div class="form-group" style="margin-top: 10px">
                                    <i class="fa fa-plus field_add" style="color: #0CA818;font-size:1.3em"></i>
                                </div>
                            </div>
                        </div>
                        <!-- /.box-body -->
                    </div>
                </div>


            </div>
        </div>
        <div class="box-footer">

            {include file="layout/edit_btn_group"/}

        </div>
    </div>
</form>

<script type="text/javascript">
    $(document).ready(function(){
        $(document).on('click','.field_add',function(){
            $(this).parents(".form-inline").after($(this).parents(".form-inline").prop("outerHTML"));
            $(this).remove();
        });
        $(document).on('click','.field_remove',function(){
            if($(this).parent().next().children('i.field_add').length ==  0) {
                $(this).parents(".form-inline").remove();
            }

        });
    });

</script>