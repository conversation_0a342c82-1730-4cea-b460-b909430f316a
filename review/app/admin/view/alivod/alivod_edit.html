<script src="__STATIC__/module/common/aliyun-upload-sdk-1.5/lib/aliyun-upload-sdk/aliyun-upload-sdk-1.5.0.min.js"></script>
<script src="__STATIC__/module/common/aliyun-upload-sdk-1.5/lib/aliyun-upload-sdk/lib/es6-promise.min.js"></script>
<script src="__STATIC__/module/common/aliyun-upload-sdk-1.5/lib/aliyun-upload-sdk/lib/aliyun-oss-sdk-5.3.1.min.js"></script>
<form action="{:url()}" method="post" class="form_single">
	<div class="box">
		<div class="box-body">
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label>标题</label>
						<span class="">（标题）</span><input class="form-control title_c" name="Title" placeholder="请输入标题"
						                                 value="{$info['Title']|default=''}" type="text">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>简介</label>
						<span class="">（简介）</span><input class="form-control Description" name="Description"
						                                 placeholder="请输入简介"
						                                 value="{$info['Description']|default=''}" type="text">
					</div>
				</div>
				
				<div class="col-md-6">
					<div class="form-group">
						<label>分类</label>
						<span class="">（分类）</span>
						<select name="CateId" class="form-control CateId">-->
							<option value="0">不分类</option>
							{notempty name='Category'}
							{volist name='Category' id='vooc'}
							<option value="{$vooc.CateId}">{$vooc.CateName}</option>
							{/volist}
							
							{/notempty}
						</select>
					</div>
				</div>
				
				<div class="col-md-6">
					<div class="form-group">
						<label>封面图</label>
						<span class="">（视频播放时封面图）</span>{assign name="cover_id"
						value="$info.cover_id|default='0'" /}
						{:widget('file/index', ['name' => 'cover_id', 'value' => $cover_id, 'type' => 'img'])}
					</div>
				</div>
				{notempty name='$info.FileURL'}
				<div class="col-md-6">
					<div class="form-group">
						<input class="form-control" name="VideoId" value="{$info['VideoId']|default=''}"
						       type="hidden">
						<label>媒体地址</label>
						<a target="_blank" href="{$info['FileURL']|default=''}">{$info['FileURL']|default=''}</a>
					</div>
				</div>
				{else/}
				<div class="col-md-6">
					<div class="form-group">
						<label>媒体地址</label>
						
						<a target="_blank" class="media_url_bo"
						   href="{$info['FileURL']|default=''}">{$info['title']|default=''}</a>
						<div class="upload">
							<div>
								<input type="file" id="fileUpload">
								<label class="status">上传状态: <span id="status"></span></label>
							</div>
							<div class="upload-type">
								<button id="authUpload" class="btn btn-default btn-xs" disabled="true">开始上传</button>
								<button id="pauseUpload" class="btn btn-default btn-xs" disabled="true">暂停</button>
								<button id="resumeUpload" class="btn btn-default btn-xs" disabled="true">恢复上传</button>
								<span class="progress">上传进度: <i id="auth-progress">0</i> %</span>
								<span></span>
							</div>
						</div>
					</div>
				</div>
				{/notempty}
			</div>
			{notempty name='$info.FileURL'}
			<div class="box-footer">
				{include file="layout/edit_btn_group"/}
			</div>
			{/notempty}
		</div>
	</div>
</form>

<script type="text/javascript">
    //兼容IE11
    ob.setValue("CateId", {$info['CateId'] |default = 0});
    if (!FileReader.prototype.readAsBinaryString) {
        FileReader.prototype.readAsBinaryString = function (fileData) {
            var binary = "";
            var pt = this;
            var reader = new FileReader();
            reader.onload = function (e) {
                var bytes = new Uint8Array(reader.result);
                var length = bytes.byteLength;
                for (var i = 0; i < length; i++) {
                    binary += String.fromCharCode(bytes[i]);
                }
                //pt.result  - readonly so assign binary
                pt.content = binary;
                pt.onload()
            }
            reader.readAsArrayBuffer(fileData);
        }
    }
    $(document).ready(function () {
        /**
         * 创建一个上传对象
         * 使用 UploadAuth 上传方式
         */
        var uploadPost = {};

        function createUploader() {
            var uploader = new AliyunUpload.Vod({
                timeout: 6000000,
                partSize: 1048576,
                parallel: 5,
                retryCount: 3,
                retryDuration: 2,
                region: 'cn-shanghai',
                userId: '1823400080293321',
                // 添加文件成功
                addFileSuccess: function (uploadInfo) {
                    $('#authUpload').attr('disabled', false)
                    $('#resumeUpload').attr('disabled', false)
                    $('#status').text('添加文件成功, 等待上传...')

                },
                // 开始上传
                onUploadstarted: function (uploadInfo) {
                    if (!uploadInfo.videoId) {
                        createUrl = "{:url('dailyMediaUploadVideo')}?name=" + uploadInfo.file.name;

                        $.ajax({
                            url: "{:url('dailyMediaUploadVideo')}",
                            type: 'POST',
                            cache: false,
                            data: uploadPost,
                            success: function (data) {
                                var up_data = JSON.parse(data);
                                var uploadAuth = up_data.UploadAuth
                                var uploadAddress = up_data.UploadAddress
                                var videoId = up_data.VideoId
                                uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, videoId)
                            }
                        });
                        $('#status').text('文件开始上传...')

                    } else {

                    }
                },

                // 文件上传成功
                onUploadSucceed: function (uploadInfo) {
                    console.log(uploadInfo);
                    $('.media_url_bo').val(uploadInfo.title);
                    $('.media_url_bo').val('https://video.robustcn.com/' + uploadInfo.object);
                    console.log("onUploadSucceed: " + uploadInfo.file.name + ", endpoint:" + uploadInfo.endpoint + ", bucket:" + uploadInfo.bucket + ", object:" + uploadInfo.object)
                    $('#status').text('文件上传成功!')
                },
                // 文件上传失败
                onUploadFailed: function (uploadInfo, code, message) {
                    console.log("onUploadFailed: file:" + uploadInfo.file.name + ",code:" + code + ", message:" + message)
                    $('#status').text('文件上传失败!')
                },
                // 取消文件上传
                onUploadCanceled: function (uploadInfo, code, message) {
                    console.log("Canceled file: " + uploadInfo.file.name + ", code: " + code + ", message:" + message)
                    $('#status').text('文件上传已暂停!')
                },
                // 文件上传进度，单位：字节, 可以在这个函数中拿到上传进度并显示在页面上
                onUploadProgress: function (uploadInfo, totalSize, progress) {
                    var progressPercent = Math.ceil(progress * 100)
                    $('#auth-progress').text(progressPercent)
                    $('#status').text('文件上传中...')
                },
                // 上传凭证超时
                onUploadTokenExpired: function (uploadInfo) {
                    // 上传大文件超时, 如果是上传方式一即根据 UploadAuth 上传时
                    // 需要根据 uploadInfo.videoId 调用刷新视频上传凭证接口(https://help.aliyun.com/document_detail/55408.html)重新获取 UploadAuth
                    // 然后调用 resumeUploadWithAuth 方法, 这里是测试接口, 所以我直接获取了 UploadAuth
                    $('#status').text('文件上传超时!')
                },
                // 全部文件上传结束
                onUploadEnd: function (uploadInfo) {
                    $('#status').text('文件上传完毕!')
	                alert('上传完成，可以在列表页刷新');
                }
            })


            return uploader
        }

        var uploader = null

        $('#fileUpload').on('change', function (e) {
            var file = e.target.files[0]
            uploadPost.Title = $('.title_c').val();
            uploadPost.Description = $('.Description').val();
            uploadPost.CateId = $('.CateId').val();
            uploadPost.FileName = file.name;
            if ($('.upload-pre-item')) {
                uploadPost.CoverURL = $('.upload-pre-item').children('a').attr('href');
            }
            console.log(uploadPost);
            if (!file) {
                alert("请先选择需要上传的文件!")
                return false;
            }
            ;
            if (!uploadPost.Title) {
                alert("请把标题填写好")
                return false;
            }
            ;
            var userData = '{"Vod":{}}';
            if (uploader) {
                uploader.stopUpload()
                $('#auth-progress').text('0')
                $('#status').text("")
            }
            uploader = createUploader()
            // 首先调用 uploader.addFile(event.target.files[i], null, null, null, userData)
            console.log(uploader)
            uploader.addFile(file, null, null, null, userData)
            $('#authUpload').attr('disabled', false)
            $('#pauseUpload').attr('disabled', true)
            $('#resumeUpload').attr('disabled', true)
            uploader.listFiles();
           
        })

        // 第一种方式 UploadAuth 上传
        $('#authUpload').on('click', function () {
            // 然后调用 startUpload 方法, 开始上传
            if (uploader !== null) {
                uploader.startUpload()
                $('#authUpload').attr('disabled', true)
                $('#pauseUpload').attr('disabled', false)
            }
        })

        // 暂停上传
        $('#pauseUpload').on('click', function () {
            if (uploader !== null) {
                uploader.stopUpload()
                $('#resumeUpload').attr('disabled', false)
                $('#pauseUpload').attr('disabled', true)
            }
        })

        //恢复上传
        $('#resumeUpload').on('click', function () {
            if (uploader !== null) {
                uploader.startUpload()
                $('#resumeUpload').attr('disabled', true)
                $('#pauseUpload').attr('disabled', false)
            }
        })

    })
</script>