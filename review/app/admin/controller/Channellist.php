<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

/**
 * 渠道列表控制器
 */
class Channellist extends AdminBase
{

    /**
     * 渠道列表列表
     */
    public function channelListList()
    {

        $where = $this->logicChannelList->getAdminWhere($this->param);

        return $this->fetch('channelList_list');
    }

    /**
     * 渠道列表添加
     */
    public function channelListAdd()
    {
        IS_POST && $this->jump($this->logicChannelList->channelListAdminEdit($this->param));
        return $this->fetch('channelList_edit');
    }

    /**
     * 渠道列表编辑
     */
    public function channelListEdit()
    {
        IS_POST && $this->jump($this->logicChannelList->channelListAdminEdit($this->param));

        $info = $this->logicChannelList->getChannelListInfo(['id' => $this->param['id']], '*');

        $this->assign('info', $info);

        return $this->fetch('channelList_edit');
    }


    /**
     * 渠道列表删除
     */
    public function channelListDel($id = 0)
    {

        $this->jump($this->logicChannelList->channelListDel(['id' => $id]));
    }
}
