$(function () {
    /**
     * 获取ThinkTree基础配置
     * @type {object}
     */
    var ob = window.ob = {};
    var confirm_var = '确认要执行该操作吗?';
    /* 基础对象检测 */
    ob || $.error("ThinkTree基础配置没有正确加载！");

    /* 通过name设置元素的值 */
    ob.setValue = function (name, value) {

        var first = name.substr(0, 1), input, i = 0, val;

        if (value === "") return;

        if ("#" === first || "." === first) {

            input = $(name);
        } else {

            input = $("[name='" + name + "']");
        }

        if (input.eq(0).is(":radio")) { //单选按钮

            input.filter("[value='" + value + "']").each(function () {
                this.checked = true;
            });
        } else if (input.eq(0).is(":checkbox")) { //复选框

            if (!$.isArray(value)) {

                val = new Array();
                val[0] = value;
            } else {

                val = value;
            }
            for (i = 0, len = val.length; i < len; i++) {

                input.filter("[value='" + val[i] + "']").each(function () {
                    this.checked = true
                });
            }
        } else {  //其他表单选项直接设置值

            input.val(value);
        }
    };

    /* 前端存储 */
    ob.store = function (name, val) {

        if (typeof (Storage) !== "undefined") {

            if ("undefined" !== typeof (val)) {

                localStorage.setItem(name, val);
            } else {

                return localStorage.getItem(name);
            }
        } else {

            window.alert('store error!');
        }
    };
    //刷新验证码
    $(".captcha_change").click(function () {

        var captcha_img_obj = $("#captcha_img");

        captcha_img_obj.attr("src", captcha_img_obj.attr("src") + "?" + Math.random());
    });


    //清理缓存
    $(".clear_cache").click(function () {

        $.post($(this).attr('url'), {}, success, "json");
        return false;

        function success(data) {
            obalert(data);
        }
    });


    //批量处理
    $('.batch_btn').click(function () {

        var $checked = $('.table input[type="checkbox"]:checked');

        if ($checked.length != 0) {
            if (confirm('您确认批量操作吗？')) {

                $.post($(this).attr("href"), {
                    ids: $checked.serializeArray(),
                    status: $(this).attr("value")
                }, function (data) {

                    obalert(data);

                }, "json");
            }
        } else {

            toast.warning('请选择批量操作数据');
        }
        return false;
    });

   //搜索功能
    $("#search").click(function(){
        if ($(this).hasClass('confirm')) {

            if($(this).attr("confirm")){
                confirm_var=$(this).attr("confirm");
            }

            if (!confirm(confirm_var)) {

                return false;
            }
        }

        window.location.href = searchFormUrl(this);
    });

    //回车搜索
    $(".search-input").keyup(function(e){
        if(e.keyCode === 13){
                $("#search").click();
                return false;
        }
    });
    //ajax get请求
    $(document).on('click', '.ajax-get', function () {

        var target;
        var beforeStatus = false;

        if ($(this).hasClass('beforeStatus')) {

            beforeStatus = true;
        }

        if ($(this).hasClass('confirm')) {

            if($(this).attr("confirm")){
                confirm_var=$(this).attr("confirm");
            }

            if (!confirm(confirm_var)) {

                return false;
            }
        }

        if ((target = $(this).attr('href')) || (target = $(this).attr('url'))) {

            if ($(this).attr('is-jump') == 'true') {

                location.href = target;

            } else {
                $(this).removeAttr('href');//去掉a标签中的href属性


                $.ajax({
                    type: "GET",
                    url: target,
                    dataType: "json",
                    //请求发送之前
                    beforeSend: function () {

                        if(beforeStatus){
                            obalert({"code":999999,"msg":"太过于火爆，正在排队中请耐心等待^","data":"" ,"wait":3000});
                        }

                        // obalert({"code":999999,"msg":"太过于火爆，正在排队中请耐心等待^","data":"" ,"wait":3000});
                    },
                    success: function (data) {
                        obalert(data);
                    }
                });
            }
        }

        return false;
    });

    //ajax post submit请求
    $('.ajax-post').click(function () {

        var target, query, form;

        var target_form = $(this).attr('target-form');

        var that = this;

        var nead_confirm = false;

        if (($(this).attr('type') == 'submit') || (target = $(this).attr('href')) || (target = $(this).attr('url'))) {

            form = $('.' + target_form);

            if ($(this).attr('hide-data') === 'true') {//无数据时也可以使用的功能
                form = $('.hide-data');
                query = form.serialize();
            } else if (form.get(0) == undefined) {
                return false;
            } else if (form.get(0).nodeName == 'FORM') {

                if ($(this).hasClass('confirm')) {

                    if (!confirm('确认要执行该操作吗?')) {

                        return false;
                    }
                }
                if ($(this).attr('url') !== undefined) {
                    target = $(this).attr('url');
                } else {
                    target = form.get(0).action;
                }
                query = form.serialize();
            } else if (form.get(0).nodeName == 'INPUT' || form.get(0).nodeName == 'SELECT' || form.get(0).nodeName == 'TEXTAREA') {

                form.each(function (k, v) {
                    if (v.type == 'checkbox' && v.checked == true) {
                        nead_confirm = true;
                    }
                })

                if (nead_confirm && $(this).hasClass('confirm')) {
                    if (!confirm('确认要执行该操作吗?')) {
                        return false;
                    }
                }

                query = form.serialize();
            } else {

                if ($(this).hasClass('confirm')) {
                    if (!confirm('确认要执行该操作吗?')) {
                        return false;
                    }
                }
                query = form.find('input,select,textarea').serialize();
            }


            var is_ladda_button = $(that).hasClass('ladda-button');

            is_ladda_button && $(that).addClass('disabled').attr('autocomplete', 'off').attr('disabled', true);

            $.ajax({
                url: target,
                type: "post",
                data: query,
                dataType: "json",
                success: function (data) {
                    obalert(data);
                    is_ladda_button && $(that).removeClass('disabled').attr('disabled', false);
                }
            })
        }
        return false;
    });

});

/**
 * 提示或提示并跳转
 */
var obalert = function (data) {
    var wait = 1000;
    if (data.code) {
         wait = data.wait*300;
    }
    if (data.code) {
        if (data.code == 999999) {
            wait = 999999;
            toast.info(data.msg);
        } else {
            toast.success(data.msg);
        }

    } else {

        if (typeof data.msg == "string") {

            toast.error(data.msg);
        } else {

            var err_msg = '';

            for (var item in data.msg) {
                err_msg += "Θ " + data.msg[item] + "<br/>";
            }

            toast.error(err_msg);
        }
    }

    if (data.url) {

        setTimeout(function () {
           location.href = data.url;
        }, wait);
    }

    if (data.code==0 && !data.url) {
        setTimeout(function () {
             location.reload();
        }, wait);
    }
};


/**
 * 操纵toastor的便捷类
 * @type {{success: success, error: error, info: info, warning: warning}}
 */
var toast = {
    /**
     * 成功提示
     * @param text 内容
     * @param title 标题
     */
    success: function (text, title) {

        $(".toast").remove();

        toastr.options = {
            "closeButton": true,
            "debug": false,
            "positionClass": "toast-top-center",
            "onclick": null,
            "showDuration": "1000",
            "hideDuration": "2000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
        toastr.success(text, title);
    },
    /**
     * 失败提示
     * @param text 内容
     * @param title 标题
     */
    error: function (text, title) {

        $(".toast").remove();

        toastr.options = {
            "closeButton": true,
            "debug": false,
            "positionClass": "toast-top-center",
            "onclick": null,
            "showDuration": "1000",
            "hideDuration": "4000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
        toastr.error(text, title);
    },
    /**
     * 信息提示
     * @param text 内容
     * @param title 标题
     */
    info: function (text, title) {

        $(".toast").remove();

        toastr.options = {
            "closeButton": true,
            "debug": false,
            "positionClass": "toast-top-center",
            "onclick": null,
            "showDuration": "1000",
            "hideDuration": "2000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
        toastr.info(text, title);
    },
    /**
     * 警告提示
     * @param text 内容
     * @param title 标题
     */
    warning: function (text, title) {

        $(".toast").remove();

        toastr.options = {
            "closeButton": true,
            "debug": false,
            "positionClass": "toast-top-center",
            "onclick": null,
            "showDuration": "1000",
            "hideDuration": "2000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
        toastr.warning(text, title);
    }
};

/**
 * 搜索表单url
 */
var searchFormUrl = function (obj) {

    var url = $(obj).attr('url');
    var query = $('.search-form').find('input,select').serialize();
    query = query.replace(/(&|^)(\w*?\d*?\-*?_*?)*?=?((?=&)|(?=$))/g, '');
    query = query.replace(/^&/g, '');
    if (url.indexOf('?') > 0) {
        url += '&' + query;
    } else {
        url += '?' + query;
    }

    return url;
};

/**
 * 获取url指定参数
 * @param name
 * @returns {*}
 * @constructor
 */
function GetQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);//search,查询？后面的参数，并匹配正则
    if (r != null) return unescape(r[2]);
    return null;
}

/**
 * 异步获取图片
 */
var img_async_even = function () {
    var img_async = [];
    var img_async_list = [];
    $(".img_anyes").each(function () {
        img_async.push($(this).attr('imgId'));
    });
    if (img_async.length > 0) {
        $.ajax({
            type: "post",
            url: "/gxpmzg.php/Common/0",
            data: {
                imgs: img_async
            },
            async: false,
            success: function (msg) {
                img_async_list = msg;
                $(".img_anyes").each(function () {
                    if (img_async_list[$(this).attr('imgId')]) {
                        $(this).attr('src', img_async_list[$(this).attr('imgId')])
                    }
                });
            }
        })
    }
}
/**
 * 异步获取各大逻辑层数据数据
 */
var logic_async_even = function () {
    var logic_async = [];
    var logic_file = [];
    var logic_index = '';
    $(".logic_anyes").each(function () {
        if (!logic_async[$(this).attr('logicName')]) {
            logic_async[$(this).attr('logicName')] = [];
        }
        (logic_file[$(this).attr('logicName')] || (logic_file[$(this).attr('logicName')] = [])).push($(this).attr('logicFile'));
        logic_index = $(this).attr('logicIndex');
        logic_async[$(this).attr('logicName')].push($(this).attr('logicId'));
    });

    for (x in logic_async) {
        var logicAj = $("." + x);
        var logicName = x;
        var logic_async_list;
        if (logic_async[x].length > 0 && logicName != '' && logic_file[x].length > 0) {
            $.ajax({
                type: "post",
                url: "/gxpmzg.php/Common/getLogicColumn",
                data: {
                    index: logic_index,
                    ids: logic_async[x],
                    file: logic_file[x],
                    logicName: logicName
                },
                async: false,
                success: function (msg) {
                    logic_async_list = msg;
                    logicAj.each(function () {
                        if ($(this).attr('logicId') == '' || $(this).attr('logicId') == null) {
                            if (logic_async_list[$(this).attr('logicId')][$(this).attr('logicFile')]) {
                                $(this).text(logic_async_list[$(this).attr('logicId')][$(this).attr('logicFile')]);
                            } else {
                                $(this).text('无');
                            }
                        } else {
                            if (logic_async_list[$(this).attr('logicId')][$(this).attr('logicFile')]) {
                                $(this).text(logic_async_list[$(this).attr('logicId')][$(this).attr('logicFile')]);
                            } else {
                                $(this).text('无');
                            }
                        }
                    });
                }
            })
        }
    }
}
