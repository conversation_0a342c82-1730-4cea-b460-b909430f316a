<body class="hold-transition skin-blue sidebar-mini">
{include file="../app/common/view/fakeloader.html" /}
<script src="__STATIC__/module/admin/js/init_body.js"></script>
<div class="wrapper">
  <header class="main-header">
    <!-- Logo -->
    <a href="{:url('Index/index')}" class="logo">
      <!-- mini logo for sidebar mini 50x50 pixels -->
      <span class="logo-mini"><b>罗博</b></span>
      <!-- logo for regular state and mobile devices -->
      <span class="logo-lg"><b>管理后台</b></span>
    </a>
    <!-- Header Navbar: style can be found in header.less -->
    <nav class="navbar navbar-static-top">
      <!-- Sidebar toggle button-->
      <a href="javascript:;" class="sidebar-toggle" data-toggle="offcanvas" role="button">
        <span class="sr-only">导航开关</span>
      </a>
      
        {if condition="!$Think.const.IS_MOBILE"}
            {volist name='auth_menu_list' id='vo'}
                {eq name='vo.is_shortcut' value='1'}
                    <a href="{:url($vo['url'])}" class="sidebar-btn">
                      {notempty name="vo['icon']"}<i class="fa {$vo['icon']}"></i>{/notempty} {$vo['name']}
                    </a>
                {/eq}
            {/volist}
        {/if}

      <div class="navbar-custom-menu">
        <ul class="nav navbar-nav">
<!--          <li>
            <a href="{:url('chat/index')}">
              <i class="fa fa-comments"></i>
              <span class="label label-danger new_message_num"></span>
            </a>
          </li>-->
          <!-- Notifications: style can be found in dropdown.less -->
          <li class="dropdown notifications-menu">
<!--            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <i class="fa fa-bell-o"></i>
              <span class="label label-warning">10</span>
            </a>-->
            <ul class="dropdown-menu">
              <li class="header">您有10个通知</li>
              <li>
                <!-- inner menu: contains the actual data -->
                <ul class="menu">
                  <li>
                    <a href="#">
                      <i class="fa fa-users text-aqua"></i> 今天有5个新成员加入
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-warning text-yellow"></i> 这是一条系统警告通知。
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-shopping-cart text-green"></i> 销售了25个产品喔
                    </a>
                  </li>
                  <li>
                    <a href="#">
                      <i class="fa fa-user text-red"></i> 用户名修改通知
                    </a>
                  </li>
                </ul>
              </li>
              <li class="footer"><a href="#">查看所有通知</a></li>
            </ul>
          </li>
          <!-- User Account: style can be found in dropdown.less -->
          <li class="dropdown user user-menu">
            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
              <img src="{$member_info['head_img_id']|get_head_picture_url}" class="user-image" alt="ThinkTree">
              <span class="hidden-xs">{$member_info['nickname']}</span>
            </a>
            <ul class="dropdown-menu">
              <!-- User image -->
              <li class="user-header">
                <img src="{$member_info['head_img_id']|get_head_picture_url}" class="img-circle" alt="ThinkTree">

                <p>
                    {$member_info['nickname']}
                  <small>上次登录时间：{$member_info['update_time']}</small>
                </p>
              </li>
              <!-- Menu Footer-->
              <li class="user-footer">
                <div class="pull-left">
                  <a href="javascript:;" class="btn clear_cache" url="{:url('login/clearCache')}">清理缓存</a>
                  <a href="{:url('member/editPassword')}" class="btn">修改密码</a>
                  <a href="javascript:;" class="btn logout" url="{:url('login/logout')}">安全退出</a>
                </div>
              </li>
            </ul>
          </li>
          
          <!-- 控制栏切换按钮 -->
          <li>
            <a href="#" data-toggle="control-sidebar"><i class="fa fa-gears"></i></a>
          </li>
        </ul>
      </div>
    </nav>
  </header>
