<div class="box">
	<div class="box-header">
		<ob_link><a class="btn btn-primary" href="{:url('SwiperAdd')}"><i class="fa fa-plus"></i> 新 增</a></ob_link>
		
		<div class="box-tools">
			<div class="input-group input-group-sm search-form">
				<input name="search_data" class="pull-right search-input" value="{:input('search_data')}"
				       placeholder="支持标题|描述" type="text">
				<div class="input-group-btn">
					<button type="button" id="search" url="{:url('SwiperList')}" class="btn btn-default"><i
							class="fa fa-search"></i></button>
				</div>
			</div>
		</div>
		<br/>
	</div>
	<div class="box-body table-responsive">
		<table class="table table-bordered table-hover">
			<thead>
			<tr>
				<th>标题</th>
				<th>排序</th>
				<th>封面</th>
				<th>发布时间</th>
				<th>操作</th>
			</tr>
			</thead>
			
			{notempty name='list'}
			<tbody>
			{volist name='list' id='vo'}
			<tr>
				<td>{empty name="vo.url"}
					{$vo.title}
					{else /}
					<a target="_blank" href="{$vo.url}">{$vo.title}</a>
					{/empty}</td>
                <td><input type="text" class="sort-th sort-text" href="{:url('setSort')}" id="{$vo.id}" value="{$vo.sort}" /></td>
                <td>
					<img class="admin-list-img-size" src="{$vo.cover_id|get_picture_url}"/>
				</td>
				<td>{$vo.create_time}</td>
				<td class="col-md-2 text-center">
					<ob_link><a href="{:url('swiperEdit', array('id' => $vo['id']))}" class="btn  btn-xs"><i
							class="fa fa-edit"></i> 编辑</a></ob_link>
					<ob_link><a class="btn  confirm ajax-get  btn-xs"
					            href="{:url('swiperDel', array('id' => $vo['id']))}"><i class="fa fa-trash-o"></i>
						删除</a></ob_link>
				</td>
			</tr>
			{/volist}
			</tbody>
			{else/}
			<tbody>
			<tr class="odd">
				<td colspan="7" class="text-center" valign="top">{:config('empty_list_describe')}</td>
			</tr>
			</tbody>
			{/notempty}
		</table>
	</div>
	<div class="box-footer clearfix text-center">
		{$list->render()}
	</div>
</div>