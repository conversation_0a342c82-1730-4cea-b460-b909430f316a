<div class="box">
  <div class="box-header">
    <ob_link><a class="btn confirm ajax-get" href="{:url('trashDataDel', array('model_name' => $model_name))}"><i class="fa fa-close"></i> 全部删除</a></ob_link>
    <ob_link><a class="btn confirm ajax-get" href="{:url('restoreData', array('model_name' => $model_name))}"><i class="fa fa-reply"></i> 全部恢复</a></ob_link>
  </div>
  <div class="box-body table-responsive">
    <table  class="table table-bordered table-hover table-striped">
      <thead>
      <tr>
          <th>ID</th>
          <th>名称</th>
          <th>创建时间</th>
          <th>删除时间</th>
          <th>操作</th>
      </tr>
      </thead>
      
      {notempty name='list'}
        <tbody>
            {volist name='list' id='vo'}
                <tr>
                  <td>{$vo.id}</td>
                  <td>{$vo[$dynamic_field]}</td>
                  <td>{$vo.create_time}</td>
                  <td>{$vo.update_time}</td>
                  <td class="col-md-2 text-center">
                      <ob_link><a href="{:url('trashDataDel', array('model_name' => $model_name, 'id' => $vo['id']))}" class="btn confirm ajax-get"><i class="fa fa-close"></i> 删除</a></ob_link>
                      &nbsp;
                      <ob_link><a href="{:url('restoreData', array('model_name' => $model_name, 'id' => $vo['id']))}" class="btn confirm ajax-get"><i class="fa fa-reply"></i> 恢复</a></ob_link>
                  </td>
                </tr>
            {/volist}
        </tbody>
        {else/}
        <tbody><tr class="odd"><td colspan="5" class="text-center" valign="top">{:config('empty_list_describe')}</td></tr></tbody>
      {/notempty}
    </table>
  </div>
  <div class="box-footer clearfix text-center">
      {$list->render()}
  </div>
</div>