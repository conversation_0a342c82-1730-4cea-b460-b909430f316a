<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>
		O·POWER 文化艺术中心
	</title>
	<meta name="keywords" content="深圳,华侨城,O·POWER,标识设计,序东堂">
	<meta name="description" content="序东堂受华侨城委托参与到O·POWER文化艺术中心城市更新项目之中，2022年项目得以呈现。我们以老旧管道器械的翻新组接，讲述文明代代延续传递的核心理念。">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="cache-control" content="max-age=0">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">
<meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT">
<meta http-equiv="pragma" content="no-cache">
<link href="../static/image/favicon_64.ico" rel="shortcut icon" type="image/x-icon">
<link rel="stylesheet" type="text/css" href="../../static/css/common.css">
<link rel="stylesheet" type="text/css" href="../../static/css/style.css">
<link rel="stylesheet" type="text/css" href="../../static/css/responsive.css">
<script src="../../static/js/jquery.min.js"></script>
<script src="../../static/js/setrem.js"></script>
<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?b24ee3c38abc23c7ebfeac6a860b2b22";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>
</head>

<body>
	<header class="top">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			

		</ul>
	</nav>
</header>
<section class="top-fixed">
	<nav class="navbox">
		<ul class="nav">
			
				<li>
					<a class="cursorA" href="../../info/info.html">Info</a>
				</li>
			
				<li>
					<a class="cursorA" href="../works.html">Works</a>
				</li>
			
				<li>
					<a class="cursorA" href="../../news/news.html">News</a>
				</li>
			
		</ul>
	</nav>
</section>
	<section class="top-logo section-gx">
		<ul class="tlogo-nav-ul">
			<li class="item1">
				<h1 class="logo">
					<a title="序东堂设计官网" class="cursorA" href="../../index.html">
						<img src="../../static/image/logo.png" class="vm" alt="序东堂设计官网">
						</a>
				</h1>
			</li>
		</ul>
	</section>
	<section class="section-gx view-info">
		<div class="view-infoLR">
			<article class="view-infoL">
				<div class="view-ititle lc-h1 target tY50">
					华侨城<br>O·Power文化艺术中心<br>环境图形 & 品牌设计<br>·<br>O·POWER<br>Urban Renewal
				</div>
			</article>
			<article class="view-infoR">
				<dl class="view-infoR-desc">
					<dd class="target tY50 dly1">
						<div class="view-infoR-item pageC lc-h2 pf-re">
							O·POWER文化艺术中心位于中国深圳，由原华中电厂改建而来。上世纪80年代末，为满足深圳特区快速发展的供电需求，华中电厂应运而生。建成后每天发电80万度，为整个华侨城片区供电，包括康佳集团、欢乐谷、世界之窗、锦绣中华等，2006年随着特区供电水平上升而关停。2021年5月，序东堂受深圳华侨城委托进行VI及标识系统设计，2022年8月完成交付。<br><br>我们采用原厂地丰富的管道形象，表达“传递能量”的意象，一方面是以电能为代表的物质能量，另一方面是在城市发展的时代背景下所涌现的精神能量。以老旧管道器械的翻新组接，讲述文明代代延续传递的核心理念。
						</div>
					</dd>
					<dd class="target tY50 dly2">
						<ul class="lc-h2 work-infoR-type" id="CaseTag">

						</ul>
					</dd>
				</dl>
				<section class="pageC lc-h2 viewPage target tY50" id="worksimgs">

					
					 
				</section>
				<article class="pager">
					<p>
						<a title="华西坝 · HUAXIBA" href="126.html">Prev</a> /
						<a title="仓前九里 · CANGQIANJIULI" href="125.html">Next</a>
					</p>
					<p>
						<a class="blaclk" href="../UrbanRenewal.html">Back to List</a>
					</p>
				</article>
			</article>
		</div>
	</section>
	<footer class="foot section-gx">
	<ul class="foot-nav">
		<li>
			<article class="foot-l">
				© All rights Reserved by xdtart
			</article>
		</li>
		<li>
			<article class="foot-contact">
				<div class="item">
					<p>
					   Mail：<br><EMAIL><br><EMAIL>
					</p>				 
			  </div>
				<div class="item">
				 <p>
					   Tel:<br>18607110100
					</p>
				</div>
				<div class="item">
					 <p>
					   Address：<br>武汉市东西湖区团结激光工业园7栋C座5楼
					</p>
				</div>
			</article>
		</li>
		<li>
			<article class="foot-rbox">
				<p class="foot-erm">
					<img src="../../static/picture/b7ebf8e41850af4e.svg" class="imgw">
                    </p>
					<p><a href="https://beian.miit.gov.cn/" target="_blank">鄂ICP备2024076981号-1</a></p>
			</article>
		</li>
	</ul>
</footer>
<script src="../../static/js/plugin.js"></script>
<script src="../../static/js/page.js"></script>

	<script>
		var CaseTag="Address | 深圳市南山区华侨城香山东街19号".replaceAll("<br />","^").split("^");
		var Awards="2023 | 日本标识设计奖 · 铜奖<br />2022 | Award360°  · 最佳环境图形设计奖".replaceAll("<br />","^").split("^");
		var CaseTagstr="";
		if(CaseTag.length==0)
		 {
			 if(CaseTag.split("|").length>1)
		     {
				  CaseTagstr+="<li>";
                  CaseTagstr+="<p class=\"t\">"+CaseTag.split("|")[0].trim() +"</p><div class=\"c pf-re\">"+CaseTag.split("}")[1].trim() +"</div>";
				  CaseTagstr+="</li>";
			  }
		  }
		 else
		 {                                
		 for (var arr=0;arr<CaseTag.length;arr++) {
		   CaseTagstr+="<li>";
		   if(CaseTag[arr].split("|").length>1)
			{
                CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].split("|")[0].trim()+"</p>"+"<div class=\"c pf-re\">"+CaseTag[arr].split("|")[1].trim()+"</div>";
			}
		   else
			{
				CaseTagstr+="<p class=\"t\">"+ CaseTag[arr].trim()+"</p>"+"<div class=\"c pf-re\"></div>";
			}
			 CaseTagstr+="</li>";
            }
          }
          if(Awards!="")
		  {
            CaseTagstr+="<li>";
            if(Awards.length==0)
		    {
			 if(Awards.split("|").length>1)
		     {
				CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c pf-re\"><dl class=\"awards-list pf-re\"><dt>"+Awards.split("|")[0].trim()+"</dt><dd>"+Awards.split("|")[1].trim()+"</dd></dl></div>";	  
			 }
		   }
		   else{
			
			CaseTagstr+="<p class=\"t\">Awards</p><div class=\"c\">";
            for (var arr=0;arr<Awards.length;arr++) {
            CaseTagstr+="<dl class=\"awards-list pf-re\">";
			
		    if(Awards[arr].split("|").length>1)
			{				 
               CaseTagstr+="<dt>"+Awards[arr].split("|")[0].trim()+"</dt><dd>"+Awards[arr].split("|")[1].trim()+"</dd>";
			}
		    else
			{
				CaseTagstr+="<dt>"+Awards[arr].trim()+"</dt><dd></dd>";
			}
			CaseTagstr+="</dl>";
		    }	
		    }
            CaseTagstr+="</li>";
		  }
		  document.getElementById("CaseTag").innerHTML=CaseTagstr;

		 const htmlString='<img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/01.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/02.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/03.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/04.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/05.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/06.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/07.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/08.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/09.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/10.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban05/11.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban05/12.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/13.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/14.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/15.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/16.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/17.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/18.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/19.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban05/20.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban05/21.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/22.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/23.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/24.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/25.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban05/26.jpg" /><img type="LargeUrl" tag="小图" src="/images/works/UrbanRenewal/Urban05/27.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/28.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/29.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/30.jpg" /><img type="LargeUrl" tag="" src="/images/works/UrbanRenewal/Urban05/31.jpg" />';
         const parser = new DOMParser();
         const doc = parser.parseFromString(htmlString, 'text/html');
         const imgTags = doc.getElementsByTagName('img');
         let imgAttributes="";
        for (let i = 0; i < imgTags.length; i++) {
        if(imgTags[i].getAttribute("tag")=="小图")
         {
         imgAttributes+="<div class=\"imgwitem-w\">";
        
        imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"O·POWER 文化艺术中心\"/></p></div>";
        i++;

		if(i < imgTags.length)
         { 
			 if(imgTags[i].getAttribute("tag")=="小图")
			   imgAttributes+="<div class=\"item\"><p class=\"img-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"O·POWER 文化艺术中心\" /></p></div></div>";
			 else 
			   imgAttributes+="</div><p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"O·POWER 文化艺术中心\" /></p>";
		 }	 
        }
       else
       {
          imgAttributes+="<p class=\"imgw-w\"><img src=\""+imgTags[i].getAttribute("src")+"\" class=\"imgw\" alt=\"O·POWER 文化艺术中心\" /></p>";
       }
     }
	document.getElementById("worksimgs").innerHTML=imgAttributes;
	</script>
</body>

</html>