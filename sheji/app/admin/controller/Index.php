<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Licensed   | http://www.apache.org/licenses/LICENSE-2.0 )           |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                               |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/tarscoding/wecong_tp                      |
// +---------------------------------------------------------------------+

namespace app\admin\controller;

use exwechat\api\custom\custom;
use think\Cache;
/**
 * 首页控制器
 */
class Index extends AdminBase
{

    /**
     * 首页方法
     */
    public function index()
    {


        $category_where['status'] = 1;

        $count['articleCategory'] = 0;

        $article_where['status'] = 1;

        $count['article'] = 0;

        $count['view'] = 0;

        $count['limit_count'] =0;
        $count['sh_count'] =0;




        $this->assign('count', $count);

        $end_time=time();
        $start_time = strtotime(date('Y-m-d', $end_time));

        if(!empty($this->param['start_time'])){
            $start_time = strtotime(date('Y-m-d', strtotime($this->param['start_time'])));
            $end_time=$start_time+86400;
        }

        $day_sum_column=array();

        $day_sum=array();

        for ($i=0;$i<24;$i++){
            $day_num=date('H',$start_time+$i*3600);
            if(empty($day_sum_column[$start_time+$i*3600])){
                $day_sum[ceil($day_num)]=0;
            }else{
                $day_sum[ceil($day_num)]=$day_sum_column[$start_time+$i*3600]["count"];
            }
        }
        $this->assign('day_sum', ['time'=>json_encode(array_keys($day_sum)),'count'=>json_encode(array_values($day_sum))]);

        return $this->fetch('index');
    }



}
