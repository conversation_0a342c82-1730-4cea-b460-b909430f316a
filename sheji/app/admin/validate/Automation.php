<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\admin\validate;

/**
 * API验证器
 */
class Automation extends AdminBase
{
    
    // 验证规则
    protected $rule =   [
        'title'          => 'require',
        'name'          => 'require|unique:automation',
    ];

    // 验证提示
    protected $message  =   [
        'title.require'         => '模型名称不能为空',
        'name.require'         => '当前创建表名不能为空',
        'name.unique'          => '当前创建表名已经存在',
    ];
    
    // 应用场景
    protected $scene = [
        'add'  =>  ['title','name','field'],
    ];

}
