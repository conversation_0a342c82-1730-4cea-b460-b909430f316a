<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 标签逻辑
 */
class Label extends LogicBase
{

    /**
     * 获取标签搜索条件
     */
    public function getWhere($data = [])
    {

        $where = [];

        isset($data['status']) && $where['status'] = $data['status'];

        !empty($data['search_data']) && $where['title'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 获标签单条信息
     */
    public function getLabelInfo($where = [], $field = '*')
    {

        return $this->modelLabel->getInfo($where, $field);
    }

    /**
     * 获取标签列表-分页
     */

    public function getLabelList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelLabel->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取标签列表-所有
     */

    public function getLabelAllList($where = [], $field = '', $order = '', $paginate = false)
    {
        return $this->modelLabel->getList($where, $field, $order, $paginate);
    }


    /**
     * 获取标签无分页列表
     */
    public function getLabelColumn($where = [], $field = '', $key = '')
    {
        return $this->modelLabel->getColumn($where, $field, $key);
    }

    /**
     * 标签单条编辑
     */
    public function labelEdit($data = [])
    {

        $result = $this->modelLabel->setInfo($data);

        return $result ? $result : $this->modelLabel->getError();
    }

    /**
     * 标签删除
     */
    public function labelDel($where = [], $is_true = false)
    {

        $result = $this->modelLabel->deleteInfo($where, $is_true);

        return $result ? $result : $this->modelLabel->getError();
    }


    //Admin模块操作

    /**
     * 获取标签搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['title'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 标签单条编辑
     */
    public function labelAdminEdit($data = [])
    {
        $validate_result = $this->validateLabel->scene('add')->check($data);

        if (!$validate_result) {

            return [RESULT_ERROR, $this->validateLabel->getError()];
        }

        $url = url('labelList');

        $data['member_id'] = MEMBER_ID;

        $result = $this->modelLabel->setInfo($data);

        $handle_text = empty($data['id']) ? '新增' : '编辑';

        $result && action_log($handle_text, '标签' . $handle_text . '，data：' . http_build_query($data));

        return $result ? [RESULT_SUCCESS, '标签操作成功', $url] : [RESULT_ERROR, $this->modelLabel->getError()];
    }

    /**
     * 标签删除
     */
    public function labelAdminDel($where = [])
    {

        $result = $this->modelLabel->deleteInfo($where);

        $result && action_log('删除', '标签删除，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '标签删除成功'] : [RESULT_ERROR, $this->modelLabel->getError()];
    }
}
