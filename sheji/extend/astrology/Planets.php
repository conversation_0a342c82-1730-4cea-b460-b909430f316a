<?php

namespace astrology;


class Planets
{
    public $sweph = './sweph/';

    public function SweTest($conditions, $starsCode)
    {
        $query = $this->sweph . "swetest -edir" . $this->sweph . ' ';

        if (is_array($conditions)) {
            $options = [];
            foreach ($conditions as $key => $value) {
                $options[] = is_int($key) ? '-' . $value : '-' . $key . $value;
            }
            $query .= implode(' ', $options);
        } else {
            $query .= $conditions;
        }

        exec($query, $planetOut);

        $planetEnglish = planetName::$planetEnglish;

        $planet_array = array();

        $planet_len=0;

        foreach ($planetOut as $key => $value) {
            $planetinfo = explode(',', str_replace(' ', '', $value));
            if($planetinfo[0]=='house1'){
                $planet_len=$key;
            }
            $planet_array[$planetinfo[0]] = $planetinfo;
        }

        foreach ($starsCode as $keyCode => $lineCode) {

            if (is_array($lineCode)) {
                if ($keyCode == 'virtual') {
                    foreach ($lineCode as $keyX => $lineX) {
                        $function = 'calculate' . $lineX;
                        $virtual_planet = $this->$function($planet_array);
                        if (!empty($virtual_planet)) {
                            array_splice($planetOut, $planet_len, 0, implode(",",$virtual_planet));
                            $planet_len++;
                        }
                    }
                } elseif ($keyCode == 'planet_xs') {

                    foreach ($lineCode as $keyX => $lineX) {
                        $planet_X = $this->calculateplanetXs($conditions, $lineX);
                        array_splice($planetOut, $planet_len, 0, $planet_X);
                        $planet_len++;
                    }
                }

            } else {
                $lineCodeEnglish = $planetEnglish[$lineCode];
                $planetinfo_str = $planetOut[$keyCode];
                $planetinfo = explode(',', str_replace(' ', '', $planetinfo_str));

                if ($lineCodeEnglish == $planetinfo['0']) {

                } else {
                    if ($lineCodeEnglish) {

                    }
                }
            }
        }

        return $planetOut;
    }
    //直接拿数据
    public function calculate($conditions, $starsCode, $planetOut = [])
    {

        if (empty($planetOut)) {
            $planetOut = $this->SweTest($conditions, $starsCode);
        }

        $p_count = count($starsCode)+count($starsCode['virtual'])+count($starsCode['planetXs'])-2;

        $house_data = array();
        $planet_data = array();
        $ascmcs_data = array();
        $planet_english = planetName::$planetEnglish;
        $planet_chinese = planetName::$planetChinese;
        $data['planetEnglish'] = planetName::$planetEnglish;
        $data['planetChinese'] = planetName::$planetChinese;
        $data['planetFont'] = planetName::$planetFont;
        $data['house_life'] = planetName::$house_life;
        $data['signFont'] = planetName::$signFont;
        $data['signEnglish'] = planetName::$signEnglish;
        $data['sign_guardian_index'] = planetName::$sign_guardian_index;
        $data['sign_phase'] = planetName::$sign_phase;
        $data['signChinese'] = planetName::$signChinese;

        foreach ($planetOut as $key => $line) {

            $lineInfo = explode(',', str_replace(' ', '', $line));

            if (!empty($lineInfo[1]) and $lineInfo[1] >= 0) {

                $longitude_array['sign'] = $this->Convert_sign_Longitude($lineInfo[1]);

                $longitude_array['longitude'] = $lineInfo[1];

                if (!empty($lineInfo[2])) {
                    $longitude_array['speed'] = $lineInfo[2];
                } else {
                    $longitude_array['speed'] = 360;
                }

                $planet_info = $longitude_array;

                if ($key < $p_count) {
                    $planet_info['planet_english'] = str_replace('.', '', $lineInfo[0]);
                    $planet_info['code_name'] = (string)array_search($planet_info['planet_english'], $planet_english);

                    $planet_info['planet_chinese'] = $planet_chinese[$planet_info['code_name']];
                    if (!empty($lineInfo[3])) {
                        $planet_info['house_number'] = $lineInfo[3];
                    }
                    $planet_data[] = $planet_info;
                } elseif ($key >= $p_count and $key < ($p_count + 12)) {
                    $house_info = $longitude_array;
                    $house_info['house_id'] = $key - $p_count + 1;
                    $house_data[] = $house_info;
                } elseif ($key >= ($p_count + 12)) {
                    $planet_info['planet_english'] = str_replace('.', '', $lineInfo[0]);
                    $planet_info['code_name'] = (string)array_search($planet_info['planet_english'], $planet_english);
                    $planet_info['planet_chinese'] = $planet_chinese[$planet_info['code_name']];
                    if (!empty($lineInfo[3])) {
                        $planet_info['house_number'] = $lineInfo[3];
                    }
                    if ($key == ($p_count + 12)) {
                        $data['planet_ascendant'] = $planet_info;
                    }
                    $ascmcs_data[] = $planet_info;
                }
            }
        }


        $data['ascmcs'] = $ascmcs_data;
        $data['planet'] = $planet_data;
        $data['house'] = $house_data;


        return $data;
    }
    //福点计算  PartOfFortune
    function calculatepFortune($planet_data)
    {

        if (empty($planet_data['Sun']) or empty($planet_data['Moon']) or empty($planet_data['house1']) or empty($planet_data['house7'])) {

            return false;
        }
        $Sum = $planet_data['Sun'][1];
        $Moon = $planet_data['Moon'][1];
        $house1 = $planet_data['house1'][1];
        $house7 = $planet_data['house7'][1];

        if ($house1 > $house7) {
            if ($Sum <= $house1 And $Sum > $house7) {
                $day_chart = True;
            } else {
                $day_chart = False;
            }
        } else {
            if ($Sum > $house1 And $Sum <= $house7) {
                $day_chart = False;
            } else {
                $day_chart = True;
            }
        }
        if ($day_chart == True) {
            $Fortune_longitude = $house1 + $Moon - $Sum;
        } else {
            $Fortune_longitude = $house1 - $Moon + $Sum;
        }

        if ($Fortune_longitude >= 360) {
            $Fortune_longitude = $Fortune_longitude - 360;
        }

        if ($Fortune_longitude < 0) {
            $Fortune_longitude = $Fortune_longitude + 360;
        }

        $planet_info[0] = "PartOfFortune";
        $planet_info[1] = $Fortune_longitude;
        $planet_info[2] = "360";


        return $planet_info;
    }

    //上升
    function calculate10($planet_data)
    {
        if (empty($planet_data['Ascendant'])) {
            return false;
        }
        $planet_info = $planet_data['Ascendant'];

        return $planet_info;
    }

    //中天
    function calculate11($planet_data)
    {
        if (empty($planet_data['MC'])) {
            return false;
        }
        $planet_info = $planet_data['MC'];

        return $planet_info;
    }

    //东升点
    function calculate14($planet_data)
    {
        if (empty($planet_data['equat.Asc.'])) {
            return false;
        }
        $planet_info = $planet_data['equat.Asc.'];
        $planet_info[0]='equatAsc';
        return $planet_info;
    }

    //宿命点
    function calculate13($planet_data)
    {
        if (empty($planet_data['Vertex'])) {
            return false;
        }
        $planet_info = $planet_data['Vertex'];

        return $planet_info;
    }

    //下降
    function calculate18($planet_data)
    {
        if (empty($planet_data['house7'])) {
            return false;
        }
        $planet_info = $planet_data['house7'];
        $planet_info[0]='Des';
        $planet_info[2]= 360;
        $planet_info[3]='7';
        return $planet_info;
    }

    //天底
    function calculate19($planet_data)
    {
        if (empty($planet_data['house4'])) {
            return false;
        }
        $planet_info = $planet_data['house4'];
        $planet_info[0]='IC';
        $planet_info[2]= 360;
        $planet_info[3]= 4;
        return $planet_info;
    }

    //日月中
    function calculate20($planet_data)
    {
        if (empty($planet_data['Sun']) or empty($planet_data['Moon'])) {
            return false;
        }
        $Sum = (float)$planet_data['Sun'][1];
        $Moon = (float)$planet_data['Moon'][1];

        $true_average = ($Sum + $Moon) / 2;

        $diff = abs($Sum - $Moon);

        if ($diff >= 180 And Abs($true_average - $Sum) > 90 And Abs($true_average - $Moon) > 90)
        {
            $true_average = $true_average + 180;
        }

        $true_average = $this->crunch($true_average);

        $planet_info[0] = 'Sun-Moon';
        $planet_info[1] =  $true_average;
        $planet_info[2] = 360;
        return $planet_info;
    }

    //南交
    function calculate21($planet_data)
    {
        if (empty($planet_data['meanNode'])) {
            return false;
        }
        $true_average=$planet_data['meanNode'][1] + 180;
        $true_average = $this->crunch($true_average);
        $planet_info[0]='MeanSouthNode';
        $planet_info[1]= $true_average;
        $planet_info[2]= 360;
        return $planet_info;

    }
    //小行星获取
    function calculateplanetXs($conditions, $xs)
    {

        $conditions['p']='s';
        $conditions['xs']=$xs;

        $query = $this->sweph . "swetest -edir" . $this->sweph . ' ';

        if (is_array($conditions)) {
            $options = [];
            foreach ($conditions as $key => $value) {
                $options[] = is_int($key) ? '-' . $value : '-' . $key . $value;
            }
            $query .= implode(' ', $options);
        } else {
            $query .= $conditions;
        }

        exec($query, $planetOut);

        if (!empty($planetOut)) {
            $lineInfo =str_replace(' ', '', $planetOut[0]);
            return $lineInfo;
        }

    }
//落入星座计算
    function Convert_sign_Longitude($longitude)
    {
        $sign_num = intval($longitude / 30);
        $pos_in_sign = $longitude - ($sign_num * 30);
        $deg = intval($pos_in_sign);
        $full_min = ($pos_in_sign - $deg) * 60;
        $min = intval($full_min);
        $full_sec = round(($full_min - $min) * 60);

        $signEnglish = planetName::$signEnglish;

        $signChinese = planetName::$signChinese;

        $signFont = planetName::$signFont;;

        return ['deg' => $deg, 'min' => $min, 'sec' => $full_sec, 'sign_id' => $sign_num, 'sign_english' => $signEnglish[$sign_num], 'sign_chinese' => $signChinese[$sign_num], 'sign_font' => $signFont[$sign_num]];
    }

    //落入星座计算
    function Convert_sign_deg_min($longitude)
    {
        $sign_num = intval($longitude / 30);
        $pos_in_sign = $longitude - ($sign_num * 30);
        $deg = intval($pos_in_sign);
        $full_min = ($pos_in_sign - $deg) * 60;
        $min = intval($full_min);
        $full_sec = round(($full_min - $min) * 60);

        return ['deg' => $deg, 'min' => $min, 'sec' => $full_sec];
    }

    //小数转换度数
    function Convert_deg_min($longitude)
    {

        $deg = intval($longitude);
        $full_min = ($longitude - $deg) * 60;
        $min = intval($full_min);
        $full_sec = round(($full_min - $min) * 60);

        return ['deg' => $deg, 'min' => $min, 'sec' => $full_sec];
    }

    //计算合位数据
    function transitsProgressed($arr, $Progressed, $current_cycle)
    {

        $planetOut = $this->SweTest($arr);

        $minDell = 99999;

        $newMoon = '';

        foreach ($planetOut as $key => $line) {

            $lineInfo = explode(',', $line);

            $defug = trim($lineInfo[2], ' ');

            $Progressed_defug = $Progressed - abs($defug);

            if ($minDell > abs($Progressed_defug)) {
                $minDell = abs($Progressed_defug);
                $newMoon = str_replace(' UT', '', $lineInfo[1]);
            }

        }

        $newMoon_time = strtotime($newMoon);

        $arr['b'] = date('d.m.Y', $newMoon_time);

        if ($current_cycle == 'd') {
            $arr['ut'] = '0:30:0';
            $arr['n'] = '24';
            $arr['s'] = '60m';
            $current_cycle = 'h';
        } else if ($current_cycle == 'h') {
            $arr['ut'] = date('H', $newMoon_time) . ':00:30';
            $arr['n'] = '60';
            $arr['s'] = '1m';
            $current_cycle = 'm';
        } else if ($current_cycle == 'm') {
            $arr['ut'] = date('H:i', $newMoon_time) . ':00';
            $arr['n'] = '60';
            $arr['s'] = '1s';
            $current_cycle = 's';
        } else {
            $current_cycle = '';
        }

        if ($current_cycle != '') {

            return $this->transitsProgressed($arr, $Progressed, $current_cycle);

        }
        return $newMoon_time;

    }

    //万能计算点数数据
    function universalProgressed($arr, $P_name, $Progressed, $current_cycle)
    {
        $planetOut = $this->SweTest($arr);

        $minDell = 99999;
        $newTime = '';

        foreach ($planetOut as $key => $line) {

            $lineInfo = explode(',', $line);

            if ($P_name == trim($lineInfo[0], ' ')) {
                $defug = trim($lineInfo[2], ' ');

                $Progressed_defug = $Progressed - abs($defug);

                if ($minDell > abs($Progressed_defug)) {

                    $minDell = abs($Progressed_defug);
                    $newTime = str_replace(' UT', '', $lineInfo[1]);
                }
            }
        }

        $newMoon_time = strtotime($newTime);

        $arr['b'] = date('d.m.Y', $newMoon_time);


        if ($current_cycle == 'd') {
            $arr['ut'] = '0:30:0';
            $arr['n'] = '24';
            $arr['s'] = '60m';
            $current_cycle = 'h';
        } else if ($current_cycle == 'h') {
            $arr['ut'] = date('H', $newMoon_time) . ':00:30';
            $arr['n'] = '60';
            $arr['s'] = '1m';
            $current_cycle = 'm';
        } else if ($current_cycle == 'm') {
            $arr['ut'] = date('H:i', $newMoon_time) . ':00';
            $arr['n'] = '60';
            $arr['s'] = '1s';
            $current_cycle = 's';
        } else {
            $current_cycle = '';
        }

        if ($current_cycle != '') {

            return $this->universalProgressed($arr, $P_name, $Progressed, $current_cycle);

        }
        return $newMoon_time;
    }

    public function crunch($x)
    {
        if ($x >= 0)
        {
            $y = $x - floor($x / 360) * 360;
        }
        else
        {
            $y = 360 + ($x - ((1 + floor($x / 360)) * 360));
        }

        return $y;
    }
}


