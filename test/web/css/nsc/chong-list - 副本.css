@charset "utf-8";

h1,h2,h3,h4,h5,ul,p { margin:0; padding:0;}
li { list-style:none;}
a{ text-decoration:none;color: #212f78;}
table{ font-size: 14px;}
table input{ vertical-align: middle;position: relative;      border: 1px solid #bbb;      background: #fff;      width: 100px;      /* height: 18px; */      /* padding: 0 2px 0 5px; */      /* line-height: 24px; */      -moz-box-shadow: 2px 2px 2px #eee inset;      /* -webkit-box-shadow: 2px 2px 2px #eee inset; */      /* box-shadow: 2px 2px 2px #eee inset; */      -moz-border-radius: 3px;      -webkit-border-radius: 3px;      border-radius: 3px;      color: #666;      font-family: 'Microsoft Yahei';}
select { padding:0 0 0 5px; border:1px solid #bbb; height:32px; line-height:32px; font-size:14px; color:#666; font-family: Microsoft Yahei;}
a:hover {color:#a96b94;}
.clearfix:after { content:"."; display:block; height:0; clear:both; visibility:hidden;}

.redtit{ color: red; font-size: 12px; display: block; text-align: center;}
.red-world,.red{ color: #ff0000;}
.red-world,.green { color: #5bc92e;}

.boxline{ text-align: center;}
input[type='submit'],.formZjbd,.formZjbd_sjh { width:90px; height:33px; line-height: 33px; font-size: 16px!important; color: #fff; padding: 0!important; font-family: Microsoft Yahei; margin:0 10px; background:url("/images/nsc/btn-modification_bg.png") no-repeat; border:0!important; cursor:pointer; letter-spacing:3px;}
input[type='reset'],.formReset,.formBack,.bt_cedan,.bt_cancel,.a-back_list{ font-size: 16px; width: 90px; height: 33px; line-height: 33px; color:#353434; margin:0 10px; padding: 0!important; background: url("/images/nsc/btn-reset_bg.png") no-repeat; border:0!important; font-family: Microsoft Yahei; cursor:pointer; letter-spacing:3px;}
.list_btn_box .formZjbd { letter-spacing:1px!important;}

/*错误页面*/

/*操作错误正确提示*/
#success h3,#error h3 { text-align: center; font-size: 18px;}
#error{ background: url("/images/nsc/icon_error-big.png") no-repeat center 0px; padding: 130px 0 0px;}
#success { background:url("/images/nsc/icon_success-big.png") no-repeat center 100px; padding: 180px 0  100px;}
.hint_red { color: #fb2323;}
.hint_green { color: #60a52c;}
.hint_green p { margin-bottom: 10px;}
/*提示页面*/
.title{ margin: 10px auto; text-align: center;}
/*未开奖*/

/*未查看消息颜色*/

.total_1024 { border:1px solid #ddd; background-color:#f6f6f6; border-radius:4px; margin:0 auto 8px; padding:10px 15px; font-size:16px; font-weight:bold; color:#fc7c23;}
.total_1024 b { color:#888; font-size:14px;}
.total_1024 i { font-style:normal; margin:0 0 0 12px; color:#888; font-size:12px; font-weight:normal;}
/*返回上一页*/
.yhlb_back{ margin: 20px 0px; text-align: center; line-height: 30px;}
.yhlb_back a,.btn_blue{ display:inline-table; width: 120px; height: 39px; line-height: 39px; background: url("/images/nsc/skin/blue_skin/list/reset.png") no-repeat; color: #333; font-size: 14px;}
/*分页*/
.pageinfo,.page { float: right; font-size: 14px;}
.pageinfo span, .page span { display: inline-block; height: 24px;}
.pageinfo a,.page a,.newsPages a { display: inline-table; margin-left: 3px; padding: 4px 5px; border: 1px solid #ddd; background: #fff; color: #555; font-size: 14px; line-height: 14px; border-radius:4px;}
.pageinfo a:hover,.page a:hover,.newsPages a:hover,.popularize_page #tPages a:hover { border: 1px solid #f49e7f; color:#eb5f2e;}
.pageinfo strong,.page strong,.newsPages strong { display: inline-table; margin-left: 3px; padding: 4px 5px; border: 1px solid #eb5f2e; background: #eb5f2e; color: #fff; font-size: 14px; line-height: 14px; border-radius:4px;}
.pageinfo #iGotoPage,.page #iGotoPage{ border-radius: 4px; border: 1px solid #dbdbdb; height: 25px; width: 40px; margin: 0 5px; text-align: center; color: #333;}
.pageinfo .button,.page .button { padding: 5px 12px; cursor: pointer; margin-left: 5px; background:#eb5f2e; color:#fff; font-size:15px; border:0; border-radius:4px;}
.user_page_click { float:none; text-align:center;}
.user_page_text { text-align:center; display:block; line-height:18px; padding:10px 0 0 0;}

/*分页-系统公告右边*/
.newsPages { font-size: 14px; color: #888; text-align: center; padding: 10px 0;}
.newsPages a,.newsPages strong { margin-bottom: 5px;}
.newsPages select { padding: 5px 5px; margin: 0 3px;}
.newsPages #tPages { display: block; margin: 8px 0 0;}

/*推广设定分页*/
.popularize_page { line-height:24px; font-size:12px; color:#666; text-align:center; padding:5px 0;}
.popularize_page #tPages { display: inline-block; height: 24px; margin:0 15px;}
.popularize_page #tPages a { display: inline-table; margin-left: 6px; padding: 4px 6px; border: 1px solid #ddd; background: #fff; color: #555; font-size: 14px; line-height: 14px; border-radius:4px;}
.popularize_page #tPages strong { display: inline-table; margin-left: 6px; padding: 4px 6px; border: 1px solid #eb5f2e; background: #eb5f2e; color: #fff; font-size: 14px; line-height: 14px; border-radius:4px;}
.popularize_page select { padding:3px 5px; margin:0 8px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset; -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px;}

/*列表相关*/
#nsc_subContent{ background:#fff; border:1px solid #e4e4e4;  border-top:0; width:60%; min-width:980px; margin:10px auto 0; box-shadow: 0px 0px 5px #c8c8c8;}
#subContent_bet_re { padding: 10px 0px; /* min-height:438px; */ }
.topContent { height:30px; color:#fff; display:none;}
.topContent ul { overflow:hidden; }
.topContent ul li { display:inline; float:left; padding:0px 0px; font-size:18px; }
.topContent ul li a,.topContent ul li a:hover { color:#333; font-size:18px; border-left:6px solid #fc7c23; font-weight:bold;  padding-left:10px; height: 26px; line-height: 26px; }
.topContent ul li.hover { background:#e6ecfe; }
.topContent ul li.hover a,.topContent ul li.hover a:hover { color:#1a2f94; }

#siderbar { background:#f8f5f5 url("/images/nsc/siderbar_list-hxbg.png") repeat-x 0 49px;}
#siderbar ul { overflow:hidden;}
#siderbar ul li { height:50px; line-height:50px; padding:0px 12px; float:left; display:inline; font-size:15px; border-right:1px solid #e5e5e5; color:#696969;}
#siderbar ul li a:hover {color:#a96b94;}
#siderbar ul li.current { height:50px; line-height:50px; border-right:1px solid #e5e5e5;  background:#fff url("/images/nsc/siderbar_list-current-hxbg.png") repeat-x top left;}
#siderbar ul li a { color:#696969; display: block; line-height: 50px;}
#siderbar ul li.current a,#siderbar ul li.current a:hover { color:#696969; display: block; height: 50px;}

/*
#siderbar { height:42px; background:#e6ecfe url(/images/nsc/skin/blue_skin/list/siderbarbg.jpg) repeat-x left bottom; padding-top:14px; }
#siderbar ul li { height:42px; line-height:42px; padding:0px 20px; float:left; display:inline; font-size:16px; }
#siderbar ul li.current { height:40px; line-height:40px; border:2px solid #98a7e0; border-bottom:0px; background:#fff; }
#siderbar ul li a,#siderbar ul li a:hover { color:#1b2365; }
#siderbar ul li.current a,#siderbar ul li.current a:hover { color:#1b2365; }
*/
/*走势图*/
.lhfx_tit { float:left; font-size: 26px; color: #888; font-family: Microsoft Yahei; text-align: left; font-weight: bold; margin:0 0 0 25px; background: #f8f8f8;position:relative;margin-top:5px\9;}
.lhfx_tit span { color: #ff632c; margin-right:5px;line-height:50px;}
.lhfx_tit .showAll { vertical-align: middle; cursor: pointer; display: inline-block; width: 30px; height: 30px; background: #f8f8f8 url(/images/nsc/lottery/next.png) no-repeat center center; }
.wo_choose { padding:0 0 0 25px; margin-bottom: 18px; font-family:'Microsoft Yahei'; font-size:14px; color:#333;position:relative;z-index:10;}
.wo_choose span { font-weight:bold; font-size:18px;}
.wo_choose label { color:#ff632c;}
#searchBox .secondary_tabs { margin:8px 10px 0 10px;}
#searchBox .secondary_tabs ul li { margin:0 10px 0 0!important;}
#searchBox .secondary_tabs ul li a,#searchBox .secondary_tabs ul li a:hover { background:#fff; border:1px solid #dedede; border-radius:26px; font-size:13px; -moz-box-shadow:2px 2px 4px #ddd; -webkit-box-shadow:2px 2px 4px #ddd; box-shadow:2px 2px 4px #ddd;}
.luzhi { background: url(/images/nsc/lottery/pk10_luzhi_icon.gif) no-repeat; float: left; display:inline; margin-top: 16px; font-size: 14px; line-height: 20px; width: 53px; height: 15px; text-indent: -9999px; }
.luzhi a,.luzhi a:hover { display: block; text-decoration: none; }

.secondary_tabs { float:left;}
.secondary_tabs ul { padding:0 0 0 10px;}
.secondary_tabs ul li { font-size:14px; float:left; display:inline; margin:10px 0 0 0;}
.secondary_tabs ul li a,.secondary_tabs ul li a:hover { padding:8px 15px; display:inline-block; text-align:center; color:#666;}
.secondary_tabs ul li.curr a,.secondary_tabs ul li.curr a:hover { background:#694d85; color:#fff; }
.lhfx_search_time { float:left; font-size:14px; margin:8px 0 0 25px;}
.time_input { vertical-align:middle; padding: 0 3px!important; height: 33px; line-height: 32px; border:1px solid #ddd;}
.time_btn { font-family: Microsoft Yahei; margin:0 10px; background:url(/images/nsc/btn-modification_bg.png) no-repeat!important; border:0!important; cursor:pointer; vertical-align:-2px;color:#fff;width:90px;height:33px;text-align:center;vertical-align:middle;}

.lhfx_lotterylist { position: absolute; font-family: microsoft yahei;z-index: 9900; top:65px;top:57px\9;left: 32px; line-height:20px; display: none; border:1px solid #ddd; box-shadow:2px 3px 5px #ddd; background:#f8f8f8; padding-top:10px\9;padding:10px 0 10px 15px; }
.lhfx_lotterylist dl,.lhfx_lotterylist dl dt,.lhfx_lotterylist dl dd { padding:0px; margin:0px; }
.lhfx_lotterylist dl { font-size: 14px; overflow: hidden; width: 180px; }
.lhfx_lotterylist dl dt { color: #ff632c; font-weight: bold; font-size:16px;}
.lhfx_lotterylist dl dd { padding:5px 0px 0px 0; width: 90px; float: left; display: inline;}
.lhfx_lotterylist dl dd a { font-size: 14px; color: #666; font-weight: normal;}
.lhfx_lotterylist dl dd a:hover { color: #ff632c; font-weight: normal; }

.menus-li { overflow: hidden; zoom: 1;}
.menus-li li{ float: left; padding: 0px 20px; height: 32px; line-height: 32px; color:#303030; cursor: pointer; font-size: 14px; margin: 0 0 10px 0;}
.menus-li .on{ background:#694d85; color:#fff;}

#searchBox { background:#f8f8f8 url(/images/nsc/skin/blue_skin/list/search_icon.png) no-repeat 10px center; padding: 10px 0 10px 70px; font-size:14px; color:#888; margin-bottom: 15px; border-radius:8px; border:1px solid #f2f2f2;}
#searchBox label { margin:0 5px 0 2px; }
#searchBox .inlineBlock { display:inline-block; margin:2px 0;}
#searchBox .duli_topbj {}
#searchBox input[type='text'] { vertical-align:middle; position:relative; border:1px solid #bbb; background:#fff; width:133px; height:30px; padding:0 2px 0 5px; line-height:30px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset; -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px; color:#666; font-family:'Microsoft Yahei';}
#searchBox select,.grayTable .u_add_zr input,.fandianinput_tit,.search_br select,.step .item .cz_input1,.step .item .item_r select { -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset;}
.no_bk-bg { border: 0!important; padding: 0!important; background: none!important; -moz-border-radius:0!important; -webkit-border-radius:0!important; border-radius:0!important; -moz-box-shadow:none!important; -webkit-box-shadow:none!important; box-shadow:none!important;}

#searchBox .inlineBlock select { margin-right:2px; vertical-align:middle;}
.clear-width { width:auto!important;}
.border0 { border:0!important;}
#searchBox .input150,#searchBox .input_02 {}
#searchBox span.image { background:url(/images/nsc/calendar_icon.png) no-repeat; display:inline-block; width:15px; height:17px; vertical-align: middle; cursor:pointer; margin-left:5px;}
#searchBox .formCheck,.search_br .formCheck,.btn_confirm { background:url(/images/nsc/searchBtn.png) no-repeat; width:82px; height:32px; line-height:32px; padding:0; font-size: 16px; color: #fff; font-family: Microsoft Yahei; margin:0 10px; border:0; cursor:pointer; letter-spacing:5px; -moz-box-shadow:none;  -webkit-box-shadow:none; box-shadow:none; vertical-align:middle;}
.search_br { height: 60px; text-align: center; color:#666;}
.search_br span{ color:#B3B3B3; height:30px; line-height:30px; display:inline-block; padding:0 12px; font-size:14px; border:1px solid #B3B3B3; cursor:default; border-radius:5px; cursor:pointer; vertical-align:middle;}
.search_br span.hover { color:#fc7c23; border:1px solid #fc7c23;}
.search_br .radio_person { display:none;}
.search_br .radio_team { display:none;}

.task_div { text-align: center;font-family: "Microsoft Yahei";background-color: #fc7c23;border: 1px solid #fc7c23;color: #FFFFFF;overflow: hidden;position: absolute;width: 290px !important; line-height: 24px;}
.task_div a,.task_div a:hover { color:#fff!important; }
.task_div textarea { width:100%; font-size: 20px; text-align: center; font-weight: bold; color: #fc7c23; border: 0!important; resize: vertical;}

/*删除与批量删除*/
#deleteall,#deleteselect { font-family: Microsoft Yahei; background:#ddd; border:1px solid #ccc; cursor: pointer; padding:0 5px; height:28px; font-size: 14px; line-height: 28px; color: #444; display:inline-block; border-radius:3px;}
/*显示下级*/
.memberEevnt { color:#f96905; cursor:pointer; }
.memberList { position:absolute; border:1px solid #f96905; font-size:16px; color:#f96905; text-align:left; padding:30px 35px; background:#fff5ee; line-height:22px; display:none; margin:21px 0 0 1px;}
.memberClose { cursor:pointer; position:absolute; top:7px; right:10px; right:5px; text-indent:-9999px; width:13px; height:13px; background:url(/images/nsc/skin/blue_skin/list/close.gif) no-repeat; }
.memberTip { background:url(/images/nsc/skin/blue_skin/list/mem_tip.gif) no-repeat; width:13px; height:9px; position:absolute; top:-9px; }

#userlistDL strong a { color:#f96905;}

.formTable {}
.formTable tr th{}
.formTable tr td {}

.tdz_left,.txtLeft { width: 40%; padding-right: 10px!important; text-align: right!important;}
.tdz_left2 { width: 48%; padding-right: 10px!important; text-align: right!important;}
.tdz_right,.txtRight { text-align: left!important; padding-left: 10px!important;}
.u_add_zl { width: 21%; text-align: right!important; padding-right: 10px!important; background: #f8f5f5;}
.u_add_zr { text-align: left!important; padding-left: 20px!important; position:relative;}
.u_add_zr p { display:inline; }
.u_add_zr label { margin-right:10px; font-size:15px; font-weight:bold; color:#444; cursor:pointer;}
.text_hint { color:#c3c3c3; padding-left: 10px; font-size:12px;}
.hint_p { font-size:12px; margin-left:10px;}
.hint_p .hint_correct { color:#5bc92e; padding:5px 8px; background:#ddf4d3; border:1px solid #acdc99; border-radius:3px;}
.hint_p .hint_error { color:#FF0000; padding:5px 8px; background:#ffd9d9; border:1px solid #f3a4a4; border-radius:3px;}

.no-records { padding: 30px 0!important; text-align:center;}
.l_message { line-height:30px; height:30px; color:#ff0000; margin-top:10px; }
.font_red { color:#ff0000; }

.list_page { line-height: 30px; margin-top: 10px; color: #888;}
.page { float: right;}
#contentBox {}
.set_list { background: #f8f8f8; padding: 13px 0 13px 20px; margin-bottom: 15px;}
.set_list label { font-size: 15px; color:#444; font-weight:bold;}
.set_list input[type='submit'] { letter-spacing:0!important;}
.set_list input:disabled{background:#B5B3B3;color:#FAF6F6;cursor:default;border-radius: 5px;-webkit-border-radius: 5px;-moz-border-radius: 5px;}
.list_btn_box { text-align: center; padding: 15px 0 0 0;}
.floatLeft {float: left;}
.floatRight {float: right;}
.wr200 {width: 200px; text-align: right!important;}
.generalize_footer { text-align: left; color: #666; line-height: 28px; padding: 20px 0 0 20px; font-size:14px;}
.generalize_footer #fe_text { border:1px solid #ddd!important; width:500px; color:#000;}
.tuiguang_td { text-align: left!important; padding: 0 0 0 50px!important;}
.tuiguang_td input { margin-right: 5px; text-align:center; border-radius:4px; -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset;}
.tuiguang_td-width input { width:88px; margin-right:5px;}
.z_list_record { margin-bottom: 15px;}
.z_list_record td { padding: 20px 0;}
.z_list_record .z_zh { font-size: 24px; color: #444; background-color: #f6f6f6; width: 50%; text-align: center; border-top: 2px solid #d6d6d6;}
.z_list_record .z_money { font-size: 24px; color: #816F9A; background-color:#f2eff5; width: 50%; text-align: center; border-top: 2px solid #dad0e3;}
.gr-td_choose {display: inline-table; padding-top: 10px;}
.formSame,.z_copy_btn { width: 62px; height: 35px; line-height: 32px; padding: 0!important; border: 0!important; color: #333; font-size: 14px; cursor: pointer; background:url(/images/nsc/skin/blue_skin/list/form_smallBtn.png) no-repeat;}
.formSubmit { width: 62px; height: 35px; line-height: 32px; padding: 0!important; border: 0!important; color: #fff; font-size: 14px; cursor: pointer; background:url(/images/nsc/skin/blue_skin/list/form_smallBtn.png) no-repeat 0 -45px;}
.z_copy_btn { margin: 0 0 0 10px;}
.z_red_color {}
.underway_text { color: #5bc92e;}
.cancel_text { color: #f96c0a;}
.accomplish_text { color: #ff2d2d;}
#lotteriesform table td select { border: 1px solid #bbb; border-radius:4px; -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset;}
.xk_tr td { background-color: #fafafa;}
.tdbg { background-color: #fbfbfb;}

.list-div { overflow: hidden; zoom:1;}
.jilu_box_l { width: 60%; float: left; margin-right: 10px;}
.jilu_box_r { width: 38%; float: left;}


/*提款*/
.z_red_color,.font_red_font { color: #ff6600; font-size: 16px; font-weight: bold;}

/*公共输入提款密码*/
.commonality_drawings_password { padding:88px 0 150px;}
.commonality_drawings_password .pd_left { width: 420px; margin: 0 auto;}
.commonality_drawings_password h5 { font-size: 24px; color:#5c646a; font-weight: normal; margin: 0 0 12px 0;}
.commonality_drawings_password_input { margin-bottom:20px;}
.commonality_drawings_password_input .password { font-size: 18px; color: #333; padding:13px 8px; width: 350px; border: 1px solid #bbb; border-radius:4px; -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset; font-family:'Microsoft Yahei';}

/*密保设定*/
.list_mb_input1 { width: 220px;}

/*绑定邮箱*/
.yes_email_t { font-size: 24px; color: #fc7c23; background-color: #ffeadc; border-top: 2px solid #ffc297; padding: 20px 0; text-align: center;}
.yes_email_input { background: none!important; border: 0!important; font-size: 24px; color: #fc7c23;}
.yes_email_b { font-size: 14px; color: #333; padding: 30px 0 0; text-align: center;}

/*绑定卡号*/
.bttt {}
.bttt ul li { padding: 15px 0; border-bottom: 1px dotted #ddd; font-size: 14px; color: #333;}
.bttt .ttt1 { display: inline-table; width: 20%; padding-right: 10px!important; text-align: right!important; color: #888;}
.bttt .ttt2 { display: inline-table; text-align: left!important; padding-left: 10px!important; font-size: 16px;}
.ttt4 { text-align: center; display: block;}

#point { color: #888; line-height: 24px;}

/*注册管理*/
#registerBox { background:#f4f4f4; margin:20px 30px 0px 30px; }
.userBox { margin-bottom:10px; }
.userBox table { background:#dbdddf; color:#888888; }
.userBox table tr { height:50px; line-height:50px; }
.userBox table tr td { background:#f4f4f4; padding-left:10px; }
.userBox table tr td.title { text-align:right; background:#ebebeb; padding-left:0px; color:#333; }
.userBox table tr td input { vertical-align:middle; }
.userBox table tr td input.input150 { height:36px; line-height:36px; width:200px; border:1px solid #ccc; margin-right:5px; }

.peieBox { margin-bottom:15px; }
.peieBox h3 { margin:15px 0 10px;  height:24px; line-height:24px; color:#fc7c23; font-size:18px; border-left: 5px solid #fc7c23; padding-left:10px; font-weight:normal; text-align: left;}
.peieBox table { background:#dbdddf; color:#333; text-align:center; font-size:14px; }
.peieBox table tr { height:42px; line-height:42px; }
.peieBox table tr td { background:#f4f4f4; }

.fandianBox { overflow: hidden; }
.fandianBox h3 { margin:15px 0;  height:24px; line-height:24px; color:#694d85; font-size:18px; border-left: 5px solid #694d85; padding-left:10px; font-weight:normal; text-align: left;}
.fandianBox .fandian { border:1px solid #dbdddf; border-top:0px; padding:14px; }
.fandianBox .fandian p { font-size:14px; color:#333; vertical-align:bottom; padding-bottom:10px; }
.fandianBox .fandian p.msg { font-size:12px; color:#888; }
.fandianBox .fandian p select { vertical-align:bottom; margin:0px 10px; }
.fandianBox .fandian p .formWord{ background:url(/images/nsc/skin/blue_skin/list/saveBtn.png) no-repeat; width:140px; height:44px; text-indent:-9999px; border:0px; cursor:pointer; }
.dotTitle { margin:15px 0;  height:24px; line-height:24px; color:#694d85; font-size:18px; border-left: 5px solid #694d85; padding-left:10px; font-weight:normal; text-align: left;}

.lotteryBox { margin-bottom:10px; }
.lotteryBox table { background:#dbdddf; color:#888888; text-align:center; font-size:14px; }
.lotteryBox table tr { height:42px; line-height:42px; }
.lotteryBox table tr th { background:#ebebeb; color:#000; font-weight:normal; }
.lotteryBox table tr td { background:#f4f4f4; color:#333; }
.lotteryBox table tr td input.input100 { width:100px; height:30px; line-height:30px; border:1px solid #ccc; margin-right:10px; }

/*在线充值*/
.maintain_ico { background:url(/images/nsc/skin/blue_skin/list/bank/sc_recharge_maintain_ico.png) no-repeat; width:202px; height:70px; display:block; position:absolute; top:-1px; left:-1px; cursor:default;}
.step { padding:0 20px;}
.step .item { overflow: hidden; zoom:1; padding: 15px 0; border-bottom: 1px dashed #ddd;}
.step .item .item_l { width:150px; float: left; font-size: 16px; line-height: 36px; color: #333; padding-right: 10px; text-align:right;}
.step .item .item_r { float: left;}
.step .item .item_r select {}
.step .item .cz_input1 { font-family: Microsoft Yahei; height: 52px; line-height: 52px; font-size: 22px; color: #333; border: 1px solid #bbb; padding: 0 8px; color: #333; border-radius:4px; margin-right:8px;}
.step .item .money_hanggao { line-height: 52px!important;}
.step .item .tips { color: #888; padding: 10px 0 0 0; font-size: 14px;}
.step .item .tips b { color: #fc7c23;}
.step .item .yuan { font-size: 28px; color:#333; vertical-align: -3px;}
.step .item #msg_money { color:red; padding-left: 38px; font-size: 14px;}
.step .item .money { font-size: 20px; color: #fc7c23; line-height: 36px;}
.step .z_font1 { font-size: 20px; color: #888; line-height: 36px;}


#choosedBank span { float:left; display:inline; margin-top:25px; }
#choosedBank .icon { margin-left:12px; width:25px; height:25px; }
#choosedBank .bankName { font-size:18px; margin-top:1px; }
#choosedBank .bankName img { vertical-align:middle;}
#choosedBank .up { width:11px; height:6px; background:url(/images/nsc/skin/blue_skin/list/bank/up.gif) no-repeat; margin-left:10px; margin-top:35px; }
#choosedBank .down { width:11px; height:6px; background:url(/images/nsc/skin/blue_skin/list/bank/down.gif) no-repeat; margin-left:10px; margin-top:35px; }
.zhi .icon { background:url(/images/nsc/skin/blue_skin/list/bank/zhi25.png) no-repeat; }
.gong .icon { background:url(/images/nsc/skin/blue_skin/list/bank/gong25.png) no-repeat; }
.jian .icon { background:url(/images/nsc/skin/blue_skin/list/bank/jian25.png) no-repeat; }
.zhao .icon { background:url(/images/nsc/skin/blue_skin/list/bank/zhao25.png) no-repeat; }
.nong .icon { background:url(/images/nsc/skin/blue_skin/list/bank/nong25.png) no-repeat; }
.bankBox { width:140px; height:38px; border:1px solid #c9c9c9; padding:5px 10px 0; background:#fff; float:left; display:inline; margin-right:20px; margin-bottom:10px; position:relative; cursor:pointer; overflow:hidden; }
.bankBox img { vertical-align:middle;}
.bankBox b { font-size:22px; color:#333; font-weight:normal; margin-left:10px;  }
.bankBox p { font-size:12px; color:#888; }
.bankBox span { display:none; }
.zhiBox { background:url(/images/nsc/skin/blue_skin/list/bank/zhi.png) no-repeat 25px 10px; }
.gongBox { background:url(/images/nsc/skin/blue_skin/list/bank/gong.png) no-repeat 25px 10px; }
.nongBox { background:url(/images/nsc/skin/blue_skin/list/bank/nong.png) no-repeat 25px 10px; }
.zhaoBox { background:url(/images/nsc/skin/blue_skin/list/bank/zhao.png) no-repeat 25px 10px; }
.jianBox { background:url(/images/nsc/skin/blue_skin/list/bank/jian.png) no-repeat 25px 10px; }
#banklist { padding: 0 0 0 160px; width:auto;}
#banklist .hover { border:1px solid #fc7c23; }
#banklist .hover span { position:absolute; right:0px; bottom:0px; background:url(/images/nsc/skin/blue_skin/list/bank/choose.png) no-repeat; width:21px; height:19px; display:block; }
.cz_btn_box { text-align:center; padding: 25px 0 50px 0;}
.cz_btn_box p { padding: 0 0 15px 0; color: #888;}
.cz_btn_box a.next { font-weight:bold; font-family: Microsoft Yahei; background:url(/images/nsc/skin/blue_skin/list/saveBtn2.png) no-repeat; width:201px; height:56px; font-size: 22px; line-height: 54px; color: #fff; display:inline-block; }
.cz_btn_box a.next:hover { background:url(/images/nsc/skin/blue_skin/list/saveBtn2.png) no-repeat 0 -66px;}
#bind { font-family: Microsoft Yahei; background:#fc7c23; cursor: pointer; border:0; padding:0 15px; height:32px; font-size: 14px; line-height: 32px; color: #fff; display:inline-block; margin: 0 15px; border-radius:3px;}

.jixuan{padding-left:10px;width:100px;}
.jixuan .jx_button_90x26{width:90px; height: 26px; margin-bottom:5px; border:1px solid #D8D8D8; background: #fff;border-radius:3px}

/*充值第二步*/
.gocz_btn_box { text-align: center; font-size: 16px; padding: 30px 0 35px 0;}
.gocz_btn_box p { padding: 10px 0;}
.gocz_btn_box p.ylj { color: #fc7c23; font-size: 20px;}
.gocz_btn { font-family: Microsoft Yahei; background:url(/images/nsc/skin/blue_skin/list/saveBtn2.png) no-repeat; border: 0; cursor: pointer; width:201px; height:56px; font-size: 22px; line-height: 54px; color: #fff; display:inline-block; margin: 0 15px;}
.cz_title { color: #000; font-size: 22px; font-weight: bold; float: left; margin: 5px 0 0 0;}
.back { float: right; background: url(/images/nsc/skin/blue_skin/list/bank/back_bg.png) no-repeat; padding-left: 32px; width: 100px; height: 34px; line-height: 34px; color: #888; font-size: 14px; display: block;}
.cz_sum { text-align: center; font-size: 24px; color: #333;}
.cz_sum b { color: #f00000; font-size: 30px;}
.cz_help { padding: 30px 0 20px; color: #aaa; font-size: 14px;}
.cz_help_t { color: #666; font-size: 18px; margin-bottom: 5px;}
.cz_help p { margin-bottom: 15px;}
.cz_help .safe1 { width: 480px; height: 234px; display: inline-block; background: url(/images/nsc/skin/blue_skin/list/bank/safe1.jpg) no-repeat; margin-right: 20px;}
.cz_help .safe2 { width: 413px; height: 234px; display: inline-block; background: url(/images/nsc/skin/blue_skin/list/bank/safe2.jpg) no-repeat;}

.time_tit { background: #fafafa;}
.time_tit_l { font-size: 20px; color: #000; font-weight: bold; margin: 0 50px 0 0;}
.time_tit_r {}
.time_tit_r b { color: #fc7c23; font-size: 20px; font-weight: bold;}
.cz_popup_hint { width:100%; position: absolute; top:50px;}
#popupad { width: 550px; background: #fafafa; border:2px solid #fc7c23; margin: 0 auto; position: relative; padding: 15px; font-size: 14px; line-height: 22px; color: #666;}
.p_closed { position:absolute; top: 10px; right: 10px; text-indent: -9999px; width: 20px; height: 20px; display: block; background: url(/images/nsc/login/msg_close.png) no-repeat 0px 0px; cursor:pointer;}
#popupad p .blue { color: #fc7c23;}
#popupad p.p2 { font-size: 16px; font-weight: bold; color: #333; margin: 5px 0 10px;}
#arrowleft { font-size: 16px; color:red;}
#arrowleft img { vertical-align: -15px; margin: 0 5px 0 10px;}
.even .button { padding: 0; width:68px; height:18px; z-index:1888;}
.zysx_tit { font-size: 16px; font-weight: bold; color: #fc7c23;}
.zysx_text { line-height: 22px!important;}
.zysx_text .red { color: #fc7c23;}

/*系统公告*/
.scroll_bar {}
.help_notice { overflow: hidden; zoom:1; width: 980px; height: 567px; margin: -10px 0 0 -12px; background-color: #f7f7f7;}
.help_notice_l { float:left; width:670px; background: url("/images/nsc/notice_boxbg.jpg") no-repeat left top;}
.help_notice_l ul {}
.help_notice h2{ height:72px;line-height:72px; background-color: #694d85; font-size: 22px; color: #fff; text-align: center;}
/*.help_notice h2 span{height:30px;width:30px;float:left;background:url(/images/nsc/skin/blue_skin/main/sc_ggtz_bg.png) no-repeat 0 -60px;}*/

.help_notice_r { width:310px; float: left;}
.help_notice_r .newsPages { background: url("/images/nsc/notice_sidebar_yybg.png") repeat-y left top;}
.notice_sidebar_box { background: url("/images/nsc/notice_sidebar_yybg.png") repeat-y left top; border-right: 1px solid #cfc0d9; height: 495px; -moz-box-shadow:inset -2px -3px 3px #e1deec; -webkit-box-shadow:inset -2px -3px 3px #e1deec; box-shadow:inset -2px -3px 3px #e1deec;}
#newsList{}
#newsList li{ margin-bottom:5px;}
#newsList li font.text { width:70%; line-height: 40px; font-size: 13px; float:left; white-space:nowrap; text-overflow:ellipsis; display:inline-block; overflow:hidden;}
#newsList li span.st { width:30%; line-height: 40px; font-size: 12px; float:right; color: #8e8e8e;}
#newsList li a { height: 40px; padding: 0 0 0 15px; display: block; color: #000; overflow:hidden;}
body #newsList .li_avtie { background: url("/images/nsc/notice_sidebar_current-bg.png") no-repeat left top;}
body #newsList .li_avtie a,#newsList li.li_avtie span { color:#000;}
body #newsList .li_avtie a { font-weight:bold;}
body #newsList .li_avtie a span { font-weight:normal;}


/*20151027*/
.orange { color:#fc7c23;}
.orange:hover { color:#fc7c23;}
.red { color:#ee0000;}
.green { color:#129c00;}
.gray { color:#888;}
.font_blue { color:#694d85;}
/*提款第一第二步骤*/
.input_kuang1 { border:1px solid #bbb; width:250px; height:39px; line-height:36px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset; -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px; color:#bbb; font-family:'Microsoft Yahei';}
.input_kuang1 input { border:0; background:none; height:39px; line-height:39px; width:96%; color:#bbb; font-family:'Microsoft Yahei'; font-size:14px; padding:0 0 0 8px;}
.db_tips_txt { margin-top:10px; font-size:12px; color:#bbb;}
.input_kuang1 option { color:#bbb; }
a.btn_orange { background-color:#fc7c23; border:1px solid #f6751b; color:#fff;}
a.btn_gray { background-color:#e5e5e5; border:1px solid #e5e5e5; color:#666;}
.btn { font-size:14px; line-height:20px; border-radius:3px; text-align:center; display:inline-block; font-weight:bold; font-family:'Microsoft Yahei'; cursor:pointer;}

.dw_main1 { padding:20px 40px; font-family:'Microsoft Yahei';}
.dw_step { border-bottom:1px solid #eee; padding:23px 0; font-family:'Microsoft Yahei'; margin:0 0 35px 0; overflow:hidden;}
.dw_step ul li { background:url("/images/nsc/skin/blue_skin/list/sc_drawings_tit-jt.png") no-repeat right 5px; padding-right:66px; margin-right:60px; height:47px; color:#bbb; font-size:24px; float:left;}
.dw_step ul li span { display:inline-block; background:url("/images/nsc/skin/blue_skin/list/sc_drawings_jdbg.png") no-repeat 0 -67px; width:47px; height:47px; line-height:47px; text-align:center; font-weight:bold; margin-right:10px;}
.dw_step ul li.last { padding:0; margin:0; background:none;}
.dw_step ul li.current { color:#fc7c23;}
.dw_step ul li.current span { background:url("/images/nsc/skin/blue_skin/list/sc_drawings_jdbg.png") no-repeat;}

.dw_list_table { font-family:'Microsoft Yahei'; font-size:14px; color:#666; margin:0 auto;}
.dw_list_table tr { padding:10px 0;}
.dw_list_table th { font-weight:normal; text-align:right; vertical-align:top; padding:10px 0; line-height:20px;}
.dw_list_table td { padding:10px 0 10px 20px; line-height:20px;}
.dw_list_table .x_line { display:inline-block;}
.dw_list_table .x_line2 { display:inline-block; margin-top:8px;}
.dw_main1 .dw_name { font-size:16px; padding-left:8px;}
.dw_main1 .dw_amount { font-size:18px; padding-left:8px; font-weight:bold;}
.dw_main1 .dw_gy { font-size:14px; padding-left:8px;}
.warning { height:32px; background:#fffbe5 url("/images/nsc/skin/blue_skin/list/sc_drawings_jdbg.png") no-repeat 10px -184px; padding:0 0 0 35px; border:1px solid #f5e187; line-height:32px; color:#666; font-size:14px; font-family:'Microsoft Yahei'; margin-bottom:20px;}
.warning span { font-weight:bold; margin:0 5px;}

.submit_area { border-top:1px solid #eee; margin-top:35px; text-align:center; padding-top:32px; font-family:'Microsoft Yahei';}
.submit_area .btn { font-family: Microsoft Yahei; background:url(/images/nsc/skin/blue_skin/list/saveBtn2.png) no-repeat; width:201px; height:56px; font-size: 22px; line-height: 54px; color: #fff; display:inline-block; border:0;}
.submit_area .btn_sqlist { display:inline-block; border-bottom:1px dotted #fc7c23; font-size:14px; line-height:20px; margin-left:40px;}
.submit_area .btn_txt_syb { display:inline-block; color:#bbb; font-size:18px; line-height:26px; height:25px; margin-right:40px; background:url("/images/nsc/icon_return.png") no-repeat; padding-left:35px;}

/*提款成功*/
.dw_result_box { width:680px; height:400px; position:relative; margin:0 auto;}
.dw_result { padding:0 0 0 85px; width:200px; height:65px; margin:-32px 0 0 -140px; position:absolute; left:50%; top:40%;}
.dw_result h2 { font-size:24px; font-weight:normal; margin-bottom:8px;}
.dw_result .p_txt { font-size:14px;}
.dw_result .p_txt a { color:#666; display:inline-block; border-bottom:1px dotted #bbb; line-height:20px; margin:0 30px 0 0;}
.succeed { background:#f7fff4; border:1px solid #dcebd7;}
.succeed .dw_result { color:#129c00; background:url("/images/nsc/skin/blue_skin/list/sc_drawings_jdbg.png") no-repeat -593px 0;}
.error { background:#fffef4; border:1px solid #ebe3d7;}
.error .dw_result { color:#ee0000; background:url("/images/nsc/skin/blue_skin/list/sc_drawings_jdbg.png") no-repeat -593px -75px;}

.autoskip { text-align:center; color:#bbb; font-size:14px; position:absolute; top:60%; width:100%;}

/*申请进度*/
.dw_sq_schedule { width:658px; min-height:450px; padding-top:20px; margin:20px auto 0; background:url("/images/nsc/skin/blue_skin/list/sc_drawings_jdbg-sx.png") no-repeat 6px -80px; font-family:'Microsoft Yahei';}
.schedule_item { padding:0 0 30px ;}
.schedule_item h3 { background:url("/images/nsc/skin/blue_skin/list/sc_drawings_jdbg.png") no-repeat 0 -275px; height:38px; line-height:38px; font-size:18px; color:#666; font-weight:normal; padding:0 0 0 50px; margin-bottom:15px;}
.item_current h3 { background:url("/images/nsc/skin/blue_skin/list/sc_drawings_jdbg.png") no-repeat 0 -227px; color:#fff;}
.schedule_item .li { padding:10px 0 10px 50px; line-height:20px;}
.schedule_item .li p { margin-top:6px;}
.schedule_item .li p.time { font-size:14px; color:#bbb;}
.schedule_item .state1 { font-size:20px; color:#bbb; font-weight:bold;}
.schedule_item .state2 { font-size:20px; color:#fc7c23; font-weight:bold;}
.schedule_item .state3 { font-size:13px; color:#bbb;}
.schedule_item .state_green { font-size:20px; color:#129c00; font-weight:bold;}
.schedule_item .state_red { font-size:20px; color:#ee0000; font-weight:bold;}
.ck_table { font-size:14px; color:#bbb; margin-top:6px;}
.schedule_submit { text-align:right; margin-top:50px;}
.schedule_submit .btn { padding:6px 20px; font-size:14px; font-weight:normal;}
.schedule_submit a { margin-left:10px;}

/*充提记录*/
.froms_search { background:#f8f8f8; font-size:14px; color:#888; font-family:'Microsoft Yahei'; margin-bottom:15px; border-radius:8px; border:1px solid #f2f2f2;}
.froms_search .search_tit_icon { background:url("/images/nsc/skin/blue_skin/list/forms_search_titicon.png") no-repeat center center; width:84px; border-right:1px solid #e8e8e8; min-height:50px;}
.froms_search span,.froms_search div { display:inline-block;}
.froms_search .btn_confirm { margin:0 0 0 10px!important;}
.tr_div1 { border-bottom:1px dashed #ddd; padding:14px 0 14px 12px;}
.tr_div1 .t,.tr_div2 .t { padding:0 5px 0 0;}
.tr_div1 .z,.tr_div2 .z { padding:0 9px;}
.tr_div2 { padding:14px 12px;}
.shortcut_time { padding:0 15px 0 0;}
.shortcut_time a { width:60px; height:26px; display:inline-block; line-height:26px; text-align:center; background:#fff; border:1px solid #dedede; border-radius:26px; font-size:13px; -moz-box-shadow:2px 2px 4px #ddd; -webkit-box-shadow:2px 2px 4px #ddd; box-shadow:2px 2px 4px #ddd; margin-right:5px; color:#888;}
.shortcut_time a.current { box-shadow: inset 2px 2px 3px #ddd; font-weight:bold; color:#fc7c23;}
.calendar_input_kuang1 { position:relative; border:1px solid #bbb; background:#fff; width:180px; height:30px; line-height:30px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset; -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px; color:#bbb; font-family:'Microsoft Yahei';}
.calendar_input_kuang1 input { border:0; background:none; height:30px; line-height:30px; width:164px; color:#666; font-family:'Microsoft Yahei'; font-size:14px; padding:0 8px;}
.calendar_icon { position:absolute; top:6px; right:7px; width:15px; height:17px; display:block; background:url("/images/nsc/calendar_icon.png") no-repeat; cursor:pointer;}
.check-box {}
.check-box input { display:none;}
.check-box label { padding:0 15px 0 0;}
.check-box .on { background:#fff url("/images/nsc/skin/blue_skin/list/forms_search_titicon2.png") no-repeat; width:18px; height:18px; display:inline-block; margin:0 5px 0 0; vertical-align:-3px;}
.check-box .no { background:#fff url("/images/nsc/skin/blue_skin/list/forms_search_titicon2.png") no-repeat 0 -38px; width:18px; height:18px; display:inline-block; margin:0 5px 0 0; vertical-align:-3px;}

.table_padding {}
/*table*/
.public_table { font-family:'Microsoft Yahei'; border-top:1px solid #dbdddf; border-left:1px solid #dbdddf; color:#666;}
.public_table thead { background-color:#f6f6f6;}
.public_table thead th { border-right:1px solid #dbdddf; border-bottom:1px solid #dbdddf; font-weight: bold;background:#f6f6f6; color:#3e3e3e; text-align:center; padding: 12px 0; font-size: 15px;}
.public_table tbody td { padding:10px 0; line-height:20px; font-size:14px; text-align:center; border-bottom:1px solid #dbdddf; border-right:1px solid #dbdddf;}
.public_table tbody td a { text-decoration:underline;}

.grayTable { background:#fff; border-top:1px solid #dbdddf; border-left:1px solid #dbdddf; border-collapse:collapse;}
.grayTable tr{ background: #fff;}
.grayTable tr th{ border-right:1px solid #dbdddf; border-bottom:1px solid #dbdddf; font-weight: bold;background:#f6f6f6; color:#3e3e3e; text-align:center; padding: 12px 0; font-size: 15px;}
.grayTable tr td { border-right:1px solid #dbdddf; border-bottom:1px solid #dbdddf; text-align:center; line-height:24px; color: #666; padding: 10px 0; }
.grayTable tr td a { color: #666;}
.grayTable tr td a:hover { color:#f96905;}
.grayTable tr td .noCheck{ color:#ff0000;}
.grayTable tr td .Check{ color:#bbb;font-size: 12px;background-color: #f3f3f3;position: absolute; width:50px; right: 10px;}
.grayTable tr td .fandianinput { text-align:right; vertical-align:1px; color:#ff0000;}
.grayTable tr:hover{}
.grayTable tr:nth-child(2n){}
.grayTable input { /* vertical-align:middle; */ /* padding:8px 3px; */ /* border:1px solid #bbb; */ /* font-size:14px; */height: 30px;line-height: 25px;}
.grayTable .red {color: #e2420a;}
.grayTable .green { color: #2fab12;}
.grayTable .blue { color: #0b7bc3;}
.grayTable .orange { color: #ff6600;}
.fandianinput_tit{ padding: 0 5px; height: 32px; text-align: center; line-height: 32px; border-radius: 4px; font-size: 18px; color:red; border: 1px solid #bbb; margin-right:5px;}
.fandianinput{ border:0!important;}

/*配额设定*/
.peieseding_table td { padding:13px 0 13px 20px!important;}
.peieseding_table td.u_add_zl { width:auto!important;}
.peieseding_table td input { width:100px; text-align:center; margin:0 3px;}
.peieseding_table .text_hint { color:#999;}
.peieseding_table .text_hint b { font-size:14px; margin:0 3px; color:#ff0000;}
.submit_top-box { float:left; margin:10px 0 0 0;}

/*玩法介绍*/
.grayTable2 {border-right:1px solid #d8d8d8;border-bottom:1px solid #d8d8d8;margin-bottom:10px;}
.grayTable2 h2 { color:#222;}
.grayTable2 th,.grayTable2 td {border-left:1px solid #d8d8d8; border-top:1px solid #d8d8d8; line-height:20px; text-align:center;}
.grayTable2 th { padding:8px; background-color:#eee; font-weight:bold;}
.grayTable2 td { padding:13px 8px; font-size:14px; color:#393939;}

/*分页*/
.paging { font-family:'Microsoft Yahei'; text-align:right; padding:20px 0; color:#666;}
.paging a,.paging span { background:#f6f6f6; border:1px solid #dbdddf; padding:6px 10px; display:inline-block; border-radius:3px; margin-left:6px; font-size:13px; color:#666;}
.paging a.current,.paging span.current { background:#fc7c23; border:1px solid #fc7c23; color:#fff;}
.paging a:hover { background:#dbdddf;}

/*完善资料*/
.organizing-data_box { border:1px solid #dcdcdc; width:780px; margin:40px auto 50px; padding:30px 0;}
.organizing-data_box .text_hint { display:inline-block;}
.organizing-data_box .text_hint-c { vertical-align:-9px;}

/*密码修改页面*/
#dna_ques_1 { font-size:14px;}
#changeloginpass #tabs-1,#changeloginpass #tabs-2 { border:1px solid #dcdcdc; width:680px; margin:20px auto 50px; padding:60px 0;}
.tdz3_left { width:200px; text-align: right!important; font-size:14px; color:#666;}
.tdz3_left font.red { color:#ff0000; margin-right:10px;}
.tdz3_right { text-align: left!important; padding:5px 0 5px 10px!important;}
.tdz3_right input { width:220px; vertical-align:middle; padding:8px 3px; border:1px solid #bbb; font-size:14px; color:#666; -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset; font-family:'Microsoft Yahei';}
.tdz3_right .select_text { width:227px; -moz-border-radius:3px; -webkit-border-radius:3px; border-radius:3px; -moz-box-shadow:2px 2px 2px #eee inset; -webkit-box-shadow:2px 2px 2px #eee inset; box-shadow:2px 2px 2px #eee inset;}
#changeloginpass .list_page { padding-left:96px;}
#changeloginpass .text_hint { display:inline-block;}
#changeloginpass .text_hint-c { vertical-align:-9px;}

/*消息管理详情页*/
.messages_details { width:960px; margin:30px auto 0;}
.messages_details_tit { background:url("/images/nsc/messages_mailer-topbg.png") no-repeat; width:960px; height:72px;}
.messages_details h3 { font-size:24px; font-weight:bold; color:#694d85; line-height:24px; padding:30px 0 0 0; text-align:center;}
.messages_details .messages_details_txt { color:#494949; font-size:16px; line-height:28px; padding:20px 40px; border-left:1px solid #e2dfe6; border-right:1px solid #e2dfe6;}
.messages_details .messages_details_time { text-align:right; font-size:13px; color:#aaa; padding:15px 40px 30px 0; border:1px solid #e2dfe6; border-top:1px dashed #e2dfe6; font-family:Arial, Helvetica, sans-serif; border-radius:0 0 8px 8px;}

/*余额查询*/
.check-balance_box { width:860px; margin:0 auto; padding:80px 0 80px 100px;}
.check-balance_box dl { float:left; padding:0 0 0 105px; background:url("/images/nsc/icon_check-balance.png") no-repeat; height:110px;}
.check-balance_box dl dt { font-size:24px; color:#eb4308; height:24px;}
.check-balance_box dl.user dt { margin:18px 0 10px;}
.check-balance_box dl.sum dt { margin:0 0 10px;}
.check-balance_box dl.sum p { height:18px; font-size:14px; color:#937eb0;}
.check-balance_box dl dd { font-size:15px; color:#937eb0; white-space:nowrap;}
.check-balance_box dl dd i { font-style:normal; color:#ac9fbe;}
.check-balance_box .user { width:235px; background-position:0 0;}
.check-balance_box .sum { width:395px; background-position:0 -120px;}
.check-balance_loading { text-align:center; font-size:18px; padding:80px 0; color:#494949;}

/*常见问题*/
#appraise{padding:0 30px;}
#appraise h2{background:#a1638c;color:#fff;display:inline-block;font-size:14px;font-weight:normal;padding:2px 5px;}
#appraise p{margin-bottom:20px;font-size:14px;line-height:20px;margin-top:5px;}

/*如何存款*/
.p_con{padding:0 15px;}
.p_con h2{font-size:16px;color:#694d85;margin:15px 0;}
.p_con p{font-size:14px;}
.p_con .tip{color:#e46a35;margin-top:10px;}
.p_con p span{color:#E01818;}

/*下级充值*/
.subordinate { border:1px solid #dcdcdc; width:646px; margin:20px auto 50px; padding:60px 0; font-size:14px; color:#666;}
.subordinate .money { font-size:20px; color:#fc7c23;}
.subordinate .tips { font-size:12px;}
.subordinate .tips b { color:#fc7c23;}
.subordinate .a-back_list { display:inline-block;}
.subordinate .a-back_list:hover { color:#353434;}

.fdsd_top td { background:#f8f5f5; font-size:16px;}
.fdsd_top td b {}

/*绑定手机号码*/
.list_btn_box b { font-size: 18px; vertical-align: middle; }
.list_btn_box .phone_number { height: 20px; line-height: 20px; border: 1px solid #ccc; padding:2px; width: 270px; font-size: 14px; }
.list_btn_box .formZjbd-disabled { background:url("/images/nsc/btn-reset_bg.png") no-repeat; cursor: default; }
.list_btn_box p { font-size: 12px; font-weight: bold; color: red; }
.list_btn_box span { background: #fff; border: none; }
.list_btn_box .success { color:green; }
.list_btn_box .error { color:red; }
.list_btn_box .loading { background:url("/images/nsc/loading.gif") no-repeat; display: inline-block; width: 16px; height: 16px;  vertical-align: middle; }

/*站内信样式调整*/
.grayTable .msgsms { background-color:#fafafa; color:#767676!important;}
.grayTable .msgsms .msgico {   display: inline-block; width: 13px;height:13px;background: url(/images/nsc/home/<USER>
.grayTable .msgsms .msgtitle,.grayTable .msgsms .msgdate{ font-weight:bold; color:#939393;}
.grayTable .msgCheck { position:relative;}
.grayTable .msgsms .msgsdel,.grayTable tr td a.msgsdel { color:#bbb; font-weight:none!important; font-size:12px;}
.grayTable tr td a.msgsdel:hover { color:#ff0000;}

/*推送二期样式调整*/
.procode .task_div { right:auto !important;}

/* 推广设定样式2016/8/15 */
.clearfix:after{display: block; content: "clear"; height: 0; clear: both; overflow: hidden; visibility: hidden;}
.tgsd { border: 1px solid #dbdddf;padding: 10px; margin-bottom: 10px; background:url(/images/nsc/tgsd_bg.jpg) repeat-x top;}
.tg_left {width: 60%; min-width: 600px;float:left;}
.tg_left h3 { padding:5px; font-size:16px;}

.tg_right {width: 35%; min-width: 259px;float:right;}
.tg_right h3 { margin-left:12px; font-size:16px;}
.tg_right .tg_img {background: #fff; border: 2px solid #e0e0e0; padding: 9px; width: 109px; height: 109px; margin:10px; float:left;}
.tg_right .tg_con { padding-top:10px; float:left; width:170px; font-size:13px; color:#ff0000;}
.tg_right .tg_con .zz_1 { color:#666; margin-bottom:8px; font-size:12px;}

/*20161004推广设定*/
.form_switch_main {}
.form_switch_head { background:#faf7fd url("/images/nsc/img_logo_shading.png") no-repeat right bottom; border: 1px solid #e7e0ee; padding: 0px 0 0px 100px; margin-bottom: 65px;}
.form_switch_head .form_item { margin-bottom: 45px;}
.form_switch_head .item_left { float: left; width: 140px; font-size: 15px; color: #888; line-height: 32px;}
.form_switch_head .item_right { float: left;}

.switch_choose {}
.switch_choose label { cursor: pointer; background: #fff; float: left; display: inline-block; margin-left: -1px; border: 1px solid #d0d0d0; width: 166px; height: 30px; line-height: 30px; color: #666; font-size: 16px; text-align: center;}
.switch_choose label.bk_l { /* border-radius:8px 0 0 8px; */}
.switch_choose label.bk_r { /* border-radius:0 8px 8px 0; */}
.switch_choose input[type='radio'] { width:0; height:0; opacity:0; filter:alpha(opacity=0);}
.switch_choose label.active{background: #f96905; border: 1px solid #f96905; color: #fff;}
/*
css3 radio选择样式 不兼容IE8
.switch_choose input[type='radio']:checked+label{background: #f96905; border: 1px solid #f96905; color: #fff;}
*/
.entry_one input { text-indent: 10px; font-size: 18px; color: #f96905; width: 500px; height: 40px; border: 1px solid #d0d0d0; margin-right: 10px; border-radius:4px;box-shadow:2px 2px 2px #eee inset;}
.entry_two input { text-indent: 10px; font-size: 15px; color: #aaa; width: 241px; height: 40px; border: 1px solid #d0d0d0; margin-right: 10px; border-radius:4px;box-shadow:2px 2px 2px #eee inset;}
.drop-down select { text-indent: 10px; font-size: 15px; color: #666; width: 500px; height: 40px; border: 1px solid #d0d0d0; margin-right: 10px; border-radius:4px;box-shadow:2px 2px 2px #eee inset;}

.color_orange { color: #f96905;}
.rebate_fp { font-size: 13px; color: #694d85;}
.rebate_zs { font-size: 28px; font-weight: bold; color: #f96905;}
.form_submit_box { position: relative;}
.form_submit_box .button { position: absolute; bottom: -23px; left: 50px; width: 200px; height: 45px; font-size: 22px!important; border-radius:45px; font-weight: bold; line-height: 45px; color: #fff; background: #F96905; text-align: center;}

.form_submit_box1 .button1 { position: absolute; bottom: -23px; left: 251px; width: 200px; height: 45px; font-size: 22px!important; border-radius:45px; font-weight: bold; line-height: 45px; color: #fff; background: #8D8B98; text-align: center;
.warm-prompt_bottom { font-size: 14px; color: #666; padding: 20px 0 30px 88px; line-height: 30px;}
.warm-prompt_bottom h5 { font-weight: bold; font-size: 16px;}

.popularize_list {}
.popularize_list ul {}
.popularize_list ul li { background: #faf7fd; border-top: 3px solid #eae6ef; padding: 25px 0 25px 88px; margin-bottom: 20px;}
.popularize_list .pl_l { float: left; width: 62%; color: #888; font-size: 15px;}
.popularize_list .pl_l h3 { color: #333; font-weight: bold; font-size: 18px;}
.popularize_list .pl_l_item { margin-top: 20px; line-height: 16px;}
.popularize_list .pl_l_item .w_1 { width: 230px; display: inline-block;}
.btn_details { float: right; margin-top: -50px; font-size: 18px; color: #f96905; font-weight: bold; width: 130px; height: 40px; text-align: center; line-height: 40px; border: 1px solid #f96905; border-radius: 8px; display: inline-block; position:relative; cursor:pointer;}
.btn_details:hover { background: #f96905; color: #fff;}

.popularize_list .pl_r { float: right; width: 35%; border-left: 1px dotted #eae6ef;}
.sjtg_img_box { background: #fff; border: 1px solid #e1dee6; width: 109px; margin: 0 auto; padding: 10px; text-align: center; color: #666;}
.sjtg_img_box .sjtg_img { width: 109px; height: 109px; margin-bottom: 8px;}

.tz_details_shot { display:none; width: 730px; padding-bottom: 11px; border-radius: 6px; color: #fff; overflow: hidden; position: absolute; left:-300px; bottom:41px;  cursor:text;}
.tz_details_shot .tz_icon_sj { background: url("/images/nsc/tz_icon_sj.png"); width: 13px; height: 11px; display: block; position: absolute; bottom:0; left: 50%; margin-left: -7px;}
.tz_details_shot dl{ background: #877897}
.tz_details_shot dt { text-align: center; font-size: 16px; font-weight: bold; line-height: 16px; padding: 18px 0; margin-top: -1px; border-top: 1px solid #9e91ab; border-bottom: 1px solid #9e91ab;}
.tz_details_shot dd { font-size: 15px; line-height: 16px;}
.tz_details_shot dd span { float: left; display: inline-block; width: 33.1%; padding: 15px 0; margin: -1px -1px 0 0 ; border: 1px solid #9e91ab; text-align: left}
.tz_details_shot dd .w_2 { width: 50%; padding-left: 13%; display: inline-block; font-style: normal;}

.btn_details:hover .tz_details_shot{ display:block; }
.tg_right .tg_con .zz_1 { color:#666; margin-bottom:8px; font-size:12px;}
