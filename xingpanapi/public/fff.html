<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>页面错误提示</title>
    <style>
        body, html {
            background-color: black;

        }
        #sun {
            width: 90px;
            height: 90px;
            border-radius: 100px;
            position: absolute;
            background: blue;
            border-color: blue;
            left: 46.5vw;
            top: 46.5vh;
            z-index: 1;
            background-image: radial-gradient(yellow, orange, orange);
        }
        #orbity1 {
            width: 10px;
            height: 10px;
            position: absolute;
            background: blue;
            border-color: blue;
            left: 50vw;
            top: 50vh;
        }
        #planet1 {
            width: 10px;
            height: 10px;
            border-radius: 50px;
            position: absolute;
            background-image: radial-gradient(#504E51, #97979F);
        }
        #orbity2 {
            width: 10px;
            height: 10px;
            position: absolute;
            background: red;
            border-color: blue;
            left: 50vw;
            top: 50vh;
        }
        #planet2 {
            width: 20px;
            height: 20px;
            border-radius: 50px;
            position: absolute;
            background-image: radial-gradient(#BBB7AB, orange);
        }
        #orbity3 {
            width: 10px;
            height: 10px;
            position: absolute;
            background: blue;
            border-color: blue;
            left: 50vw;
            top: 50vh;
        }
        #planet3 {
            width: 20px;
            height: 20px;
            border-radius: 50px;
            position: absolute;
            background-image: radial-gradient(green, blue);
        }
        #orbity4 {
            width: 10px;
            height: 10px;
            position: absolute;
            background: red;
            border-color: blue;;
            left: 50vw;
            top: 50vh;
        }
        #planet4 {
            width: 15px;
            height: 15px;
            border-radius: 50px;
            position: absolute;
            background-image: radial-gradient(#E27B58, #C67B5C);
        }
        #orbity5 {
            width: 10px;
            height: 10px;
            position: absolute;
            background: blue;
            border-color: blue;
            left: 50vw;
            top: 50vh;
        }
        #planet5 {
            width: 40px;
            height: 40px;
            border-radius: 50px;
            position: absolute;
            background-image: linear-gradient(#90614D, #C88B3A, #90614D, #C88B3A);
        }
        #orbity6 {
            width: 10px;
            height: 10px;
            position: absolute;
            background: red;
            border-color: blue;;
            left: 50vw;
            top: 50vh;
        }
        #planet6 {
            width: 40px;
            height: 40px;
            border-radius: 50px;
            position: absolute;
            background-image: linear-gradient(#c3924f, #e2bf7d, #c3924f, #e2bf7d);
        }
        #orbity7 {
            width: 10px;
            height: 10px;
            position: absolute;
            background: blue;
            border-color: blue;
            left: 50vw;
            top: 50vh;
        }
        #planet7 {
            width: 30px;
            height: 30px;
            border-radius: 50px;
            position: absolute;
            background-image: radial-gradient(#D5FBFC, blue);
        }
        #orbity8 {
            width: 10px;
            height: 10px;
            position: absolute;
            background: red;
            border-color: blue;;
            left: 50vw;
            top: 50vh;
        }
        #planet8 {
            width: 30px;
            height: 30px;
            border-radius: 50px;
            position: absolute;
            background-image: radial-gradient(#3E66F9, #212354);
        }
    </style>
</head>
<body>
<div id="sun"></div>
<div id="orbity1">
    <div id="planet1"></div>
</div>
<div id="orbity2">
    <div id="planet2"></div>
</div>
<div id="orbity3">
    <div id="planet3"></div>
</div>
<div id="orbity4">
    <div id="planet4"></div>
</div>
<div id="orbity5">
    <div id="planet5"></div>
</div>
<div id="orbity6">
    <div id="planet6"></div>
</div>
<div id="orbity7">
    <div id="planet7"></div>
</div>
<div id="orbity8">
    <div id="planet8"></div>
</div>
<script>
    function Orbity(radius, planet_name, velocity, sizeplanet) {
        var planet_name = document.getElementById(planet_name);
        var pos = 0;
        var id = setInterval(frame, 1);
        var coordx = "";
        var coordy = "";
        function frame() {
            //if (pos == 2*Math.PI) {
            //} else {
            pos = pos + (Math.PI * (velocity / 20000));
            x = Math.cos(pos) * radius - sizeplanet / 2;
            y = Math.sin(pos) * radius - sizeplanet / 2;
            planet_name.style.left = x + 'px';
            planet_name.style.top = y + 'px';
            // }
        }
        return null;
    }
    document.getElementById("planet1").innerHTML = Orbity(100, "planet1", 47.87, 10);
    document.getElementById("planet2").innerHTML = Orbity(125, "planet2", 35.02, 20);
    document.getElementById("planet3").innerHTML = Orbity(150, "planet3", 29.78, 20);
    document.getElementById("planet4").innerHTML = Orbity(175, "planet4", 24.08, 15);
    document.getElementById("planet5").innerHTML = Orbity(250, "planet5", 13.07, 40);
    document.getElementById("planet6").innerHTML = Orbity(300, "planet6", 9.69, 40);
    document.getElementById("planet7").innerHTML = Orbity(350, "planet7", 6.81, 30);
    document.getElementById("planet8").innerHTML = Orbity(400, "planet8", 5.43, 30);
</script>
</body>
</html>