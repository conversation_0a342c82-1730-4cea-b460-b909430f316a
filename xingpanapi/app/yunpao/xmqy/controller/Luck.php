<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\yunpao\controller;
use lunar\Solar;
/**
 * 日月年运控制器
 */
class Luck extends ApiBase
{
    //日运
    public function day()
    {
        //开始计算流年数据
        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        if (empty($this->param['longitude'])) {
            $this->param['longitude']='123';
        }
        if (empty($this->param['latitude'])) {
            $this->param['latitude']='61';
        }
        !isset($this->param['tz']) && $this->param['tz'] = 8;
        empty($this->param['tz']) && $this->param['tz'] = 8;
        $param = $this->param;
        $param['h_sys'] = 'P';

        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $param['h_sys']='D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        $birthday = $param['birthday'];
        $time_zone = $param['tz'];
        $birth_points = $param['longitude'] . ',' . $param['latitude'];
        $is_summer = 0;

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 - $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '0123456789',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        //$param['date']="2024-05-29 12:0:0";
        if (empty($param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($param['date']);
        }


        $new_birth_time = $date_ime - $time_zone * 3600 - $is_summer * 3600;

        $new_conditions = [
            'b' => date('d.m.Y', $new_birth_time),
            'p' => '01234',
            'house' => $birth_points . ',P',
            'ut' => '12:00:00',
            'f' => 'Plsj',  //名字 经度度数 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        $data=$this->logicDayLuck->basicData($life_arr,$new_conditions);

        $corpus=$data['corpus'];

        // 使用 usort 函数进行排序
        usort($corpus, function($a, $b) {
            return $a['difference'] - $b['difference'];
        });

        $year = date('Y',$date_ime);
        $month = date('m',$date_ime);
        $day = date('d',$date_ime);

        $solar = Solar::fromYmdHms($year, $month, $day, 11, 0, 0);
        $lunar = $solar->getLunar();
        $yi =$lunar->getDayYi();
        $ji =$lunar->getDayJi();

        $yi=array_splice($yi,0,2);
        $ji=array_splice($ji,0,2);

        $data['good'] =implode(',',$yi);
        $data['ji'] =implode(',',$ji);

        $data['describe_array'] = $corpus[0];

        $luckyDuckDay = $this->yiji($date_ime, $life_birth_time);
        $data['lucky_number'] = $luckyDuckDay['lucky_number'];
        $data['lucky_flower'] = $luckyDuckDay['lucky_flower'];
        $data['lucky_color'] = $luckyDuckDay['lucky_color'];
        $data['lucky_color_str'] = $luckyDuckDay['lucky_color_str'];
        $data['lucky_stone'] = $luckyDuckDay['lucky_stone'];

      //  $data['describe'] = '今日很多事在忙，你的社会关系能够让你看到自己的情绪是如何影响他人的，这会让你陷入感官冲突。这也会造成你无法正确表达自己的情感。';

      //  $data['title'] = '多看书，多交流';

        $data['love_contrast']=1;  //1升  0平  -1降
        $data['wealth_contrast']=0;
        $data['healthy_contrast']=-1;

        $new_conditions['b'] = date('d.m.Y', ($new_birth_time-86400));
        $data2=$this->logicDayLuck->basicLastData($life_arr,$new_conditions);

        if (!empty($data2)){
            foreach ($data2 as $sgsg=>$vsfg){
                //降低
                if($vsfg>$data[$sgsg]){
                    $data[$sgsg.'_contrast']=-1;
                } elseif($vsfg==$data[$sgsg]){
                    $data[$sgsg.'_contrast']=0;
                } else{
                    $data[$sgsg.'_contrast']=1;
                }
            }
        }




        return $this->apiReturn($data);
    }
    //周运
    public function weeks()
    {
        //开始计算流年数据
        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if (empty($this->param['longitude'])) {
            $this->param['longitude']='123';
        }
        if (empty($this->param['latitude'])) {
            $this->param['latitude']='61';
        }
        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;
        $param['h_sys'] = 'P';

        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $param['h_sys']='D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        $birthday = $param['birthday'];
        $time_zone = 8;
        $birth_points = $param['longitude'] . ',' . $param['latitude'];
        $is_summer = 0;

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 - $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '0123456789',
            'house' => $birth_points.','.$param['h_sys'],
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        if (empty($param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($param['date']);
        }


        $new_birth_time = $date_ime - $time_zone * 3600 - $is_summer * 3600;

        $new_conditions = [
            'b' => date('d.m.Y', $new_birth_time),
            'p' => '01234',
            'house' => $birth_points . ',P',
            'ut' => '12:00:00',
            'f' => 'Plsj',  //名字 经度度数 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        $data=$this->logicWeekLuck->basicData($life_arr,$new_conditions);

        $year = date('Y',$date_ime);
        $month = date('m',$date_ime);
        $day = date('d',$date_ime);
        $corpus=$data['corpus'];

        // 使用 usort 函数进行排序
        usort($corpus, function($a, $b) {
            return $a['difference'] - $b['difference'];
        });


        $data['describe_array'] = $corpus[0];



        return $this->apiReturn($data);
    }
    //月运
    public function moon()
    {
        //开始计算流年数据
        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if (empty($this->param['longitude'])) {
            $this->param['longitude']='123';
        }
        if (empty($this->param['latitude'])) {
            $this->param['latitude']='61';
        }
        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;
        $param['h_sys'] = 'P';

        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $param['h_sys']='D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        $birthday = $param['birthday'];
        $time_zone = 8;
        $birth_points = $param['longitude'] . ',' . $param['latitude'];
        $is_summer = 0;

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 - $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '0123456789',
            'house' => $birth_points.','.$param['h_sys'],
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $param['date']="2024-07-01 0:0:0";
        if (empty($param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($param['date']);
        }


        $chazhit = $date_ime - $time_zone * 3600 - $is_summer * 3600-$life_birth_time;
        $prog_timedd = $chazhit / (27.321582 * 86400);
        $new_birth_time=$life_birth_time+$prog_timedd*86400;

        $new_conditions = [
            'b' => date('d.m.Y', $new_birth_time),
            'p' => '01234',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => '12:00:00',
            'f' => 'Plsj',  //名字 经度度数 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        $data=$this->logicMoonLuck->basicData($life_arr,$new_conditions);

        $corpus=$data['corpus'];
        // 使用 usort 函数进行排序
        usort($corpus, function($a, $b) {
            return $a['difference'] - $b['difference'];
        });


        $corpus_lunarrer = $this->logicLunarreTurn->explainData(['birthday'=>$birthday,
            'birth_points'=>$birth_points,
            'is_summer'=>$is_summer,
            'time_zone'=>$time_zone,
            'current_date'=> date('Y-m-d H:i:s', $date_ime),
            'h_sys'=>'P']);
        $data['describe_array'] = $corpus[0];

        $data['corpus'] = array_merge(array($corpus_lunarrer), $data['corpus']);



        return $this->apiReturn($data);
    }
    //年运
    public function year()
    {
        //开始计算流年数据
        if (!isset($this->param['longitude']) or !isset($this->param['latitude']) or $this->param['longitude'] == '' or $this->param['latitude'] == '') {
            return ['code' => 1060002, 'msg' => 'j经纬度参数不能为空'];
        }
        if (empty($this->param['birthday'])) {
            return ['code' => 1060002, 'msg' => '出生日期填写错误'];
        }
        $param = $this->param;
        $param['h_sys'] = 'P';

        if ($this->param['latitude'] > 66.56 or $this->param['latitude'] < -66.56) {
            $param['h_sys']='D';
            //return ['code' => 1060002, 'msg' => '纬度在-66.56~66.56之间'];
        }

        $birthday = $param['birthday'];
        $time_zone = 8;
        $birth_points = $param['longitude'] . ',' . $param['latitude'];
        $is_summer = 0;

        $life_birth_time = strtotime($birthday) - $time_zone * 3600 - $is_summer * 3600;

        $utdatenow = date('d.m.Y', $life_birth_time);
        $utnow = date('H:i:s', $life_birth_time);
        $life_arr = [
            'b' => $utdatenow,
            'p' => '0123456789m',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        if (empty($param['date'])) {
            $date_ime = time();
        } else {
            $date_ime = strtotime($param['date']);
        }

        $year = date('Y',$date_ime);
        $targetDate = $year . "-06-15 12:00:00";
        $date_ime=strtotime($targetDate);

        $new_birth_time = $date_ime - $time_zone * 3600 - $is_summer * 3600;

        $new_conditions = [
            'b' => date('d.m.Y', $new_birth_time),
            'p' => '01234',
            'house' => $birth_points. ',' . $param['h_sys'],
            'ut' => '12:00:00',
            'f' => 'Plsj',  //名字 经度度数 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];


        $data=$this->logicYearLuck->basicData($life_arr,$new_conditions);

        $year = date('Y',$date_ime);
        $month = date('m',$date_ime);
        $day = date('d',$date_ime);
        $solar = Solar::fromYmdHms($year, $month, $day, 11, 0, 0);
        $lunar = $solar->getLunar();
        $yi =$lunar->getDayYi();
        $ji =$lunar->getDayJi();

        $yi=array_splice($yi,0,2);
        $ji=array_splice($ji,0,2);

        $data['good'] =implode(',',$yi);
        $data['ji'] =implode(',',$ji);


        $luckyDuckDay = $this->yiji($date_ime, $life_birth_time);
        $data['lucky_number'] = $luckyDuckDay['lucky_number'];
        $data['lucky_flower'] = $luckyDuckDay['lucky_flower'];
        $data['lucky_color'] = $luckyDuckDay['lucky_color'];
        $data['lucky_color_str'] = $luckyDuckDay['lucky_color_str'];
        $data['lucky_stone'] = $luckyDuckDay['lucky_stone'];



        $data['love_contrast']=1;  //1升  0平  -1降
        $data['wealth_contrast']=0;
        $data['healthy_contrast']=-1;

        $new_conditions['b'] = date('d.m.Y', ($new_birth_time-86400*365));
        $data2=$this->logicYearLuck->basicLastData($life_arr,$new_conditions);

        if (!empty($data2)){
            foreach ($data2 as $sgsg=>$vsfg){
                //降低
                if($vsfg>$data[$sgsg]){
                    $data[$sgsg.'_contrast']=-1;
                } elseif($vsfg==$data[$sgsg]){
                    $data[$sgsg.'_contrast']=0;
                } else{
                    $data[$sgsg.'_contrast']=1;
                }
            }
        }

        return $this->apiReturn($data);
    }
    //查找行星落入宫位
    public function getHouseId($house,$longitude){

        foreach ($house as $key=>$value){
            if ($key < 11) {
                $last_house = $house[$key + 1];
            } else {
                $last_house = $house[0];
            }
            if ($longitude >= $value['longitude'] and $longitude < $last_house['longitude']) {
                return $key + 1;
            }
        }
    }


    /**
     * 商城商品列表单条编辑
     */
    public function yiji($date_ime,$life_birth_time)
    {

        $data['lucky_number'] = 6;

        $lucky_day=date('dmY', $date_ime).date('dmY', $life_birth_time);

        chong:
        $lucky_day_array=str_split($lucky_day);
        $lucky_day=0;
        foreach ($lucky_day_array as $keyl=>$valuel){
            $lucky_day+=$valuel;
        }

        if($lucky_day>9){
            goto chong;
        }
        $data['lucky_number'] = $lucky_day;
        $data['lucky_color'] = '黄色';
        $data['lucky_bearing'] = '东北';

        $color_str[] = ["红色", "黄色", "绿色", "青色", "蓝色", "紫色", "黑色"];
        $color_str[] = ["朱红", "桔黄", "豆绿", "豆青", "天蓝", "紫罗兰色", "土黑"];
        $color_str[] = ["粉红", "深桔黄", "浅豆绿", "花青", "蔚蓝", "紫藤色", "棕色"];
        $color_str[] = ["梅红", "浅桔黄", "橄榄绿", "茶青", "月光蓝", "紫水晶色", "红棕"];
        $color_str[] = ["玫瑰红", "柠檬黄", "茶绿", "葱青", "海洋蓝", "葡萄紫", "金棕"];
        $color_str[] = ["桃红", "玉米黄", "葱绿", "天青", "海蓝", "茄皮紫", "铁锈棕"];
        $color_str[] = ["樱桃红", "橄榄黄", "苹果绿", "霁青", "湖蓝", "玫瑰紫", "桔棕"];
        $color_str[] = ["桔红", "樱草黄", "原野绿", "石青", "深湖蓝", "丁香紫", "橄榄棕"];
        $color_str[] = ["石榴红", "稻草黄", "森林绿", "铁青", "中湖蓝", "钴紫", "褐色"];
        $color_str[] = ["枣红", "芥末黄", "洋蓟绿", "蟹青", "浅湖蓝", "绛紫", "深褐"];
        $color_str[] = ["莲红", "杏黄", "苔藓绿", "鳝鱼青", "赤褐", "赤褐", "灰褐"];
        $color_str[] = ["灰色", "莲灰", "铅灰", "豆灰", "梅红", "蓝色", "绿色"];
        $color_str[] = ["银灰", "茶褐", "碳灰", "藕灰", "玫瑰红", "天蓝", "豆绿"];
        $color_str[] = ["铁灰", "紫褐", "驼灰", "天青", "桃红", "蔚蓝", "浅豆绿"];


        $color[] = ["#FF0000", "#FFFF00", "#008000", "#00FFFF", "#0000FF", "#800080", "#000000"];
        $color[] = ["#FF4500", "#FFA500", "#9ACD32", "#20B2AA", "#87CEEB", "#8A2BE2", "#8B4513"];
        $color[] = ["#FFC0CB", "#FF8C00", "#98FB98", "#7FFFD4", "#1E90FF", "#DDA0DD", "#A52A2A"];
        $color[] = ["#DDA0DD", "#FFE4B5", "#6B8E23", "#008B8B", "#ADD8E6", "#9370DB", "#A0522D"];
        $color[] = ["#FF69B4", "#FFFACD", "#008B8B", "#00FF7F", "#000080", "#8B008B", "#DAA520"];
        $color[] = ["#FFA07A", "#F0E68C", "#32CD32", "#40E0D0", "#0000CD", "#8B4513", "#B8860B"];
        $color[] = ["#C71585", "#BDB76B", "#00FF00", "#48D1CC", "#4169E1", "#BA55D3", "#CD853F"];
        $color[] = ["#FF4500", "#FFD700", "#228B22", "#008B8B", "#00008B", "#DDA0DD", "#6B8E23"];
        $color[] = ["#DC143C", "#F0E68C", "#228B22", "#008080", "#4682B4", "#483D8B", "#8B4513"];
        $color[] = ["#8B0000", "#FFD700", "#6B8E23", "#008080", "#87CEFA", "#8B008B", "#8B4513"];
        $color[] = ["#FFC0CB", "#FFD700", "#006400", "#008080", "#8B4513", "#8B4513", "#A52A2A"];
        $color[] = ["#808080", "#D3D3D3", "#778899", "#696969", "#DDA0DD", "#0000FF", "#008000"];
        $color[] = ["#C0C0C0", "#D2691E", "#696969", "#E6E6FA", "#FF69B4", "#87CEEB", "#9ACD32"];
        $color[] = ["#708090", "#8B4513", "#BDB76B", "#40E0D0", "#FFA07A", "#1E90FF", "#98FB98"];

        $flowers = array(
            array("牡丹", "月季", "兰花", "桂花", "菊花", "荷花", "水仙"),
            array("玫瑰", "百合", "茉莉", "杜鹃", "郁金香", "紫罗兰", "风信子"),
            array("夜来香", "丁香", "芍药", "君子兰", "蒲公英", "栀子花", "冬青"),
            array("桃花", "梅花", "素馨", "金盏花", "矢车菊", "薰衣草", "牛王花"),
            array("罂粟", "鸢尾花", "紫菀", "落新妇", "杜英", "玉兰", "塔菊"),
            array("穗穗金", "凌霄", "勿忘我", "满天星", "雏菊", "迎春花", "常春藤"),
            array("蓝莓花", "牵牛花", "金鸡菊", "木蓝", "桔梗", "翠菊", "薹草"),
            array("七里香", "油桐花", "洋水仙", "石斛兰", "龙胆", "虞美人", "凤仙花"),
            array("蝴蝶花", "火鹤花", "曼陀罗", "金盏黄", "黄槐花", "柳兰", "百日菊"),
            array("唐菖蒲", "海桐花", "葵花", "安哥拉", "萱草", "风车草", "白香花"),
            array("芝樱", "飞燕草", "金花", "鸡冠花", "锦葵", "仙客来", "五色梅"),
            array("雏菊", "铁线莲", "夜来香", "寿菊", "秋桜", "云雾菊", "百子莲"),
            array("阳光菊", "银盏花", "丹参花", "金鱼草", "睡莲", "马利筋", "鹿蹄草")
        );
        $stones = array(
            array("长石", "黑曜石", "石英", "黄玉", "绿松石", "孔雀石", "青金石"),
            array("红玛瑙", "蓝玛瑙", "黄玛瑙", "紫晶", "白玉", "软玉", "葡萄石"),
            array("青玉", "橄榄石", "黄金矿", "碧玺", "锡矿", "绿耀石", "磷灰石"),
            array("猫眼石", "蛋白石", "蓝宝石", "红宝石", "翡翠", "舍丹石", "孔雀石"),
            array("镉矾", "粉晶", "象牙石", "氧化铝", "石榴石", "草绿宝石", "珍珠"),
            array("琥珀", "黑玄武岩", "刚玉", "斜长石", "和田玉", "金星石", "云母石"),
            array("玻璃石", "彩虹石", "钻石", "海蓝宝石", "蓝铜矿", "烟熏石", "石榴石"),
            array("橄榄石", "黄铜矿", "方解石", "硅石", "海蓝宝石", "镉黄矿", "坦桑石"),
            array("紫水晶", "玻璃石", "隕石", "辉石", "影青石", "石碱", "杂石矿"),
            array("石膏", "蜜蜡石", "月石", "硼石", "樟柏石", "雪花石", "朱砂"),
            array("花岗岩", "石墨", "滑石", "煤", "间砂岩", "石灰岩", "粉砂岩"),
            array("赭石", "黄铁矿", "云母石", "霞石", "蛇纹岩", "石英箨", "紫砂"),
            array("金刚砂", "孔雀石", "水滴石", "铁矿石", "白珪石", "光母石", "橄榄岩")
        );
        $data['lucky_color'] = $color[$lucky_day][date('w', $life_birth_time)];
        $data['lucky_color_str'] = $color_str[$lucky_day][date('w', $life_birth_time)];
        $data['lucky_flower'] = $flowers[$lucky_day][date('w', $life_birth_time)];
        $data['lucky_stone'] = $stones[$lucky_day][date('w', $life_birth_time)];
        return $data;
    }
}
