<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

/**
 * 应用公共（函数）文件
 */

use think\Db;
use think\Cache;
use think\Response;
use think\exception\HttpResponseException;
use app\common\logic\File as LogicFile;


// +---------------------------------------------------------------------+
// | 系统相关函数
// +---------------------------------------------------------------------+

/**
 * 检测用户是否登录
 * @return integer 0-未登录，大于0-当前登录用户ID
 */
function is_login()
{
    
    $member = session('member_auth');
    
    if (empty($member)) {
        
        return DATA_DISABLE;
    } else {
        
        return session('member_auth_sign') == data_auth_sign($member) ? $member['member_id'] : DATA_DISABLE;
    }
}

/**
 * 系统非常规MD5加密方法
 * @param  string $str 要加密的字符串
 * @return string 
 */
function data_md5($str, $key = 'ThinkTree')
{
    
    return '' === $str ? '' : md5(sha1($str) . $key);
}

/**
 * 使用上面的函数与系统加密KEY完成字符串加密
 * @param  string $str 要加密的字符串
 * @return string 
 */
function data_md5_key($str, $key = '')
{
    
    if (is_array($str)) {
        
        ksort($str);

        $data = http_build_query($str);
        
    } else {
        
        $data = (string) $str;
    }
    return empty($key) ? data_md5($data, SYS_ENCRYPT_KEY) : data_md5($data, $key);
}

/**
 * 数据签名认证
 * @param  array  $data 被认证的数据
 * @return string       签名
 */
function data_auth_sign($data)
{
    
    // 数据类型检测
    if (!is_array($data)) {
        
        $data = (array)$data;
    }
    
    // 排序
    ksort($data);
    
    // url编码并生成query字符串
    $code = http_build_query($data);
    
    // 生成签名
    $sign = sha1($code);
    
    return $sign;
}

/**
 * 检测当前用户是否为管理员
 * @return boolean true-管理员，false-非管理员
 */
function is_administrator($member_id = null)
{
    
    $return_id = is_null($member_id) ? is_login() : $member_id;
    
    return $return_id && (intval($return_id) === SYS_ADMINISTRATOR_ID);
}

/**
 * 获取单例对象
 */
function get_sington_object($object_name = '', $class = null, $array = null)
{
    $request = request();

    $request->__isset($object_name) ?: $request->bind($object_name, new $class($array));

    return $request->__get($object_name);
}

/**
 * 获取插件类的类名
 * @param strng $name 插件名
 */
function get_addon_class($name = '')
{
    
    $lower_name = strtolower($name);
    
    $class = SYS_ADDON_DIR_NAME. SYS_DS_CONS . $lower_name . SYS_DS_CONS . $name;
    
    return $class;
}

/**
 * 钩子
 */
function hook($tag = '', $params = [])
{
    
    \think\Hook::listen($tag, $params);
}

/**
 * 插件显示内容里生成访问插件的url
 * @param string $url url
 * @param array $param 参数
 */
function addons_url($url, $param = array())
{

    $parse_url  =  parse_url($url);
    $addons     =  $parse_url['scheme'];
    $controller =  $parse_url['host'];
    $action     =  $parse_url['path'];

    /* 基础参数 */
    $params_array = array(
        'addon_name'      => $addons,
        'controller_name' => $controller,
        'action_name'     => substr($action, 1),
    );

    $params = array_merge($params_array, $param); //添加额外参数
    
    return url('addon/execute', $params);
}

/**
 * 插件对象注入
 */
function addon_ioc($this_class, $name, $layer)
{
    
    !str_prefix($name, $layer) && exception('逻辑与模型层引用需前缀:' . $layer);

    $class_arr = explode(SYS_DS_CONS, get_class($this_class));

    $sr_name = sr($name, $layer);

    $class_logic = SYS_ADDON_DIR_NAME . SYS_DS_CONS . $class_arr[DATA_NORMAL] . SYS_DS_CONS . $layer . SYS_DS_CONS . $sr_name;

    return get_sington_object(SYS_ADDON_DIR_NAME . '_' . $layer . '_' . $sr_name, $class_logic);
}

/**
 * 抛出响应异常
 */
function throw_response_exception($data = [], $type = 'json')
{
    
    $response = Response::create($data, $type);

    throw new HttpResponseException($response);
}

/**
 * 获取访问token
 */
function get_access_token($key)
{
    if(empty($key)){
        return false;
    }
    $api_key_list=getApiKeyList($key);

    if ($api_key_list) {
        return $key;
    }else{
        return false;
    }
}

/**
 * 格式化字节大小
 * @param  number $size      字节数
 * @param  string $delimiter 数字和单位分隔符
 * @return string            格式化后的带单位的大小
 */
function format_bytes($size, $delimiter = '')
{
    
    $units = array('B', 'KB', 'MB', 'GB', 'TB', 'PB');
    
    for ($i = 0; $size >= 1024 && $i < 5; $i++) {
        
        $size /= 1024;
    }
    
    return round($size, 2) . $delimiter . $units[$i];
}


// +---------------------------------------------------------------------+
// | 数组相关函数
// +---------------------------------------------------------------------+

/**
 * 把返回的数据集转换成Tree
 * @param array $list 要转换的数据集
 * @param string $pid parent标记字段
 * @param string $level level标记字段
 * @return array
 */
function list_to_tree($list, $pk='id', $pid = 'pid', $child = '_child', $root = 0)
{
    
    // 创建Tree
    $tree = [];
    
    if (!is_array($list)) {
        
        return false;
    }
    
    // 创建基于主键的数组引用
    $refer = [];

    foreach ($list as $key => $data) {

        $refer[$data[$pk]] =& $list[$key];
    }

    foreach ($list as $key => $data) {

        // 判断是否存在parent
        $parentId =  $data[$pid];

        if ($root == $parentId) {

            $tree[] =& $list[$key];

        } else if (isset($refer[$parentId])){

            is_object($refer[$parentId]) && $refer[$parentId] = $refer[$parentId]->toArray();
            
            $parent =& $refer[$parentId];

            $parent[$child][] =& $list[$key];
        }
    }
    
    return $tree;
}
/**
 * @param $items  数据库里获取的结果集，不论是健名，还是健名为id，自动转换
 * @param string $id
 * @param string $pid
 * @param string $link 关联符
 * @param null $parent 为选择想要的下级id，举个栗子：id=1 pid=0 那么$prent=0取得id=1的数据
 * @return array 返回值,拉类型模式，下级被上级回收，下级就不会显示在列表上
 */
function tree_select_universal($arr, $id = 'id', $pid = 'pid', $link = 'child', $parent = null)
{
    $tree = array();
    if (empty($arr)) return $tree;
    if (array_keys($arr)[0] == 0) {
        foreach ($arr as $v) {
            if (is_object($v)) $v = $v->toArray();
            $t[$v[$id]] = $v;
        }
        $arr = $t;
    }
    foreach ($arr as $key => $item) {
        if (is_object($item)) $arr[$key] = $item->toArray();
        if (isset($parent)) {
            $arr[$item[$pid]][$link][$item[$id]] = &$arr[$item[$id]];

        } else {
            if (isset($arr[$item[$pid]])) {
                $arr[$item[$pid]][$link][] = &$arr[$item[$id]];
            } else {
                $tree[] = &$arr[$item[$id]];
            }
        }
    }

    return isset($parent) ? $arr[$parent][$link] : $tree;
}
/**
 * 分析数组及枚举类型配置值 格式 a:名称1,b:名称2
 * @return array
 */
function parse_config_attr($string)
{
    
    $array = preg_split('/[,;\r\n]+/', trim($string, ",;\r\n"));
    
    if (strpos($string, ':')) {
        
        $value = [];
        
        foreach ($array as $val) {
            
            list($k, $v) = explode(':', $val);
            
            $value[$k] = $v;
        }
        
    } else {
        
        $value = $array;
    }
    
    return $value;
}

/**
 * 解析数组配置
 */
function parse_config_array($name = '')
{
    
    return parse_config_attr(config($name));
}

/**
 * 将二维数组数组按某个键提取出来组成新的索引数组
 */
function array_extract($array = [], $key = 'id')
{
    
    $count = count($array);
    
    $new_arr = [];
     
    for($i = 0; $i < $count; $i++) {
        
        if (!empty($array) && !empty($array[$i][$key])) {
            
            $new_arr[] = $array[$i][$key];
        }
    }
    
    return $new_arr;
}

/**
 * 根据某个字段获取关联数组
 */
function array_extract_map($array = [], $key = 'id'){
    
    
    $count = count($array);
    
    $new_arr = [];
     
    for($i = 0; $i < $count; $i++) {
        
        $new_arr[$array[$i][$key]] = $array[$i];
    }
    
    return $new_arr;
}

/**
 * 页面数组提交后格式转换 
 */
function transform_array($array)
{

    $new_array = array();
    $key_array = array();

    foreach ($array as $key=>$val) {

        $key_array[] = $key;
    }

    $key_count = count($key_array);

    foreach ($array[$key_array[0]] as $i => $val) {
        
        $temp_array = array();

        for( $j=0;$j<$key_count;$j++ ){

            $key = $key_array[$j];
            $temp_array[$key] = $array[$key][$i];
        }

        $new_array[] = $temp_array;
    }

    return $new_array;
}

/**
 * 页面数组转换后的数组转json
 */
function transform_array_to_json($array)
{
    
    return json_encode(transform_array($array));
}

/**
 * 关联数组转索引数组
 */
function relevance_arr_to_index_arr($array)
{
    
    $new_array = [];
    
    foreach ($array as $v)
    {
        
        $temp_array = [];
        
        foreach ($v as $vv)
        {
            $temp_array[] = $vv;
        }
        
        $new_array[] = $temp_array;
    }
    
    return $new_array;
}

/**
 * 数组转换为字符串，主要用于把分隔符调整到第二个参数
 * @param  array  $arr  要连接的数组
 * @param  string $glue 分割符
 * @return string
 */
function arr2str($arr, $glue = ',')
{
    
    return implode($glue, $arr);
}

/**
 * 数组转字符串二维
 * @param  array  $arr  要连接的数组
 * @param  string $glue 分割符
 * @return string
 */
function arr22str($arr)
{
    
    $t ='' ;
    $temp = [];
    foreach ($arr as $v) {
        $v = join(",",$v);
        $temp[] = $v;
    }
    foreach ($temp as $v) {
        $t.=$v.",";
    }
    $t = substr($t, 0, -1);
    return $t;
}


// +---------------------------------------------------------------------+
// | 字符串相关函数
// +---------------------------------------------------------------------+

/**
 * 字符串转换为数组，主要用于把分隔符调整到第二个参数
 * @param  string $str  要分割的字符串
 * @param  string $glue 分割符
 * @return array
 */
function str2arr($str, $glue = ',')
{
    
    return explode($glue, $str);
}

/**
 * 字符串替换
 */
function sr($str = '', $target = '', $content = '')
{
    
    return str_replace($target, $content, $str);
}

/**
 * 字符串前缀验证
 */
function str_prefix($str, $prefix)
{
    
    return strpos($str, $prefix) === DATA_DISABLE ? true : false;
}

// +---------------------------------------------------------------------+
// | 文件相关函数
// +---------------------------------------------------------------------+

/**
 * 获取目录下所有文件
 */
function file_list($path = '')
{
    
    $file = scandir($path);
    
    foreach ($file as $k => $v) {
        
        if (is_dir($path . SYS_DS_PROS . $v)) {
            
            unset($file[$k]);
        }
    }
    
    return array_values($file);
}
/**
 * @return bool生成指定文件夹
 */
function dirs_mk($dir, $mode = 0777)
{
    if (is_dir($dir) || @mkdir($dir, $mode)) return TRUE;
    if (!@mkdirs(dirname($dir), $mode)) return FALSE;
    return @mkdir($dir, $mode);
}
/**
 * 获取目录列表
 */
function get_dir($dir_name)
{
    
    $dir_array = [];
    
    if (false != ($handle = opendir($dir_name))) {
        
        $i = 0;
        
        while (false !== ($file = readdir($handle))) {
            
            if ($file != "." && $file != ".."&&!strpos($file,".")) {
                
                $dir_array[$i] = $file;
                
                $i++;
            }
        }
        
        closedir($handle);
    }
    
    return $dir_array;
}

/**
 * 获取文件根目录
 */
function get_file_root_path()
{
    
    $root_arr = explode(SYS_DS_PROS, URL_ROOT);
    
    array_pop($root_arr);

    $root_url = arr2str($root_arr, SYS_DS_PROS);

    return $root_url . SYS_DS_PROS;
}

/**
 * 获取图片url
 */
function get_picture_url($id = 0)
{

    return (new LogicFile())->getPictureUrl($id);
}

/**
 * 获取头像图片url
 */
function get_head_picture_url($id = 0)
{

    return (new LogicFile())->getPictureUrl($id, true);
}

/**
 * 获取文件url
 */
function get_file_url($id = 0)
{
    
    return (new LogicFile())->getFileUrl($id);
}

/**
 * 删除所有空目录 
 * @param String $path 目录路径 
 */
function rm_empty_dir($path)
{
    
    if (!(is_dir($path) && ($handle = opendir($path))!==false)) {
        
        return false;
    }
      
    while(($file = readdir($handle))!==false)
    {

        if (!($file != '.' && $file != '..')) {
            
           continue;
        }
        
        $curfile = $path . SYS_DS_PROS . $file;// 当前目录

        if (!is_dir($curfile)) {
            
           continue;  
        }

        rm_empty_dir($curfile);

        if (count(scandir($curfile)) == 2) {
            
            rmdir($curfile);
        }
    }

    closedir($handle); 
}


// +---------------------------------------------------------------------+
// | 时间相关函数
// +---------------------------------------------------------------------+

/**
 * 时间戳格式化
 * @param int $time
 * @return string 完整的时间显示
 */
function format_time($time = null, $format='Y-m-d H:i:s')
{
    
    if (null === $time) {
        
        $time = TIME_NOW;
    }
    
    return date($format, intval($time));
}

/**
 * 获取指定日期段内每一天的日期
 * @param Date $startdate 开始日期
 * @param Date $enddate  结束日期
 * @return Array
 */
function get_date_from_range($startdate, $enddate)
{
    
  $stimestamp = strtotime($startdate);
  $etimestamp = strtotime($enddate);
  
  // 计算日期段内有多少天
  $days = ($etimestamp-$stimestamp)/86400+1;
  
  // 保存每天日期
  $date = [];
  
  for($i=0; $i<$days; $i++) {
      
      $date[] = date('Y-m-d', $stimestamp+(86400*$i));
  }
  
  return $date;
}

// +---------------------------------------------------------------------+
// | 调试函数
// +---------------------------------------------------------------------+

/**
 * 将数据保存为PHP文件，用于调试
 */
function sf($arr = [], $fpath = './test.php')
{
    
    $data = "<?php\nreturn ".var_export($arr, true).";\n?>";
    
    file_put_contents($fpath, $data);
}

/**
 * dump函数缩写
 */
function d($arr = [])
{
    dump($arr);
}

/**
 * dump与die组合函数缩写
 */
function dd($arr = [])
{
    dump($arr);die;
}


// +---------------------------------------------------------------------+
// | 其他函数
// +---------------------------------------------------------------------+

/**
 * 通过类创建逻辑闭包
 */
function create_closure($object = null, $method_name = '', $parameter = [])
{
    
    $func = function() use($object, $method_name, $parameter) {
        
                return call_user_func_array([$object, $method_name], $parameter);
            };
            
    return $func;
}

/**
 * 通过闭包控制缓存
 */
function auto_cache($key = '', $func = null, $time = 3)
{
    
    $result = cache($key);
    
    if (empty($result)) {
        
        $result = $func();
        
        !empty($result) && cache($key, $result, $time);
    }
    
    return $result;
}

/**
 * 通过闭包列表控制事务
 */
function closure_list_exe($list = [])
{
    
    Db::startTrans();
    
    try {
        
        foreach ($list as $closure) {
            
            $closure();
        }
        
        Db::commit();
        
        return true;
    } catch (\Exception $e) {
        
        Db::rollback();
        
        throw $e;
    }
}

/**
 * 自动封装事务
 */
function trans($parameter = [], $callback = null)
{
    
    try {

        Db::startTrans();

        $backtrace = debug_backtrace(false, 2);

        array_shift($backtrace);

        $class = $backtrace[0]['class'];

        $result = (new $class())->$callback($parameter);

        Db::commit();

        return $result;

    } catch (Exception $ex) {

        Db::rollback();

        throw new Exception($ex->getMessage());
    }
}

/**
 * 更新缓存版本
 */
function update_cache_version($obj = null)
{
    
    $ob_auto_cache = cache('ob_auto_cache');

    is_string($obj) ? $ob_auto_cache[$obj]['version']++ : $ob_auto_cache[$obj->getTable()]['version']++;

    cache('ob_auto_cache', $ob_auto_cache);
}

/**
 * 获取通知订单号
 */
function get_order_sn()
{
   
    $where['service_name']  = 'Pay';
    $where['status']        = DATA_NORMAL;

    $pay_types = Db::name('driver')->where($where)->select();
    
    $order_sn = null;
    
    foreach ($pay_types as $v)
    {
        
        $model = model($v['driver_name'], 'service\\pay\\driver');
        
        $order_sn = $model->getOrderSn();
        
        if (!empty($order_sn)) {  break; }
    }
    
    return $order_sn;
}


/**
 * @param $name
 * @return mixed  获取rediskey值
 */
function getRedis($name)
{
    return Cache::store('redis')->get($name);
}

/**
 * @param $name
 * @return mixed  获取rediskey值
 */
function setRedis($name,$data, $expire = null)
{
    return Cache::store('redis')->set($name,$data,$expire);
}

/**
 * @param $name
 * @return mixed  删除rediskey值
 */
function delRedis($name)
{
    return Cache::store('redis')->rm($name);
}

/**
 * @param $name Hash 关键字
 * 清理所有hash值的数组
 *
 */
function cacheRedisHashClean($name)
{
    $keys_list = Cache::store('redis')->hkeys($name);
    foreach ($keys_list as $key => $value) {
        Cache::store('redis')->hdel($name, $value);
    }
}

/**
 * @param $name
 * @param $key
 * * @return mixed  清理Hash记录值
 */
function cacheRedisHashInfoClean($name, $key)
{
    return Cache::store('redis')->hdel($name, $key);
}

/**
 * @param $name
 * @param $key $arr = [1, 2, 3, 5];
 * * @return mixed  Hash单个获取批量记录值
 */
function arrayRedisHash($name, $arr)
{
    return Cache::store('redis')->hmget($name, $arr);
}

/**
 * @param $name
 * @param $key
 * @return mixed  Hash单个获取记录值
 */
function getRedisHash($name, $key)
{
    return Cache::store('redis')->hget($name, $key);
}

/**
 * @param $name
 * @param $key
 * @return mixed   设置redis  Hash数据
 */
function setRedisHash($name, $key, $data)
{
    return Cache::store('redis')->hset($name, $key, $data);
}

/**
 * @param null $updata true 更新tokens数据
 * @return bool|mixed  后台数据获取
 */
function getAccessToken($updata = null)
{
    $appidAccessToken = Cache::store('redis')->hget('admin_appid_token', "member_" . MEMBER_ID);
    if ($appidAccessToken and $updata != true) {
        $tokens = $appidAccessToken;
    } else {
        $regionModel = model('WeiAppid');
        $result = $regionModel->where(['id' => session('member_info')['choose_appid']])->field('id,appid,secret,token,mch_id,mch_key,encoding_aes_key,wechat,origin_id')->find();
        if ($result) {
            $tokens['id'] = $result['id'];
            $tokens['appid'] = $result['appid'];
            $tokens['secret'] = $result['secret'];
            $tokens['token'] = $result['token'];
            $tokens['wechat'] = $result['wechat'];
            $tokens['mch_id'] = $result['mch_id'];
            $tokens['mch_key'] = $result['mch_key'];
            $tokens['origin_id'] = $result['origin_id'];
            $tokens['encoding_aes_key'] = $result['encoding_aes_key'];
            // Cache::store('redis')->set("member_" . MEMBER_ID, $tokens);
            Cache::store('redis')->hset('admin_appid_token', "member_" . MEMBER_ID, $tokens);
        } else {
            return false;
        }
    }
    $access = new \exwechat\api\accessToken($tokens['appid'], $tokens['secret'], $tokens['token'], $tokens['encoding_aes_key']);

    $tokens = array_merge($tokens, $access->getAccessToken());
    return $tokens;
}

/**
 * @param $ToUserName 微信名
 * @param bool $node true 获取 access_token和ticket
 * @return bool|mixed  前端数据获取
 */
function getAppidToken()
{
    $tokens['id'] = 1;
    $tokens['appid'] = 'wx35ad67d9539125f1';
    $tokens['secret'] = '114596af5d286111d3d5ab308a9ef4dd';
    $tokens['token'] = '275d52112b0221ae0681dd5cda353618';
    $tokens['wechat'] = 'gh_18c3ed5925ca';
    $tokens['origin_id'] = 'gh_18c3ed5925ca';
    $tokens['mch_id'] = '1603616297';
    $tokens['mch_key'] = 'zhuoyueadqGbus8ZgpwY1jF81VssVncp';
    $tokens['encoding_aes_key'] = '';
    $access = new \exwechat\api\accessToken($tokens['appid'], $tokens['secret'], $tokens['token'], $tokens['encoding_aes_key']);

    $tokens = array_merge($tokens, $access->getAccessToken());

    return $tokens;
}

/**
 * @param $access_token api_access_token
 */
function getApiKeyList($access_token)
{
    $ApiKeyList = Cache::store('redis')->hget('api_key_list', $access_token);
    if ($ApiKeyList) {
        return $ApiKeyList;
    } else {
        $id=explode("*#",$access_token);
        if(count($id)<3){
            return API_KEY;  //不是微信api是普通的token
        }
        $regionModel = model('WeiAppid');
        $result = $regionModel->where(['id' => $id[0],'appid'=>$id[1]])->field('id,appid,secret,token,encoding_aes_key,wechat,mch_id,mch_key,member_id')->find();
        if ($result) {
            $tokens['id'] = $result['id'];
            $tokens['appid'] = $result['appid'];
            $tokens['secret'] = $result['secret'];
            $tokens['token'] = $result['token'];
            $tokens['wechat'] = $result['wechat'];
            $tokens['mch_id'] = $result['mch_id'];
            $tokens['mch_key'] = $result['mch_key'];
            $tokens['member_id'] = $result['member_id'];
            $tokens['encoding_aes_key'] = $result['encoding_aes_key'];
            Cache::store('redis')->hset('api_key_list', $access_token, $tokens);
            return true;
        } else {
            return false;
        }
    }
    return $tokens;
}

function filterEmoji($str)
{
    $str = preg_replace_callback( '/./u',
        function (array $match) {
            return strlen($match[0]) >= 4 ? '' : $match[0];
        },
        $str);
    return $str;
}

/**
 * 保存本地服务器
 */
function downFile($url,$filename, $timeout = 60)
{
    $path = dirname($filename);
    if (!is_dir($path) && !mkdir($path, 0755, true)) {
        return false;
    }
    $fp = fopen($filename, 'w');
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_FILE, $fp);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_exec($ch);
    curl_close($ch);
    fclose($fp);
    return $filename;
}


function unique_rand($min, $max, $num) {
    //初始化变量为0
    $count = 0;
    //建一个新数组
    $return = array();
    while ($count < $num) {
        //在一定范围内随机生成一个数放入数组中
        $return[] = mt_rand($min, $max);
        //去除数组中的重复值用了“翻翻法”，就是用array_flip()把数组的key和value交换两次。这种做法比用 array_unique() 快得多。
        $return = array_flip(array_flip($return));
        //将数组的数量存入变量count中
        $count = count($return);
    }
    //为数组赋予新的键名
    shuffle($return);
    return $return;
}

//参数1：访问的URL，参数2：post数据(不填则为GET)，参数3：提交的$cookies,参数4：是否返回$cookies
function curl_request($url,$post='',$cookie='', $returnCookie=0){
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0)');
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
    curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
    curl_setopt($curl, CURLOPT_REFERER, "http://XXX");
    if($post) {
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($post));
    }
    if($cookie) {
        curl_setopt($curl, CURLOPT_COOKIE, $cookie);
    }
    curl_setopt($curl, CURLOPT_HEADER, $returnCookie);
    curl_setopt($curl, CURLOPT_TIMEOUT, 10);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    $data = curl_exec($curl);
    if (curl_errno($curl)) {
        return curl_error($curl);
    }
    curl_close($curl);
    if($returnCookie){
        list($header, $body) = explode("\r\n\r\n", $data, 2);
        preg_match_all("/Set\-Cookie:([^;]*);/", $header, $matches);
        $info['cookie']  = substr($matches[1][0], 1);
        $info['content'] = $body;
        return $info;
    }else{
        return $data;
    }
}

/**
 * @param $URL 请求链接
 * @param null $data 数据 array() string
 * @param string $type 请求类型 POST GET PUT DELETE
 * @param string $headers 头部信息
 * @param string $data_type 返回数据类型 默认为json
 * @return mixed
 */
function callInterfaceCommon($URL, $data = null, $type = 'POST', $headers = "",$headers_result=false)
{
    $ch = curl_init();
    //判断ssl连接方式
    if (stripos($URL, 'https://') !== false) {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSLVERSION, 1);
    }
    $connttime = 300; //连接等待时间500毫秒
    $timeout = 15000;//超时时间15秒

    $querystring = "";
    if (is_array($data)) {
        // Change data in to postable data
        foreach ($data as $key => $val) {
            if (is_array($val)) {
                foreach ($val as $val2) {
                    $querystring .= urlencode($key) . '=' . urlencode($val2) . '&';
                }
            } else {
                $querystring .= urlencode($key) . '=' . urlencode($val) . '&';
            }
        }
        $querystring = substr($querystring, 0, -1); // Eliminate unnecessary &
    } else {
        $querystring = $data;
    }

    // echo $querystring;
    curl_setopt($ch, CURLOPT_URL, $URL); //发贴地址
    //设置HEADER头部信息
    if ($headers_result == true) {
        curl_setopt($ch, CURLOPT_HEADER, true);
    }
    if ($headers != "") {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    } else {
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-type: text/json'));
    }
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);//反馈信息
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1); //http 1.1版本

    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT_MS, $connttime);//连接等待时间
    curl_setopt($ch, CURLOPT_TIMEOUT_MS, $timeout);//超时时间

    switch ($type) {
        case "GET" :
            curl_setopt($ch, CURLOPT_HTTPGET, true);
            break;
        case "POST":
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $querystring);
            break;
        case "PUT" :
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $querystring);
            break;
        case "DELETE":
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $querystring);
            break;
    }
    $file_contents = curl_exec($ch);//获得返回值
    // echo time().'<br>';
    //$status = curl_getinfo($ch);
    //dump($status);
    curl_close($ch);

    return $file_contents;
}

function remove_shell_chars($value) {
    return str_replace([';', '|', '&', '`', '$'], '', $value);
}  // 清除9类高危符号‌:ml-citation{ref="1,3" data="citationList"}
