<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\astrology2\logic;
use app\common\logic\ApiBase;

/**
 * 接口文档逻辑
 */
class ApiInterface extends ApiBase
{

    /**
     * 获取接口列表
     */
    public function getApiList($where = [], $field = true, $order = '', $paginate = false)
    {
        $api_group_list = $this->modelApiGroup->getColumn($where , $field, 'id',$order);

        $api_list = $this->modelApi->getColumn(['group_id'=>['in',array_column($api_group_list,'id')]], $field,'',$order);

        foreach($api_list as $key=>$value){

            $api_group_list[$value['group_id']]['api_list'][] = $value;
        }
        return $api_group_list;
    }
    
    /**
     * 获取接口分组列表
     */
    public function getApiGroupList($where = [], $field = true, $order = 'id asc', $paginate = false)
    {
        
        return $this->modelApiGroup->getList($where, $field, $order, $paginate);
    }
    
    /**
     * 获取接口信息
     */
    public function getApiInfo($where = [], $field = true)
    {
        
        $info = $this->modelApi->getInfo($where, $field);
        
        $api_data_type_option = parse_config_array('api_data_type_option');
        $api_status_option    = parse_config_array('api_status_option');
        
        $info_array = $info->toArray();
        
        if (!empty($info_array['request_data'])) {
            foreach ($info_array['request_data'] as &$v)
            {
                $v['data_type']  = $api_data_type_option[$v['data_type']];
                $v['is_require'] = $v['is_require'] ? '是' : '否';
            }
        }
        
        if (!empty($info_array['response_data'])) {
            foreach ($info_array['response_data'] as &$v)
            {
                $v['data_type']  = $api_data_type_option[$v['data_type']];
            }
        }
        
        $info_array['request_type_text'] = $info_array['request_type'] ? 'GET' : 'POST';
        $info_array['api_status_text']   = $api_status_option[$info_array['api_status']];
        
        return $this->apiAttachField($info_array);
    }
    
    /**
     * API附加字段
     */
    public function apiAttachField($info_array)
    {
        empty($info_array['request_data']) && $info_array['request_data'] = [];
        if ($info_array['is_page'])
        {
            $page_attach_field = config('page_attach_field');
            
            foreach ($page_attach_field as $field) {
                
                array_unshift($info_array['request_data'], $field);
            }
        }
        
        $info_array['is_request_sign']      && array_unshift($info_array['request_data'],   config('data_sign_attach_field'));
        
        $info_array['is_user_token']        && array_unshift($info_array['request_data'],   config('user_token_attach_field'));
        
        empty($info_array['request_data'])  && $info_array['request_data'] = [];
        
        array_unshift($info_array['request_data'], config('access_token_attach_field'));
        
        empty($info_array['response_data']) && $info_array['response_data'] = [];
        
        $info_array['is_response_sign']     && array_unshift($info_array['response_data'],  config('data_sign_attach_field'));
        
        return $info_array;
    }

    /**
     * API错误码数据
     */
    public function apiErrorCodeData()
    {

        $path = APP_PATH . 'common' . SYS_DS_PROS . RESULT_ERROR;

        $file_list  = file_list($path);

        $code_data = [];

        foreach ($file_list as $v)
        {

            $class_path = SYS_DS_CONS . SYS_APP_NAMESPACE . SYS_DS_CONS . 'common' . SYS_DS_CONS . RESULT_ERROR . SYS_DS_CONS;

            $class_name = sr($v, EXT);

            $ref = new \ReflectionClass($class_path . $class_name);

            $props = $ref->getStaticProperties();



            foreach ($props as $k => $v)
            {

                $data['class']          = $class_name;
                $data['property']       = $k;
                $data[API_CODE_NAME]    = $v[API_CODE_NAME];
                $data[API_MSG_NAME]     = $v[API_MSG_NAME];

                $code_data[] = $data;
            }
        }
        array_multisort(array_column($code_data,'code'),SORT_ASC,$code_data);
        return $code_data;
    }
}
