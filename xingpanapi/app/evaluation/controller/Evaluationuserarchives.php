<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\evaluation\controller;

/**
 * 用户档案控制器
 */
class Evaluationuserarchives extends UserBase
{

    /**
     * 用户档案列表
     */
    public function list()
    {

        $where = $this->logicEvaluationUserArchives->getWhere($this->param_data);
        $where['user_id'] = $this->param_data['user_id'];
        $where['status'] = 1;

        $data = $this->logicEvaluationUserArchives->getEvaluationUserArchivesColumn($where, '');

        foreach ($data as $key => &$value) {
            if (!empty($data['cover_id']) and empty($data['cover_url'])) {
                $value['cover_url'] = $this->logicFile->getPictureUrl($value['cover_id']);
            }
        }

        return $this->apiReturn($data);
    }

    /**
     * 用户档案无分页列表
     */
    public function info()
    {
        $where['user_id'] = $this->param_data['user_id'];

        $where['id'] = $this->param_data['id'];

        $data = $this->logicEvaluationUserArchives->getUserArchivesInfo($where);

        if(empty($data)){
            return $this->apiReturn(['code' => 5010012, 'msg' => '当前档案不存在']);
        }

        if (!empty($data['cover_id']) and empty($data['cover_url'])) {
            $data['cover_url'] = $this->logicFile->getPictureUrl($data['cover_id']);
        }


        return $this->apiReturn($data);
    }

    /**
     * 用户档案添加
     */
    public function add()
    {

        $birth_points=$this->param_data['birth_points'];
        $birth_points_array= explode(",",$birth_points);
        if($birth_points_array[1]>66.5 or $birth_points_array[1]<-66.5){
            return $this->apiReturn(['code' => 5010012, 'msg' => '出生地纬度在-66.5~66.5之间']);
        }
        $birth_points=round($birth_points_array[0], 6).','.round($birth_points_array[1], 6);
        $now_points=$this->param_data['now_points'];
        $now_points_array= explode(",",$now_points);
        if($now_points_array[1]>66.5 or $now_points_array[1]<-66.5){
            return $this->apiReturn(['code' => 5010012, 'msg' => '现居地纬度在-66.5~66.5之间']);
        }
        $now_points=round($now_points_array[0], 6).','.round($now_points_array[1], 6);

        $regit_data = [
            'sex' => $this->param_data['sex'],
            'nickname' => filterEmoji($this->param_data['nickname']),
            'birthday' => $this->param_data['birthday'],
            'birth_place' => $this->param_data['birth_place'],
            'birth_points' => $birth_points,
            'now_place' => $this->param_data['now_place'],
            'now_points' => $now_points,
            'category_id' => $this->param_data['category_id'],
            'time_zone' => $this->param_data['time_zone'],
            'is_summer' => $this->param_data['is_summer'],
            'use_time' => time(),
            'user_id' => $this->param_data['user_id']
        ];
        if(!empty($this->param_data['id'])){
            $regit_data['id'] = $this->param_data['id'];
        }
        if($this->param_data['category_id']==1){
            $regit_data['use_time']=2000000000;
        }
        !empty($this->param_data['cover_id']) && $regit_data['cover_id'] = $this->param_data['cover_id'];
        !empty($this->param_data['cover_url']) && $regit_data['cover_url'] = $this->param_data['cover_url'];
        $regit = $this->logicEvaluationUserArchives->EvaluationuserarchivesEdit($regit_data);
        if($regit['code']>0){
            return $this->apiReturn($regit);
        }
        $regit['data']=array_merge($regit['data'],$regit_data);
        return $this->apiReturn($regit);
    }



    /**
     * 用户档案删除
     */
    public function del()
    {
        $EvaluationuserarchivesInfo = $this->logicEvaluationUserArchives->getEvaluationuserarchivesInfo(['user_id' => $this->param_data['user_id'], 'id' => $this->param_data['id']], 'id,category_id,nickname');

        if (empty($EvaluationuserarchivesInfo)) {
            return $this->apiReturn(['code' => 1020002, 'msg' => '当前档案不存在']);
        }

        if ($EvaluationuserarchivesInfo['category_id'] == 1) {
            return $this->apiReturn(['code' => 1020002, 'msg' => '自己的档案不能删除']);
        }
        $regit = $this->logicEvaluationUserArchives->EvaluationuserarchivesDel(['user_id' => $this->param_data['user_id'], 'id' => $this->param_data['id']]);
//
//        $this->logicEvaluationCombination->evaluationCombinationDel(['user_id' => $this->param_data['user_id'], 'archives_id' =>  $this->param_data['id']]);
//        $this->logicEvaluationCombination->evaluationCombinationDel(['user_id' => $this->param_data['user_id'], 'second_id' =>  $this->param_data['id']]);


        return $this->apiReturn([]);

    }

    /**
     * 获取星座参数
     */
    public function natal()
    {

        $param['birthday'] = $this->param_data['birthday'];
        $param['birth_points'] = $this->param_data['birth_points'];
        $birth_points_array= explode(",", $param['birth_points']);

        if($birth_points_array[1]>66.5 or $birth_points_array[1]<-66.5){
            $param['birth_points'] =$birth_points_array[0].','.'66';
        }

        $param['time_zone'] = $this->param_data['time_zone'];
        $param['is_summer'] = $this->param_data['is_summer'];
        $sex= $this->param_data['sex'];

        $param['planets'] = [0, 1];
        $param['virtual'] = [10];
        $param['h_sys'] = 'P';
        $param['svg_type'] = -1;



        $data = $this->chartNatal->plateData($param);

        $planet = $data['planet'];

        $cover_url_name=($planet[0]["sign"]['sign_id']+1).'_'.$sex;
        $cover_id=($planet[0]["sign"]['sign_id']+1)*$sex;

        $constellation[] = ['planet_code' => $planet[0]['code_name'], 'planet_chinese' => $planet[0]['planet_chinese'], 'sign_chinese' => $planet[0]["sign"]['sign_chinese'], 'sign_id' => $planet[0]["sign"]['sign_id'], 'sign_font' => $planet[0]["sign"]['sign_font']];
        $constellation[] = ['planet_code' => $planet[1]['code_name'], 'planet_chinese' => $planet[1]['planet_chinese'], 'sign_chinese' => $planet[1]["sign"]['sign_chinese'], 'sign_id' => $planet[1]["sign"]['sign_id'], 'sign_font' => $planet[1]["sign"]['sign_font']];
        $constellation[] = ['planet_code' => $planet[2]['code_name'], 'planet_chinese' => $planet[2]['planet_chinese'], 'sign_chinese' => $planet[2]["sign"]['sign_chinese'], 'sign_id' => $planet[2]["sign"]['sign_id'], 'sign_font' => $planet[2]["sign"]['sign_font']];

        return $this->apiReturn(['cover_id' => $cover_id,
            'cover_url' => 'https://asxup-1305125772.cos.ap-guangzhou.myqcloud.com/static/app/img/h_'.$cover_url_name.'.png',
            'constellation' => $constellation]);

    }
}
