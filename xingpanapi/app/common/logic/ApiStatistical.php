<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 接口管理逻辑
 */
class ApiStatistical extends LogicBase
{

      /**
       * 获取接口管理搜索条件
      */
      public function getWhere($data = [])
      {

         $where = [];

         //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

         return $where;
      }

     /**
      * 获接口管理单条信息
      */
     public function getApiStatisticalInfo($where = [], $field = '*')
     {

        return $this->modelApiStatistical->getInfo($where, $field);
     }

    /**
     * 获取接口管理列表
     */

    public function getApiStatisticalList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelApiStatistical->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取接口管理无分页列表
     */
    public function getApiStatisticalColumn($where = [], $field = '', $key = '')
    {
        return $this->modelApiStatistical->getColumn($where, $field , $key);
    }

    /**
     * 获取接口管理无分页列表
     */
    public function setApiStatisticalColumn($data_list = [], $replace = false)
    {
        return $this->modelApiStatistical->setList($data_list , $replace);
    }

    /**
     * 接口管理单条编辑
     */
    public function apiStatisticalEdit($data = [])
    {
		
        $result = $this->modelApiStatistical->setInfo($data);
        
        return $result ? $result : $this->modelApiStatistical->getError();
    }

    /**
     * 接口管理单条编辑
     */
    public function apiStatisticalUpdate($where = [], $data = [])
    {

        $result = $this->modelApiStatistical->updateInfo($where, $data);

        return $result ? $result : $this->modelApiStatistical->getError();
    }

    /**
     * 接口管理删除
     */
    public function apiStatisticalDel($where = [], $is_true = false)
    {

        $result = $this->modelApiStatistical->deleteInfo($where, $is_true);
        
        return $result ? $result : $this->modelApiStatistical->getError();
    }
	
	
	//Admin模块操作
	/**
     * 获取接口管理搜索条件
     */
    public function getAdminWhere($data = [])
    {

        $where = [];

        //!empty($data['search_data']) && $where['title|describe'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }
	  
	/**
     * 接口管理单条编辑
     */
    public function apiStatisticalAdminEdit($data = [])
    {


        $url = url('apiStatisticalList');
        
        $data['member_id'] = MEMBER_ID;
		
        $result = $this->modelApiStatistical->setInfo($data);
		
		$handle_text = empty($data['id']) ? '新增' : '编辑';
        
        $result && action_log($handle_text, '接口管理' . $handle_text . '，data：' .http_build_query($data));
		
        return $result ? [RESULT_SUCCESS, '接口管理操作成功', $url] : [RESULT_ERROR, $this->modelApiStatistical->getError()];
    }

    /**
     * 接口管理删除
     */
    public function apiStatisticalAdminDel($where = [])
    {

        $result = $this->modelApiStatistical->deleteInfo($where);
        
        $result && action_log('删除', '接口管理删除，where：' . http_build_query($where));
        
        return $result ? [RESULT_SUCCESS, '接口管理删除成功'] : [RESULT_ERROR, $this->modelApiStatistical->getError()];
    }
}
