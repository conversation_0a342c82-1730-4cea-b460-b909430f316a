<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 星座运势语料逻辑
 */
class CorpusConstellation extends LogicBase
{

    /**
 * 获取星座运势语料搜索条件
 */
    public function getWhere($data = [])
    {

        $where = [];

        isset($data['status']) && $where['status'] = $data['status'];

        isset($data['planet']) && $where['planet'] = $data['planet'];

        isset($data['constellation']) && $where['constellation'] = $data['constellation'];

        !empty($data['search_data']) && $where['planet|constellation|keywords|content|'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 获星座运势语料单条信息
     */
    public function getCorpusConstellationInfo($where = [], $field = '*')
    {

        return $this->modelCorpusConstellation->getInfo($where, $field);
    }

    /**
     * 获取星座运势语料列表-分页
     */

    public function getCorpusConstellationList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelCorpusConstellation->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取星座运势语料列表-所有
     */

    public function getCorpusConstellationAllList($where = [], $field = '', $order = '', $paginate = false)
    {
        return $this->modelCorpusConstellation->getList($where, $field, $order, $paginate);
    }


    /**
     * 获取星座运势语料无分页列表
     */
    public function getCorpusConstellationColumn($where = [], $field = '', $key = '')
    {
        return $this->modelCorpusConstellation->getColumn($where, $field, $key);
    }

    /**
     * 星座运势语料单条编辑
     */
    public function corpusConstellationEdit($data = [])
    {

        $result = $this->modelCorpusConstellation->setInfo($data);

        return $result ? $result : $this->modelCorpusConstellation->getError();
    }

    /**
     * 星座运势语料删除
     */
    public function corpusConstellationDel($where = [], $is_true = false)
    {

        $result = $this->modelCorpusConstellation->deleteInfo($where, $is_true);

        return $result ? $result : $this->modelCorpusConstellation->getError();
    }


    //Admin模块操作

    /**
     * 获取星座运势语料搜索条件
     */
    public function getAdminWhere($data = [])
    {


        $where = [];


        if(isset($data['chartType']) and $data['chartType']!='all'){
            $where['chartType'] = $data['chartType'];
        }

        !empty($data['search_data']) && $where['oneself|other|keywords|content'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }


    /**
     * 商城商品列表单条编辑
     */
    public function corpusConstellationAdminEdit($data = [])
    {


        $result = $this->modelCorpusConstellation->setInfo($data);

        $handle_text = empty($data['id']) ? '座运势语料列表' : '编辑';

        $result && action_log($handle_text, '座运势语料列表' . $handle_text . '，id：' . $data['id']);

        return $result ? [RESULT_SUCCESS, '座运势语料修改成功', url('corpusConstellationList')] : [RESULT_ERROR, $this->modelShopGoods->getError()];
    }
    /**
     * 星座运势语料删除
     */
    public function corpusConstellationAdminDel($where = [])
    {

        $result = $this->modelCorpusConstellation->deleteInfo($where);

        $result && action_log('删除', '星座运势语料删除，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '星座运势语料删除成功'] : [RESULT_ERROR, $this->modelCorpusConstellation->getError()];
    }


    /**
     * 商城商品列表单条编辑
     */
    public function yiji($year,$month,$day,$birthday)
    {

        $luckyDuckDay = getRedis('yj_data');
        if (!$luckyDuckDay or empty($luckyDuckDay[$year . $month . $day])) {
            $host = "https://jisuhlcx.market.alicloudapi.com";
            $path = "/huangli/date";
            $method = "GET";
            $appcode = "32b61c7d09624b59bec92a7301cc36cf";
            $headers = array();
            array_push($headers, "Authorization:APPCODE " . $appcode);
            $querys = "day=$day&month=$month&year=$year";
            $bodys = "";
            $url = $host . $path . "?" . $querys;

            $curl = curl_init();
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($curl, CURLOPT_FAILONERROR, false);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HEADER, false);
            if (1 == strpos("$" . $host, "https://")) {
                curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            }
            $luckyDuckDay_dec = json_decode(curl_exec($curl), true);

            $yi='万事俱备';
            $ji='大事勿用';
            if(!empty($luckyDuckDay_dec['result']['yi'][2])){
                $yi=$luckyDuckDay_dec['result']['yi'][2];
            }
            if(!empty($luckyDuckDay_dec['result']['ji'][2])){
                $ji=$luckyDuckDay_dec['result']['ji'][2];
            }

            $luckyDuckDay[$year . $month . $day]=['good'=>$yi,'ji'=>$ji];

            setRedis('yj_data', $luckyDuckDay);
        }
        $data=$luckyDuckDay[$year . $month . $day];
        $data['lucky_number'] = 6;



        $lucky_day=$year.$month.$day.date('dmY', strtotime($birthday));

        chong:
        $lucky_day_array=str_split($lucky_day);
        $lucky_day=0;
        foreach ($lucky_day_array as $keyl=>$valuel){
            $lucky_day+=$valuel;
        }

        if($lucky_day>9){
            goto chong;
        }
        $data['lucky_number'] = $lucky_day;
        $data['lucky_color'] = '黄色';
        $data['lucky_bearing'] = '东北';
        return $data;
    }




    /**
     * 商城商品列表单条编辑
     */
    public function getCorpusInfo($corpusConstellationWhere)
    {

        $corpusConstellationModify='';

        $corpusConstellationInfo = $this->logicCorpusConstellation->getCorpusConstellationInfo($corpusConstellationWhere, 'oneself,other,keywords,content');

        if(!empty($corpusConstellationInfo)){
            $corpusConstellationModify = $corpusConstellationWhere;

            $corpusConstellationModify['title'] = $corpusConstellationInfo['oneself'] . $corpusConstellationInfo['other'];

            $corpusConstellationModify['content'] = $corpusConstellationInfo['content'];
        }


//        if(!empty($planet_desc)){
//            $corpusConstellationDescribeInfo = $this->logicCorpusConstellation->getCorpusConstellationInfo(['chartType' => $chartType, 'type' => 1, 'oneself' => $planetName[$planet_desc]], 'oneself,other,keywords,content');
//            $corpusConstellationModify['describe'] = $corpusConstellationDescribeInfo['content'];
//        }
        return $corpusConstellationModify;
    }



}
