<?php

namespace exwechat\api\pay;
use exwechat\api\utils\common;

/**
 * 支付
 * <AUTHOR> <<EMAIL>>
 * @license [https://mp.weixin.qq.com/wiki]
 */
class wxXcPay
{
    private $wxxcurl = 'https://api.mch.weixin.qq.com/pay/unifiedorder';

    protected $appid;
    protected $mch_id;
    protected $key;
    protected $openid;
    protected $out_trade_no;
    protected $body;
    protected $total_fee;
    protected $notify_url;

    function __construct($data=[])
    {
        $this->appid = $data['appid'];
        $this->openid =  $data['openid'];
        $this->mch_id =  $data['mch_id'];
        $this->key =  $data['key'];
        $this->out_trade_no =  $data['out_trade_no'];
        $this->body =  $data['body'];
        $this->total_fee =  $data['total_fee'];
        $this->notify_url =  $data['notify_url'];
    }

    public function wxxcPay()
    {
        $host = $_SERVER["SERVER_ADDR"];
        $nonce_str = common::createNonceStr();//随机字符串
        $trade_type = 'JSAPI';//交易类型 默认
        $this->total_fee*=100;
        //这里是按照顺序的 因为下面的签名是按照顺序 排序错误 肯定出错
        $post['appid'] = $this->appid;
        $post['body'] = $this->body;
        $post['mch_id'] = (int)$this->mch_id;
        $post['nonce_str'] = $nonce_str;//随机字符串
        $post['notify_url'] =  $this->notify_url;
        $post['openid'] = $this->openid;
        $post['out_trade_no'] = $this->out_trade_no;
        $post['spbill_create_ip'] = $host;//终端的ip
        $post['total_fee'] = $this->total_fee;//总金额 最低为一块钱 必须是整数
        $post['trade_type'] = $trade_type;

        $sign = PaySign::generateSign($post, $this->key);
        $post_xml = '<xml>
           <appid>' . $this->appid . '</appid>
           <body>' . $this->body . '</body>
           <mch_id>' . $this->mch_id . '</mch_id>
           <nonce_str>' . $nonce_str . '</nonce_str>
           <notify_url>' .  $this->notify_url . '</notify_url>
           <openid>' . $this->openid . '</openid>
           <out_trade_no>' . $this->out_trade_no . '</out_trade_no>
           <spbill_create_ip>' . $host . '</spbill_create_ip>
           <total_fee>' . $this->total_fee . '</total_fee>
           <trade_type>' . $trade_type . '</trade_type>
           <sign>' . $sign . '</sign>
        </xml> ';
        $xml = $this->http_request($this->wxxcurl, $post_xml);
        $array = $this->xml($xml);//全要大写

        if ($array['RETURN_CODE'] == 'SUCCESS' && $array['RESULT_CODE'] == 'SUCCESS') {
            $time = time();
            $tmp = array();//临时数组用于签名
            $tmp['appId'] = $this->appid;
            $tmp['nonceStr'] = $nonce_str;
            $tmp['package'] = 'prepay_id=' . $array['PREPAY_ID'];
            $tmp['signType'] = 'MD5';
            $tmp['timeStamp'] = "$time";

            $data['state'] = 1;
            $data['appId'] = $this->appid;
            $data['timeStamp'] = "$time";//时间戳
            $data['nonceStr'] = $nonce_str;//随机字符串
            $data['signType'] = 'MD5';//签名算法，暂支持 MD5
            $data['package'] = 'prepay_id=' . $array['PREPAY_ID'];//统一下单接口返回的 prepay_id 参数值，提交格式如：prepay_id=*
            $data['paySign'] = PaySign::generateSign($tmp, $this->key);//签名,具体签名方案参见微信公众号支付帮助文档;
            $data['out_trade_no'] = $this->out_trade_no;
            $data['prepay_id'] =$array['PREPAY_ID'];

        } else {

            $data['state'] = 0;
            $data['text'] = "错误";
            $data['RETURN_CODE'] = $array['RETURN_CODE'];
            $data['ERR_CODE_DES'] = $array['ERR_CODE_DES'];
        }
        return $data;
    }
    //curl请求啊
    function http_request($url, $data = null, $headers = array())
    {
        $curl = curl_init();
        if (count($headers) >= 1) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        }
        curl_setopt($curl, CURLOPT_URL, $url);

        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);

        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }

    //获取xml
    private function xml($xml)
    {
        $p = xml_parser_create();
        xml_parse_into_struct($p, $xml, $vals, $index);
        xml_parser_free($p);
        $data = array();

        foreach ($index as $key => $value) {
            if ($key == 'xml' || $key == 'XML') continue;
            $tag = $vals[$value[0]]['tag'];
            $value = $vals[$value[0]]['value'];
            $data[$tag] = $value;
        }
        return $data;
    }
}
