<?php

namespace exwechat\api\pay;

use exwechat\api\http;
use exwechat\api\utils\common;
use exwechat\XMLParse;
use exwechat\exXMLMaker;

/**
 * 支付
 * <AUTHOR> <<EMAIL>>
 * @license [https://mp.weixin.qq.com/wiki]
 */
class wxPay
{
    // 统一下单
    protected $unifiedorder = 'https://api.mch.weixin.qq.com/pay/unifiedorder';

    // 退款
    protected $refund = 'https://api.mch.weixin.qq.com/secapi/pay/refund';

    protected $appid;
    protected $mch_id;
    protected $signKey;

    function __construct($data)
    {
        $this->appid = $data['appid'];
        $this->mch_id = $data['mch_id'];
        $this->signKey = $data['mch_key'];
    }

    /**
     * 统一下单
     */
    public function unified($orderData)
    {
        $nonce_str=common::createNonceStr();
        $orderData['appid'] = $this->appid;
        $orderData['mch_id'] = $this->mch_id;

        $orderData['spbill_create_ip'] = common::get_ip();//终端的ip
        $orderData['nonce_str'] = $nonce_str;
        $orderData['sign'] = PaySign::generateSign($orderData, $this->signKey);

        $xml = exXMLMaker::arrToXml($orderData);

        $ret = http::curl_post($this->unifiedorder, $xml);

        if ($ret[0]) {
            if ($this->logger)
                $this->logger->debug('统一下单有错误!', $ret);
            return false;
        }

        $data=XMLParse::xmlToArray($ret[1]);

        if ($data['return_code'] == 'SUCCESS' && $data['result_code'] == 'SUCCESS' && $data['trade_type']=='JSAPI') {
            $time = time();
            $tmp = array();//临时数组用于签名
            $tmp['appId'] = $this->appid;
            $tmp['nonceStr'] = $nonce_str;
            $tmp['package'] = 'prepay_id=' . $data['prepay_id'];
            $tmp['signType'] = 'MD5';
            $tmp['timeStamp'] = "$time";

            $data['state'] = 1;
            $data['appId'] = $this->appid;
            $data['timeStamp'] = "$time";//时间戳
            $data['nonceStr'] = $nonce_str;//随机字符串
            $data['signType'] = 'MD5';//签名算法，暂支持 MD5
            $data['package'] = 'prepay_id=' . $data['prepay_id'];//统一下单接口返回的 prepay_id 参数值，提交格式如：prepay_id=*
            $data['paySign'] = PaySign::generateSign($tmp, $this->signKey);//签名,具体签名方案参见微信公众号支付帮助文档;


        }

        return $data;
    }
    /**
     * 统一下单
     */
    public function wxRefund($orderData)
    {
        $nonce_str=common::createNonceStr();
        $orderData['appid'] = $this->appid;
        $orderData['mch_id'] = $this->mch_id;
        $orderData['nonce_str'] = $nonce_str;




        $orderData['sign'] = PaySign::generateSign($orderData, $this->signKey);


        $xml = exXMLMaker::arrToXml($orderData);

        $cert_key['cert']=PATH_PUBLIC.'cert_key_p12/apiclient_cert.pem';
        $cert_key['key']=PATH_PUBLIC.'cert_key_p12/apiclient_key.pem';

        $ret = http::sslpost($this->refund, $xml,$cert_key);
 ;
        if ($ret[0]) {
            return false;
        }

        $data=XMLParse::xmlToArray($ret[1]);


        return $data;
    }
}
