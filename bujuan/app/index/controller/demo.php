<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+

namespace app\index\controller;

use astrology\planetName as planetName;
use exwechat\api\account\shortUrl;

/**
 * 前端首页控制器
 */
class Index extends IndexBase
{

    // 首页
    public function index($cid = 0)
    {

        $where = [];

        !empty((int)$cid) && $where['a.category_id'] = $cid;

        $this->assign('article_list', $this->logicArticle->getArticleList($where, 'a.*,m.nickname,c.name as category_name', 'a.create_time desc'));

        $this->assign('category_list', $this->logicArticle->getArticleCategoryList([], true, 'create_time asc', false));

        // 关闭布局
        $this->view->engine->layout(false);

        return $this->fetch('index');
    }
    //注册
    public function signup(){

        return $this->fetch();

    }
    //登录
    public function login(){

        return $this->fetch();

    }
    //找回密码
    public function resetPassword(){

        return $this->fetch('reset_password');

    }
    //服务条款
    public function serviceTerms(){

        return $this->fetch('service_terms');

    }
    //隐私政策
    public function policyPrivacy(){

        return $this->fetch('service_terms');

    }
    //定价
    public function pricing(){

        return $this->fetch('pricing');

    }
    //西方占星术
    public function westernAstrology(){

        return $this->fetch('western_astrology');

    }
    //pdf专栏
    public function pdfHoroscope(){

        return $this->fetch('pdf_horoscope');

    }
    //每日运势
    public function dailyHoroscope(){

        return $this->fetch('daily_horoscope');

    }
    //常见问题
    public function faq(){

        return $this->fetch('faq');

    }
    //参看文档
    public function docs(){

        return $this->fetch();

    }
    //联系我们
    public function contact(){

        return $this->fetch();

    }
    //我们的团队
    public function team(){

        return $this->fetch();

    }
    //关于我们
    public function about(){

        return $this->fetch();

    }

}
