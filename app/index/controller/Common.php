<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\index\controller;

use app\common\controller\ControllerBase;

class Common extends ControllerBase
{
    /**
     * 宣传记录无分页列表
     */
    public function column()
    {
        if(isset($this->param['cid']) and $this->param['cid']!='all'){
            $where['cid'] = $this->param['cid'];
        }


        $end_time=strtotime(date('Y-m-d',time()))+86400;
        $start_time=$end_time-86400*30;

        !empty($this->param['start_time']) && $start_time = strtotime($this->param['start_time']);
        !empty($this->param['end_time']) && $end_time = strtotime($this->param['end_time']);
        $where['create_time']=['between',[$start_time,$end_time]];

        $step = (int)(($end_time - $start_time) / 86400);

        if($step<1){
            $data[]=['code'=>'1','mdg'=>'天数不是小于1'];
            exit(json_encode(['code'=>'0','data'=>$data]));
        }

        $propagandaCourseLog_data=$this->logicPropagandaCourseLog->getPropagandaCourseLogColumn($where);


        $biali=array();
        foreach ($propagandaCourseLog_data  as $key=>$vole){

            $create_time=strtotime(date('Y-m-d',$vole['create_time']));

            $vole['date']=date('H:i:s',$vole['create_time']);
            $biali[$create_time][]=$vole;
        }
        //dump($biali);

        for ($i=0;$i<$step;$i++){

            //$timeKey = $date['start'] + $i * $date['step'];

        }
        $data=array();
        foreach ($biali as $keyb=>$valueb){
            $data[]=['count'=>count($valueb),'date'=>date('Y-m-d',$keyb),'list'=>$valueb];
        }
       exit(json_encode(['code'=>'0','data'=>$data]));
    }


}
