<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\gxpmzg\controller;
use GuzzleHttp\Command\Guzzle\GuzzleClient;
/**
 * 通知公告控制器
 */
class Notice extends AdminBase
{

    /**
     * 通知公告列表
     */
    public function noticeList()
    {


        $where = $this->logicNotice->getWhere($this->param);

        $data=$this->logicNotice->getNoticeList($where);

        $this->assign('list',$data);
        $this->assign('event',['1'=>'app内部消息','2'=>'手机通知栏消息','3'=>'都是']);

        return $this->fetch('notice_list');
    }
    /**
     * api通知公告无分页列表
     */
    public function noticeColumn()
    {

        $data=$this->logicNotice->getNoticeColumn($this->param_data);

		return $this->apiReturn($data);
    }
    /**
     * 通知公告添加
     */
    public function noticeAdd()
    {

        IS_POST && $this->jump($this->logicNotice->noticeEdit($this->param));

        $info['start_time']= date("Y-m-d H:i:s");

        $this->assign('info', $info);

        return $this->fetch('notice_edit');
		
    }
    /**
     * 通知公告修改
     */
    public function noticeEdit()
    {

        IS_POST && $this->jump($this->logicNotice->noticeEdit($this->param));

        $info = $this->logicNotice->getNoticeInfo(['id' => $this->param['id']], '*');

        $info['start_time']=date('Y-m-d H:i:s',$info['start_time']);

        $this->assign('info', $info);

        return $this->fetch('notice_edit');
    }
    /**
     * 通知公告删除
     */
    public function noticeDel($id)
    {
        $this->jump($this->logicNotice->noticeDel(['id' => $id]));
    }
}
