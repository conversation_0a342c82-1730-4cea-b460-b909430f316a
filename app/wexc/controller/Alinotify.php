<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\wexc\controller;
use app\common\controller\ControllerBase;
use exwechat\exLog;
/**
 * 余额充值记录控制器
 */
class Alinotify extends ControllerBase
{
    /**
     * 余额充值记录删除
     */
    public $redata = array();////解析数据

    public function _initialize()
    {
        $this->redata=$_POST;
    }

    //支付验证
    public function payNotify()
    {
        include_once EXTEND_PATH . '/alipay/aop/AopClient.php';
        $aop = new \AopClient;
        $aop->alipayrsaPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAplReconkFt09T6F5b+p9MJsdsNM6yM9p+VESogpn2VP//prAzwqtUs2JwT2WVH2JFbcreJpFLAFyP07fIHtbLpR64XO67QY6Gv+cvIKD6J7SAHqRCIMtvVGbq+COaaFBAOXWGcHGb5x4cLQ4SgVVu5nNOe/oScyARuEJqw6BUqPuZuVeHiJdkKf7q9vbX4TqhPKhC8Yta1g2pNlAfAjnox3LU/KXE7r1Oadpvs2maVOWLq+9sFIo0nv+eGMqtoIKXXBdiclPVcIGD7qE3YxERSU/Gl0XvpO67jqvoq0cGbMPIBsieikv8UrR51jHyB5rgbRgPwOcK/kALu/JXOkDxwIDAQAB';
        $flag = $aop->rsaCheckV1($this->redata, NULL, "RSA2");

        if($flag){
            if($this->redata['trade_status'] == 'TRADE_SUCCESS' || $this->redata['trade_status'] == 'TRADE_FINISHED'){

                $whereLog['order_number'] = $this->redata['out_trade_no'];
                $whereLog['status'] = 0;
                if (strpos($this->redata['out_trade_no'], "C") !== false) {
                    $amountLogInfo = $this->logicAmountLog->getAmountLogInfo($whereLog,  'id,status,user_id,money');
                    $this->payAmountAdd($amountLogInfo);

                }elseif (strrpos($this->redata['out_trade_no'], "G") !== false){
                    $goldLogInfo = $this->logicGoldLog->getGoldLogInfo($whereLog,  'id,status,user_id,money');
                    $this->payGoldAdd($goldLogInfo);
                }
                exLog::log($this->redata, 'post');
                echo "success";
            }else{
                echo "fail";	//请不要修改或删除
            }
        }else{
            echo "fail";	//请不要修改或删除
        }
    }
    //积分增加
    public function payAmountAdd($amountLogInfo)
    {
        if (!empty($amountLogInfo)) {
            $amount = $this->redata['total_amount'];
            $weiUserData['id']  = $amountLogInfo['user_id'];
            $this->logicUser->setUserIncDec($weiUserData, 'amount', $amount,'setInc');
            $amountLog['id'] = $amountLogInfo['id'];
            $amountLog['status'] = 1;
            $amountLog['transaction_id'] = $this->redata['trade_no'];
            $this->logicAmountLog->amountLogEdit($amountLog);
        }
    }
    //金币增加
    public function payGoldAdd($goldLogInfo)
    {
        if (!empty($goldLogInfo)) {
            $amount = $this->redata['total_amount'];
            $weiUserData['id']  = $goldLogInfo['user_id'];
            $this->logicWeiUser->setWeiUserIncDec($weiUserData, 'gold', $amount,'setInc');
            $goldLog['id'] = $goldLogInfo['id'];
            $goldLog['status'] = 1;
            $goldLog['transaction_id'] = $this->redata['trade_no'];
            $this->logicGoldLog->goldLogEdit($goldLog);
        }
    }
}
