<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\chart;

use astrology\SweTest as SweTest;

/**
 * 单盘数据生成
 */
class Natal extends PlateSingle
{

    /**
     * 获取站点导航搜索条件
     */
    public function plateData($param)
    {
        $house = $param['birth_points'] . ',' . $param['h_sys'];

        $planet_degree=array();
        !empty($param['planet_degree']) && $planet_degree=$param['planet_degree'];

        $allow_degree=$this->getAllowDegree($param);

        $starsCode = $param['planets'];

        $birthdayToTime = strtotime($param['birthday']) - $param['time_zone'] * 3600 - $param['is_summer'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $planets = implode('', $starsCode);

        $arr = [
            'b' => $utdatenow,
            'p' => $planets,
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $exSweTest = get_sington_object('exSweTest', SweTest::class);
        $starsCode=$this->getStarsCode($param,$starsCode);

        $ay=false;
        if(isset($param['ay'])){
            $ay=$param['ay'];
        }

        $data = $exSweTest->calculate($arr, $starsCode,[],$ay);

        $planets_data['house'] = $this->housePlanet($exSweTest, $data);
        $sign_attribute = $this->signPlanet($data);
        $planets_data['sign'] = $sign_attribute['sign'];

        $data['chartType']=1;
        $planets_data['planet'] = $this->planetPhase($exSweTest, $data, $allow_degree,$planet_degree);

        if(!empty($param['svg_type']) and $param['svg_type']==1){
            $planets_data['svg'] = $this->simpleSvg($planets_data, $data,$param['format']);
        }else if(!empty($param['svg_type']) and $param['svg_type']==-1){

        }else{
            $planets_data['svg'] = $this->seniorSvg($planets_data, $data);
        }
        $planets_data['attribute'] = $sign_attribute['attribute'];

        if(!empty($param['is_corpus']) && $param['is_corpus']==1){
            $planets_data['interpretation']['content']= $this->explainData($param,$exSweTest);
        }
        return $planets_data;
    }


    //获取一句话解释
    public function explainData($param,$exSweTest)
    {
        $house = $param['birth_points'] . ',' . $param['h_sys'];

        !empty($param['planet_degree']) && $planet_degree=$param['planet_degree'];

        !empty($param['phase']) && $allow_degree=$param['phase'];

        $birthdayToTime = strtotime($param['birthday']) - $param['time_zone'] * 3600 - $param['is_summer'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $arr = [
            'b' => $utdatenow,
            'p' => '0123456',
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $data = $exSweTest->calculate($arr, ['0', '1', '2', '3', '4', '5', '6',   'virtual' => ['10']]);

        $planet=$data['planet'];

        $content='您的上升是'.$planet[7]['sign']['sign_chinese'].'座，太阳是'.$planet[0]['sign']['sign_chinese'].'座，月亮是'.$planet[1]['sign']['sign_chinese'].'座';

        $life_lin_score = array();
        foreach ($planet as $key => $value) {

            if ($key < 7) {
                $life_array[$key] = $value;   //本命先天数据

                $life_lin_score[$value['code_name']] = $this->calculatePlanConstellationNew($value["sign"]['sign_id'], $value['code_name'], (int)$value["sign"]["deg"]);
            }
        }
        arsort($life_lin_score);

        $new=array_keys($life_lin_score);

        $plantc["0"]="你星盘中太阳能量最强，会有很强烈的自我认同感，你能够成为万人仰慕的那一个发光发热的人。你最适合去做能获得所有人的目光与关注的工作，似乎这样你才能够得到自我认同。";
        $plantc["1"]="你星盘中月亮能量最强，你拥有丰富的情绪与想象力，你会很容易感知他人的情绪并作出对应的行为。你很适合去从事有照顾类型、群众工作或心灵相关的事业。";
        $plantc["2"]="你星盘中水星能量最强，你喜欢交流、思维敏捷，对于你而言，一切相互的事情都建立在沟通上，你适合从事和文学，教育，旅游，创意想法有关的行业，那会是你的优势领域。";
        $plantc["3"]="你星盘中金星能量最强，你有与生俱来的魅力，能够恰到好处去获得大家的好感，你拥有天生的美感，很容易把握潮流趋势，你适合做和美有关的工作，比如艺术类，美容类，甚至协调人和人之间的关系，让关系更美好的工作也适合你。";
        $plantc["4"]="你星盘中火星能量最强，你精力充沛，热情似火，胆识过人。你拥有开创力与拼搏精神，每次你需要去做一件事情的时候你一定会冲在前面，你也会是勇往直前的那一个。任何需要勇敢和爆发力的工作都适合你，比如运动员，医生，警察，军人，消防员，销售工作等。";
        $plantc["5"]="你星盘中木星能量最强，你非常慷慨，心胸开阔，天生给人乐观和自信的感觉，你追求心智的提升，你对宗教和哲学方面具有浓厚兴趣，你适合从事传播类型的工作，不管是思想的传播还是实物的传播，比如宗教、法律、教育，和哲学，旅游，国际贸易等有关的工作。 ";
        $plantc["6"]="你星盘中土星能量最强，对世界中的权利、地位、权威有强烈的野心，而这些通常透过商业、科学和政治表达出来，你认真负责，组织能力强，你适合从事和组织结构有关的工作，比如所有的管理工作，和科学技术，建筑结构有关的工作。";

        return $content.$plantc[$new[0]];
    }

    //行星落入星座计算分值
    public function calculatePlanConstellationNew($constellation_name, $plan_name, $degree)
    {

        $score = 0; //分值
        $constellation['1'] = '0';
        $constellation['2'] = '1';
        $constellation['3'] = '2';
        $constellation['4'] = '3';
        $constellation['5'] = '4';
        $constellation['6'] = '5';
        $constellation['7'] = '6';
        $constellation['8'] = '7';
        $constellation['9'] = '8';
        $constellation['10'] = '9';
        $constellation['11'] = '10';
        $constellation['12'] = '11';

        $planet['1'] = '0';
        $planet['2'] = '1';
        $planet['3'] = '2';
        $planet['4'] = '3';
        $planet['5'] = '4';
        $planet['6'] = '5';
        $planet['7'] = '6';


        //数组模式方便入库。


        $assoc[1] = [5 => 5, 4 => 1, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 12], 3 => [12, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [5, 1, 4], -5 => 3, -4 => 6];

        $assoc[2] = [5 => 4, 4 => 2, 3 => [4, 2, 5], 2 => [4 => [0, 8], 3 => [8, 14], 6 => [14, 22], 7 => [22, 27], 5 => [27, 30]], 1 => [3, 2, 7], -5 => 4, -4 => false];

        $assoc[3] = [5 => 3, 4 => false, 3 => [7, 4, 6], 2 => [3 => [0, 6], 6 => [6, 12], 4 => [12, 17], 5 => [17, 24], 7 => [24, 30]], 1 => [6, 5, 1], -5 => 5, -4 => false];

        $assoc[4] = [5 => 2, 4 => 6, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [7, 13], 3 => [13, 19], 6 => [19, 26], 7 => [26, 30]], 1 => [4, 3, 2], -5 => 6, -4 => 4];

        $assoc[5] = [5 => 1, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 6], 4 => [6, 11], 7 => [11, 18], 3 => [18, 24], 5 => [24, 30]], 1 => [7, 6, 5], -5 => 6, -4 => false];

        $assoc[6] = [5 => 3, 4 => 3, 3 => [4, 2, 5], 2 => [3 => [0, 7], 4 => [7, 17], 6 => [17, 21], 5 => [21, 28], 7 => [28, 30]], 1 => [1, 4, 3], -5 => 5, -4 => 3];

        $assoc[7] = [5 => 4, 4 => 7, 3 => [7, 3, 6], 2 => [7 => [0, 6], 3 => [6, 14], 6 => [14, 21], 4 => [21, 24], 5 => [24, 30]], 1 => [2, 7, 6], -5 => 4, -4 => 0];

        $assoc[8] = [5 => 5, 4 => false, 3 => [4, 5, 2], 2 => [5 => [0, 7], 4 => [7, 11], 3 => [11, 19], 6 => [19, 24], 7 => [24, 30]], 1 => [5, 1, 4], -5 => 3, -4 => 1];

        $assoc[9] = [5 => 6, 4 => false, 3 => [1, 6, 7], 2 => [6 => [0, 12], 4 => [12, 17], 3 => [17, 21], 7 => [21, 26], 5 => [26, 30]], 1 => [3, 2, 7], -5 => 2, -4 => false];

        $assoc[10] = [5 => 7, 4 => 5, 3 => [4, 2, 5], 2 => [3 => [0, 7], 6 => [7, 14], 4 => [14, 22], 7 => [22, 26], 5 => [26, 30]], 1 => [6, 5, 1], -5 => 1, -4 => 5];

        $assoc[11] = [5 => 7, 4 => false, 3 => [7, 3, 6], 2 => [3 => [0, 7], 4 => [7, 13], 6 => [13, 20], 5 => [20, 25], 7 => [25, 30]], 1 => [4, 3, 2], -5 => 0, -4 => false];

        $assoc[12] = [5 => 6, 4 => 4, 3 => [4, 5, 2], 2 => [4 => [0, 12], 6 => [12, 16], 3 => [16, 19], 5 => [19, 2], 7 => [28, 30]], 1 => [7, 6, 5], -5 => 2, -4 => 2];

        //真正计算在线面，上面是方便入库数组模式

        //判断星座是否存在
        if (!$constellation_id = array_search($constellation_name, $constellation)) {
            return false;
        }
        //判断行星是否存在
        if (!$plan_id = array_search($plan_name, $planet)) {
            return false;
        }
        //计算5分
        if ($assoc[$constellation_id][5] == $plan_id) {
            $score += 5;
        }
        //计算4分
        if ($assoc[$constellation_id][4] == $plan_id && $assoc[$constellation_id][4]!=false) {
            $score += 4;
        }
        //计算3分
        if (array_search($plan_id, $assoc[$constellation_id][3]) > -1) {
            $score += 3;
        }
        //计算2分
        if (!empty($assoc[$constellation_id][2][$plan_id])) {

            $degree_array = $assoc[$constellation_id][2][$plan_id];
            if ($degree >= $degree_array[0] and $degree < $degree_array[1]) {
                $score += 2;
            }
        }
        //计算1分
        if (($index_key = array_search($plan_id, $assoc[$constellation_id][1])) > -1) {
            if ($degree >= $index_key * 10 and $degree < ($index_key + 1) * 10) {
                $score += 1;
            }
        }
        //计算-5分

        if ($assoc[$constellation_id][-5] == $plan_id) {
            $score -= 5;
        }
        //计算-4分
        if ($assoc[$constellation_id][-4] == $plan_id && $assoc[$constellation_id][-4]!=false) {
            $score -= 5;
        }

        return $score;
    }

}
