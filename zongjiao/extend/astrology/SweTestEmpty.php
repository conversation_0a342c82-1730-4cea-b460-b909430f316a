<?php

namespace astrology;


class SweTestEmpty
{
    public $sweph = './sweph/';

    public function calculatePlanet($conditions, $starsCode)
    {
        $planetOut = $this->SweTest($conditions, $starsCode);

        $p_count = count($starsCode);
        if (!empty($starsCode['virtual'])) {
            $p_count = $p_count + count($starsCode['virtual']) - 1;
        }
        if (!empty($starsCode['planet_xs'])) {
            $p_count = $p_count + count($starsCode['planet_xs']) - 1;
        }

        foreach ($planetOut as $keyCode => $lineCode) {
            if ($keyCode < $p_count) {
                $planet[] = $lineCode;
            } else {
                break;
            }

        }
        return $planet;
    }

    public function SweTest($conditions, $starsCode)
    {


        $planetOut = $this->SweTestQuery($conditions);

        $planetEnglish = planetName::$planetEnglish;

        $planet_array = array();

        $p_count = $this->house_one_index($starsCode);


        foreach ($planetOut as $key => $value) {
            $planetinfo = explode(',', str_replace(' ', '', $value));

            $planet_array[$planetinfo[0]] = $planetinfo;
        }

        foreach ($starsCode as $keyCode => $lineCode) {

            if (is_array($lineCode)) {
                if ($keyCode == 'virtual') {

                    foreach ($lineCode as $keyX => $lineX) {
                        $function = 'calculate' . $lineX;
                        $virtual_planet = $this->$function($planet_array, $conditions);
                        if (!empty($virtual_planet)) {
                            array_splice($planetOut, $p_count, 0, implode(",", $virtual_planet));
                            $p_count++;
                        } else {
                        }
                    }
                } elseif ($keyCode == 'planet_xs') {
                    foreach ($lineCode as $keyX => $lineX) {
                        $planet_X = $this->calculateplanetXs($conditions, $lineX);
                        array_splice($planetOut, $p_count, 0, $planet_X);
                        $p_count++;
                    }
                }

            } else {
                $lineCodeEnglish = $planetEnglish[$lineCode];
                $planetinfo_str = $planetOut[$keyCode];
                $planetinfo = explode(',', str_replace(' ', '', $planetinfo_str));

                if ($lineCodeEnglish == $planetinfo['0']) {

                } else {
                    if ($lineCodeEnglish) {

                    }
                }
            }
        }

        return $planetOut;
    }

    //通过数据拿行星列表

    public function SweTestQuery($conditions)
    {
        $query = $this->sweph . "swetest -edir" . $this->sweph . ' ';

        if (is_array($conditions)) {
            $options = [];
            foreach ($conditions as $key => $value) {
                $options[] = is_int($key) ? '-' . $value : '-' . $key . $value;
            }
            $query .= implode(' ', $options);
        } else {
            $query .= $conditions;
        }

        exec($query, $planetOut);

        return $planetOut;
    }

    //直接拿数据

    public function house_one_index($starsCode)
    {
        $house_one = count($starsCode);
        if (!empty($starsCode['virtual'])) {
            $house_one = $house_one - 1;
        }
        if (!empty($starsCode['planet_xs'])) {
            $house_one = $house_one - 1;
        }
        if (!empty($starsCode['planet_xf'])) {
            $house_one = $house_one - 1;
        }
        if (!empty($starsCode['planet_hel'])) {
            $house_one = $house_one - 1;
        }
        return $house_one;
    }

    //计算宫位从第几个开始

    function calculateplanetXs($conditions, $xs)
    {

        $conditions['p'] = 's';
        $conditions['xs'] = ltrim($xs,"xs");

        $planetOut = $this->SweTestQuery($conditions);

        if (!empty($planetOut)) {
            $lineInfo = str_replace(' ', '', $planetOut[0]);
            return $lineInfo;
        }

    }

    //福点计算  PartOfFortune

    public function calculate($conditions, $starsCode, $planetOut = [])
    {

        if (empty($planetOut)) {
            $planetOut = $this->SweTest($conditions, $starsCode);
        }
        $p_count = count($starsCode);
        if (!empty($starsCode['virtual'])) {
            $p_count = $p_count + count($starsCode['virtual']) - 1;
        }
        if (!empty($starsCode['planet_xs'])) {
            $p_count = $p_count + count($starsCode['planet_xs']) - 1;
        }

        $house_data = array();
        $planet_data = array();
        $ascmcs_data = array();
        $planet_english = planetName::$planetEnglish;
        $planet_chinese = planetName::$planetChinese;
        $data['planetEnglish'] = planetName::$planetEnglish;
        $data['planetChinese'] = planetName::$planetChinese;
        $data['planetFont'] = planetName::$planetFont;
        $data['house_life'] = planetName::$house_life;
        $data['signFont'] = planetName::$signFont;
        $data['signEnglish'] = planetName::$signEnglish;
        $data['sign_guardian_index'] = planetName::$sign_guardian_index;
        $data['sign_phase'] = planetName::$sign_phase;
        $data['signChinese'] = planetName::$signChinese;

        foreach ($planetOut as $key => $line) {

            $lineInfo = explode(',', str_replace(' ', '', $line));
            if ($lineInfo[0] == 'house1') {
                $p_count = $key;
            }

            if (isset($lineInfo[1])) {

                $longitude_array['sign'] = $this->Convert_sign_Longitude($lineInfo[1]);

                $longitude_array['longitude'] = $lineInfo[1];

                if (!empty($lineInfo[2])) {
                    $longitude_array['speed'] = $lineInfo[2];
                } else {
                    $longitude_array['speed'] = 360;
                }

                $planet_info = $longitude_array;

                if ($key < $p_count) {
                    $planet_info['planet_english'] = str_replace('.', '', $lineInfo[0]);
                    $planet_info['code_name'] = (string)array_search($planet_info['planet_english'], $planet_english);

                    $planet_info['planet_chinese'] = $planet_chinese[$planet_info['code_name']];

                    if (!empty($lineInfo[3])) {
                        $planet_info['house_number'] = $lineInfo[3];
                    }
                    $planet_data[] = $planet_info;
                } elseif ($key >= $p_count and $key < ($p_count + 12)) {
                    $house_info = $longitude_array;
                    $house_info['house_id'] = $key - $p_count + 1;

                    $house_data[] = $house_info;
                } elseif ($key >= ($p_count + 12)) {
                    $planet_info['planet_english'] = str_replace('.', '', $lineInfo[0]);
                    $planet_info['code_name'] = (string)array_search($planet_info['planet_english'], $planet_english);

                    $planet_info['planet_chinese'] = $planet_chinese[$planet_info['code_name']];
                    if (!empty($lineInfo[3])) {
                        $planet_info['house_number'] = $lineInfo[3];
                    }
                    if ($key == ($p_count + 12)) {
                        $data['planet_ascendant'] = $planet_info;
                    }
                    $ascmcs_data[] = $planet_info;
                }
            }
        }


        $data['ascmcs'] = $ascmcs_data;
        $data['planet'] = $planet_data;
        $data['house'] = $house_data;


        return $data;
    }

    //上升

    function Convert_sign_Longitude($longitude)
    {
        $longitude = $this->crunch($longitude);
        $sign_num = intval($longitude / 30);
        $pos_in_sign = $longitude - ($sign_num * 30);
        $deg = intval($pos_in_sign);
        $full_min = ($pos_in_sign - $deg) * 60;
        $min = intval($full_min);
        $full_sec = round(($full_min - $min) * 60);

        $signEnglish = planetName::$signEnglish;

        $signChinese = planetName::$signChinese;

        $signFont = planetName::$signFont;

        return ['deg' => $deg, 'min' => $min, 'sec' => $full_sec, 'sign_id' => $sign_num, 'sign_english' => $signEnglish[$sign_num], 'sign_chinese' => $signChinese[$sign_num], 'sign_font' => $signFont[$sign_num]];
    }

    //中天

    public function crunch($x)
    {
        if ($x >= 0) {
            $y = $x - floor($x / 360) * 360;
        } else {
            $y = 360 + ($x - ((1 + floor($x / 360)) * 360));
        }

        return $y;
    }

    //东升点

    function calculatepFortune($planet_data, $conditions)
    {

        if (empty($planet_data['Sun']) or empty($planet_data['Moon']) or empty($planet_data['house1']) or empty($planet_data['house7'])) {

            return false;
        }
        $Sum = $planet_data['Sun'][1];
        $Moon = $planet_data['Moon'][1];
        $house1 = $planet_data['house1'][1];
        $house7 = $planet_data['house7'][1];

        if ($house1 > $house7) {
            if ($Sum <= $house1 And $Sum > $house7) {
                $day_chart = True;
            } else {
                $day_chart = False;
            }
        } else {
            if ($Sum > $house1 And $Sum <= $house7) {
                $day_chart = False;
            } else {
                $day_chart = True;
            }
        }
        if ($day_chart == True) {
            $Fortune_longitude = $house1 + $Moon - $Sum;
        } else {
            $Fortune_longitude = $house1 - $Moon + $Sum;
        }

        if ($Fortune_longitude >= 360) {
            $Fortune_longitude = $Fortune_longitude - 360;
        }

        if ($Fortune_longitude < 0) {
            $Fortune_longitude = $Fortune_longitude + 360;
        }

        $planet_info[0] = "PartOfFortune";
        $planet_info[1] = $Fortune_longitude;
        $planet_info[2] = "360";


        return $planet_info;
    }

    //宿命点

    function calculate10($planet_data, $conditions)
    {
        if (empty($planet_data['Ascendant'])) {
            return false;
        }
        $planet_info = $planet_data['Ascendant'];
        $planet_info[2] = 1;
        $planet_info[3] = 1;
        return $planet_info;
    }

    //下降

    function calculate11($planet_data, $conditions)
    {
        if (empty($planet_data['MC'])) {
            return false;
        }
        $planet_info = $planet_data['MC'];
        $planet_info[2] = 1;
        $planet_info[3] = 10;

        return $planet_info;
    }

    //天底

    function calculate14($planet_data, $conditions)
    {
        if (empty($planet_data['equat.Asc.'])) {
            return false;
        }
        $planet_info = $planet_data['equat.Asc.'];
        $planet_info[0] = 'equatAsc';
        return $planet_info;
    }

    //日月中

    function calculate13($planet_data, $conditions)
    {
        if (empty($planet_data['Vertex'])) {
            return false;
        }
        $planet_info = $planet_data['Vertex'];

        return $planet_info;
    }

    //南交

    function calculate18($planet_data, $conditions)
    {
        if (empty($planet_data['house7'])) {
            return false;
        }
        $planet_info = $planet_data['house7'];
        $planet_info[0] = 'Des';
        $planet_info[2] = 360;
        $planet_info[3] = '7';
        return $planet_info;
    }

    //小行星获取

    function calculate19($planet_data, $conditions)
    {
        if (empty($planet_data['house4'])) {
            return false;
        }
        $planet_info = $planet_data['house4'];
        $planet_info[0] = 'IC';
        $planet_info[2] = 360;
        $planet_info[3] = 4;
        return $planet_info;
    }

    //落入星座计算

    function calculate20($planet_data, $conditions)
    {
        if (empty($planet_data['Sun']) or empty($planet_data['Moon'])) {
            return false;
        }
        $Sum = (float)$planet_data['Sun'][1];
        $Moon = (float)$planet_data['Moon'][1];

        $true_average = ($Sum + $Moon) / 2;

        $diff = abs($Sum - $Moon);

        if ($diff >= 180 And Abs($true_average - $Sum) > 90 And Abs($true_average - $Moon) > 90) {
            $true_average = $true_average + 180;
        }

        $true_average = $this->crunch($true_average);

        $planet_info[0] = 'Sun-Moon';
        $planet_info[1] = $true_average;
        $planet_info[2] = 360;
        return $planet_info;
    }

    //落入星座计算

    function calculate21($planet_data, $conditions)
    {
        if (empty($planet_data['meanNode'])) {
            $conditions['p'] = 'm';
            $planetOut = $this->SweTestQuery($conditions);
            $lineInfo = explode(',', str_replace(' ', '', $planetOut[0]));
            $true_average = $lineInfo[1] + 180;
        } else {
            $true_average = $planet_data['meanNode'][1] + 180;
        }


        $planet_info[0] = 'meanSouthNode';
        $planet_info[1] = $true_average;
        $planet_info[2] = 360;
        $planet_info[3] = '7';

        return $planet_info;

    }

    //小数转换度数

    function Convert_sign_deg_min($longitude)
    {
        $longitude = $this->crunch($longitude);
        $sign_num = intval($longitude / 30);
        $pos_in_sign = $longitude - ($sign_num * 30);
        $deg = intval($pos_in_sign);
        $full_min = ($pos_in_sign - $deg) * 60;
        $min = intval($full_min);
        $full_sec = round(($full_min - $min) * 60);

        return ['deg' => $deg, 'min' => $min, 'sec' => $full_sec];
    }

    //计算合位数据

    function Convert_deg_min($longitude)
    {
        $longitude = $this->crunch($longitude);
        $deg = intval($longitude);
        $full_min = ($longitude - $deg) * 60;
        $min = intval($full_min);
        $full_sec = round(($full_min - $min) * 60);

        return ['deg' => $deg, 'min' => $min, 'sec' => $full_sec];
    }

    //万能计算点数数据

    function transitsProgressed($arr, $Progressed, $current_cycle)
    {

        $planetOut = $this->SweTestQuery($arr);

        $minDell = 99999;

        $newMoon = '';

//        foreach ($planetOut as $key => $line) {
//
//            $lineInfo = explode($arr["g"], $line);
//
//            $defug = trim($lineInfo[2], ' ');
//
//            $Progressed_defug = $Progressed - abs($defug);
//
//            if ($minDell > abs($Progressed_defug)) {
//                $minDell = abs($Progressed_defug);
//                $newMoon = str_replace(' UT', '', $lineInfo[1]);
//            }
//
//        }

        foreach ($planetOut as $key => $line) {

            if ($key == (count($planetOut) - 1)) {
                break;
            }

            $lineInfo = explode($arr["g"], $line);
            $defug = trim($lineInfo[2], ' ');


            $lineInfo_last = explode($arr["g"], $planetOut[$key + 1]);
            $defug_last = trim($lineInfo_last[2], ' ');

            $lineInfo_cha = abs($defug - $defug_last);

            if ($defug > $defug_last) {
                if ($lineInfo_cha > 180) {
                    if ($defug <= $Progressed or $defug_last > $Progressed) {
                        $newMoon = str_replace(' UT', '', $lineInfo[1]);
                        break;
                    }
                } else {
                    if ($defug >= $Progressed and $defug_last < $Progressed) {
                        $newMoon = str_replace(' UT', '', $lineInfo[1]);
                        break;
                    }
                }
            } else {
                if ($lineInfo_cha > 180) {
                    if ($defug <= $Progressed or $defug_last > $Progressed) {
                        $newMoon = str_replace(' UT', '', $lineInfo[1]);
                        break;
                    }
                } else {
                    if ($defug <= $Progressed and $defug_last > $Progressed) {
                        $newMoon = str_replace(' UT', '', $lineInfo[1]);
                        break;
                    }
                }
            }
        }
        if (isset($newMoon)) {
            $newMoon_time = strtotime($newMoon);
            $arr['b'] = date('d.m.Y', $newMoon_time);
        } else {
            $current_cycle = '';
            $newMoon_time = '';
        }


        if ($current_cycle == 'd') {
            $arr['ut'] = '0:00:0';
            $arr['n'] = '25';
            $arr['s'] = '60m';
            $current_cycle = 'h';
        } else if ($current_cycle == 'h') {
            $arr['ut'] = date('H', $newMoon_time) . ':00:00';
            $arr['n'] = '61';
            $arr['s'] = '1m';
            $current_cycle = 'm';
        } else if ($current_cycle == 'm') {
            $arr['ut'] = date('H:i', $newMoon_time) . ':00';
            $arr['n'] = '61';
            $arr['s'] = '1s';
            $current_cycle = 's';
        } else {
            $current_cycle = '';
        }

        if ($current_cycle != '') {

            return $this->transitsProgressed($arr, $Progressed, $current_cycle);

        }
        return $newMoon_time;

    }

    //获取范围内的数据

    function universalPhaseProgressed($arr, $time = 2505600)
    {
        $towday = strtotime($arr['b'] . ' ' . $arr['ut']) + $time;
        $planetOut = $this->SweTestQuery($arr);
        $universalList = array();

        foreach ($planetOut as $key => $line) {
            $lineInfo = explode(',', str_replace(' ', '', $line));
            $lineInfo[2] = strtotime(str_replace('UT', '', $lineInfo[2]));
            $lineInfo[6] = date('Y-m-d H:i:s', $lineInfo[2]);
            if ($towday >= $lineInfo[2]) {
                $universalList['start'][$lineInfo[0]][] = $lineInfo;
            }
            if ($towday <= $lineInfo[2]) {
                $universalList['end'][$lineInfo[0]][] = $lineInfo;
            }
        }
        return $universalList;
    }

    //万能计算点数数据

    function universalProgressedC($arr)
    {
        $towday = strtotime($arr['b'] . ' ' . $arr['ut']) + 29 * 86400;
        $planetOut = $this->SweTestQuery($arr);
        $universalList = array();
        foreach ($planetOut as $key => $line) {
            $lineInfo = explode(',', str_replace(' ', '', $line));
            $lineInfo[2] = strtotime(str_replace('UT', '', $lineInfo[2]));
            $lineInfo[6] = date('Y-m-d H:i:s', $lineInfo[2]);

            if ($towday >= $lineInfo[2]) {
                $universalList['start'][$lineInfo[0]][] = $lineInfo;
            }
            if ($towday <= $lineInfo[2]) {
                $universalList['end'][$lineInfo[0]][] = $lineInfo;
            }


        }

        return $universalList;
    }

    function universalProgressed($arr, $P_name, $Progressed, $current_cycle)
    {
        $planetOut = $this->SweTestQuery($arr);

        $minDell = 99999;
        $newTime = '';
        foreach ($planetOut as $key => $line) {
            $lineInfo = explode(',', str_replace(' ', '', $line));

            if ($P_name == $lineInfo[0]) {
                $defug = $lineInfo[2];

                $Progressed_defug = $Progressed - abs($defug);

                if ($minDell > abs($Progressed_defug)) {

                    $minDell = abs($Progressed_defug);
                    $newTime = str_replace('UT', '', $lineInfo[1]);
                }
            }
        }
        $newMoon_time = strtotime($newTime);

        $arr['b'] = date('d.m.Y', $newMoon_time);


        if ($current_cycle == 'd') {
            $arr['ut'] = '0:30:0';
            $arr['n'] = '24';
            $arr['s'] = '60m';
            $current_cycle = 'h';
        } else if ($current_cycle == 'h') {
            $arr['ut'] = date('H', $newMoon_time) . ':00:30';
            $arr['n'] = '60';
            $arr['s'] = '1m';
            $current_cycle = 'm';
        } else if ($current_cycle == 'm') {
            $arr['ut'] = date('H:i', $newMoon_time) . ':00';
            $arr['n'] = '60';
            $arr['s'] = '1s';
            $current_cycle = 's';
        } else {
            $current_cycle = '';
        }

        if ($current_cycle != '') {

            return $this->universalProgressed($arr, $P_name, $Progressed, $current_cycle);

        }
        return $newMoon_time;
    }
}


