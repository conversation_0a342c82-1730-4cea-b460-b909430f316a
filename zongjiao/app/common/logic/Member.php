<?php
// +---------------------------------------------------------------------+
// | ThinkTree  | [ WE CAN DO IT JUST THINK ]                            |
// +---------------------------------------------------------------------+
// | Author     | Bigotry <<EMAIL>>                             |
// +---------------------------------------------------------------------+
// | Repository | https://gitee.com/ThinkTree                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

/**
 * 会员逻辑
 */
class Member extends LogicBase
{

    /**
     * 获取会员信息
     */
    public function getMemberInfo($where = [], $field = true)
    {

        $info = $this->modelMember->getInfo($where, $field);

        $info['leader_nickname'] = $this->modelMember->getValue(['id' => $info['leader_id']], 'nickname');

        return $info;
    }
    /**
     * 获取会员无分页列表
     */
    public function getMemberColumn($where = [], $field = '', $key = '')
    {
        return $this->modelMember->getColumn($where, $field , $key);
    }
    /**
     * 获取会员列表
     */
    public function getMemberList($where = [], $field = 'm.*,b.nickname as leader_nickname', $order = '', $paginate = DB_LIST_ROWS)
    {

        $this->modelMember->alias('m');

        $join = [
            [SYS_DB_PREFIX . 'member b', 'm.leader_id = b.id', 'LEFT'],
        ];

        $where['m.' . DATA_STATUS_NAME] = ['neq', DATA_DELETE];

        $this->modelMember->join = $join;

        return $this->modelMember->getList($where, $field, $order, $paginate);
    }

    /**
     * 导出会员列表
     */
    public function exportMemberList($where = [], $field = 'm.*,b.nickname as leader_nickname', $order = '')
    {

        $list = $this->getMemberList($where, $field, $order, false);

        $titles = "昵称,用户名,邮箱,手机,注册时间,上级";
        $keys   = "nickname,username,email,mobile,create_time,leader_nickname";

        action_log('导出', '导出会员列表');

        export_excel($titles, $keys, $list, '会员列表');
    }

    /**
     * 获取会员列表搜索条件
     */
    public function getWhere($data = [])
    {

        $where = [];

        !empty($data['search_data']) && $where['m.nickname|m.username|m.email|m.mobile'] = ['like', '%'.$data['search_data'].'%'];

        if (!is_administrator()) {

            $member = session('member_info');

            if ($member['is_share_member']) {

                $ids = $this->getInheritMemberIds(MEMBER_ID);

                $ids[] = MEMBER_ID;

                $where['m.leader_id'] = ['in', $ids];

            } else {

                $where['m.leader_id'] = MEMBER_ID;
            }
        }

        return $where;
    }

    /**
     * 获取存在继承关系的会员ids
     */
    public function getInheritMemberIds($id = 0, $data = [])
    {

        $member_id = $this->modelMember->getValue(['id' => $id, 'is_share_member' => DATA_NORMAL], 'leader_id');

        if (empty($member_id)) {

            return $data;
        } else {

            $data[] = $member_id;

            return $this->getInheritMemberIds($member_id, $data);
        }
    }

    /**
     * 获取会员的所有下级会员
     */
    public function getSubMemberIds($id = 0, $data = [])
    {

        $member_list = $this->modelMember->getList(['leader_id' => $id], 'id', 'id asc', false);

        foreach ($member_list as $v)
        {

            if (!empty($v['id'])) {

                $data[] = $v['id'];

                $data = array_unique(array_merge($data, $this->getSubMemberIds($v['id'], $data)));
            }

            continue;
        }

        return $data;
    }

    /**
     * 会员添加到用户组
     */
    public function addToGroup($data = [])
    {

        $url = url('memberList');

        if (SYS_ADMINISTRATOR_ID == $data['id']) {

            return [RESULT_ERROR, '天神不能授权哦~', $url];
        }

        $where = ['member_id' => ['in', $data['id']]];

        $this->modelAuthGroupAccess->deleteInfo($where, true);

        if (empty($data['group_id'])) {

            return [RESULT_SUCCESS, '会员授权成功', $url];
        }

        $add_data = [];

        foreach ($data['group_id'] as $group_id) {

            $add_data[] = ['member_id' => $data['id'], 'group_id' => $group_id];
        }

        if ($this->modelAuthGroupAccess->setList($add_data)) {

            action_log('授权', '会员授权，id：' . $data['id']);

            $this->logicAuthGroup->updateSubAuthByMember($data['id']);

            return [RESULT_SUCCESS, '会员授权成功', $url];
        } else {

            return [RESULT_ERROR, $this->modelAuthGroupAccess->getError()];
        }
    }

    /**
     * 会员添加
     */
    public function memberAdd($data = [])
    {
        $data['access_token']=data_md5_key($data['username']);

        $validate_result = $this->validateMember->scene('add')->check($data);

        if (!$validate_result) {

            return [RESULT_ERROR, $this->validateMember->getError()];
        }

        $url = url('memberList');

        $data['nickname']  = $data['username'];
        $data['leader_id'] = MEMBER_ID;
        $data['is_inside'] = DATA_NORMAL;

        $data['password'] = data_md5_key($data['password']);

        $result = $this->modelMember->setInfo($data);

        $result && action_log('新增', '新增会员，username：' . $data['username']);

        return $result ? [RESULT_SUCCESS, '会员添加成功', $url] : [RESULT_ERROR, $this->modelMember->getError()];
    }

    /**
     * 会员access_token更新
     */
    public function updateAccessToken($data = [])
    {
        $data['access_token']=data_md5_key(mt_rand(0, 99).$data['id'].time());

        $url = url('memberInfo');

        $result = $this->modelMember->setInfo($data);

        $result && action_log('更新', 'access_token更新');

        return $result ? [RESULT_SUCCESS, 'access_token更新成功', $url] : [RESULT_ERROR, $this->modelMember->getError()];
    }



    /**
     * 会员编辑
     */
    public function memberEdit($data = [])
    {

        if(!empty($data['update_access_token'])){
            $data['access_token']=data_md5_key($data['nickname'].$data['mobile']);
        }
        unset($data['update_access_token']);
        $validate_result = $this->validateMember->scene('edit')->check($data);

        if (!$validate_result) {

            return [RESULT_ERROR, $this->validateMember->getError()];
        }

        $url = url('memberinfo');

        $result = $this->modelMember->setInfo($data);

        $result && action_log('编辑', '编辑会员，id：' . $data['id']);

        return $result ? [RESULT_SUCCESS, '会员编辑成功', $url] : [RESULT_ERROR, $this->modelMember->getError()];
    }

    /**
     * 修改密码
     */
    public function editPassword($data = [])
    {

        $validate_result = $this->validateMember->scene('password')->check($data);

        if (!$validate_result) {

            return [RESULT_ERROR, $this->validateMember->getError()];
        }

        $member = $this->getMemberInfo(['id' => $data['id']]);

        if (data_md5_key($data['old_password']) != $member['password']) {

            return [RESULT_ERROR, '旧密码输入不正确'];
        }

        $data['id'] = MEMBER_ID;

        $url = url('index/index');

        $data['password'] = data_md5_key($data['password']);

        $result = $this->modelMember->setInfo($data);

        $result && action_log('编辑', '会员密码修改，id：' . $data['id']);

        return $result ? [RESULT_SUCCESS, '密码修改成功', $url] : [RESULT_ERROR, $this->modelMember->getError()];
    }

    /**
     * 设置会员信息
     */
    public function setMemberValue($where = [], $field = '', $value = '')
    {

        return $this->modelMember->setFieldValue($where, $field, $value);
    }

    /**
     * 会员删除
     */
    public function memberDel($where = [])
    {

        $url = url('memberList');

        if (SYS_ADMINISTRATOR_ID == $where['id'] || MEMBER_ID == $where['id']) {

            return [RESULT_ERROR, '天神和自己不能删除哦~', $url];
        }

        $result = $this->modelMember->deleteInfo($where);

        $result && action_log('删除', '删除会员，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '会员删除成功', $url] : [RESULT_ERROR, $this->modelMember->getError(), $url];
    }
    /**
     * 会员修改指定的参数
     */
    public function memberWeiApp($data)
    {

        $url = url('Weiappid/weiAppidList');

        $result = $this->modelMember->setFieldValue(['id'=>MEMBER_ID], array_keys($data)[0], array_values($data)[0]);

        $info=session('member_info');
        $info['choose_appid']=array_values($data)[0];
        session('member_info', $info);

        getAccessToken($updata=true);
        $result && action_log('设置默认公众号主体ID', '操作appid，where：' . array_values($data)[0]);

        return $result ? [RESULT_SUCCESS, '操作默认appid成功', $url] : [RESULT_ERROR, $this->modelMember->getError(), $url];
    }
}
