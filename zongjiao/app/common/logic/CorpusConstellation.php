<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;
/**
 * 星座运势语料逻辑
 */
class CorpusConstellation extends LogicBase
{

    /**
 * 获取星座运势语料搜索条件
 */
    public function getWhere($data = [])
    {

        $where = [];

        isset($data['status']) && $where['status'] = $data['status'];

        isset($data['planet']) && $where['planet'] = $data['planet'];

        isset($data['constellation']) && $where['constellation'] = $data['constellation'];

        !empty($data['search_data']) && $where['planet|constellation|keywords|content|'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }

    /**
     * 获星座运势语料单条信息
     */
    public function getCorpusConstellationInfo($where = [], $field = '*')
    {

        return $this->modelCorpusConstellation->getInfo($where, $field);
    }

    /**
     * 获取星座运势语料列表-分页
     */

    public function getCorpusConstellationList($where = [], $field = '', $order = '', $paginate = 0)
    {
        return $this->modelCorpusConstellation->getList($where, $field, $order, $paginate);
    }

    /**
     * 获取星座运势语料列表-所有
     */

    public function getCorpusConstellationAllList($where = [], $field = '', $order = '', $paginate = false)
    {
        return $this->modelCorpusConstellation->getList($where, $field, $order, $paginate);
    }


    /**
     * 获取星座运势语料无分页列表
     */
    public function getCorpusConstellationColumn($where = [], $field = '', $key = '')
    {
        return $this->modelCorpusConstellation->getColumn($where, $field, $key);
    }

    /**
     * 星座运势语料单条编辑
     */
    public function corpusConstellationEdit($data = [])
    {

        $result = $this->modelCorpusConstellation->setInfo($data);

        return $result ? $result : $this->modelCorpusConstellation->getError();
    }

    /**
     * 星座运势语料删除
     */
    public function corpusConstellationDel($where = [], $is_true = false)
    {

        $result = $this->modelCorpusConstellation->deleteInfo($where, $is_true);

        return $result ? $result : $this->modelCorpusConstellation->getError();
    }


    //Admin模块操作

    /**
     * 获取星座运势语料搜索条件
     */
    public function getAdminWhere($data = [])
    {


        $where = [];


        if(isset($data['chartType']) and $data['chartType']!='all'){
            $where['chartType'] = $data['chartType'];
        }

        !empty($data['search_data']) && $where['oneself|other|keywords|content'] = ['like', '%'.$data['search_data'].'%'];

        return $where;
    }


    /**
     * 商城商品列表单条编辑
     */
    public function corpusConstellationAdminEdit($data = [])
    {


        $result = $this->modelCorpusConstellation->setInfo($data);

        $handle_text = empty($data['id']) ? '座运势语料列表' : '编辑';

        $result && action_log($handle_text, '座运势语料列表' . $handle_text . '，id：' . $data['id']);

        return $result ? [RESULT_SUCCESS, '座运势语料修改成功', url('corpusConstellationList')] : [RESULT_ERROR, $this->modelShopGoods->getError()];
    }
    /**
     * 星座运势语料删除
     */
    public function corpusConstellationAdminDel($where = [])
    {

        $result = $this->modelCorpusConstellation->deleteInfo($where);

        $result && action_log('删除', '星座运势语料删除，where：' . http_build_query($where));

        return $result ? [RESULT_SUCCESS, '星座运势语料删除成功'] : [RESULT_ERROR, $this->modelCorpusConstellation->getError()];
    }


    /**
     * 商城商品列表单条编辑
     */
    public function yiji($date_ime,$life_birth_time)
    {

        $data['lucky_number'] = 6;

        $lucky_day=date('dmY', $date_ime).date('dmY', $life_birth_time);

        chong:
        $lucky_day_array=str_split($lucky_day);
        $lucky_day=0;
        foreach ($lucky_day_array as $keyl=>$valuel){
            $lucky_day+=$valuel;
        }

        if($lucky_day>9){
            goto chong;
        }
        $data['lucky_number'] = $lucky_day;
        $data['lucky_color'] = '黄色';
        $data['lucky_bearing'] = '东北';

        $color[] = ["红色", "黄色", "绿色", "青色", "蓝色", "紫色", "黑色"];
        $color[] = ["朱红", "桔黄", "豆绿", "豆青", "天蓝", "紫罗兰色", "土黑"];
        $color[] = ["粉红", "深桔黄", "浅豆绿", "花青", "蔚蓝", "紫藤色", "棕色"];
        $color[] = ["梅红", "浅桔黄", "橄榄绿", "茶青", "月光蓝", "紫水晶色", "红棕"];
        $color[] = ["玫瑰红", "柠檬黄", "茶绿", "葱青", "海洋蓝", "葡萄紫", "金棕"];
        $color[] = ["桃红", "玉米黄", "葱绿", "天青", "海蓝", "茄皮紫", "铁锈棕"];
        $color[] = ["樱桃红", "橄榄黄", "苹果绿", "霁青", "湖蓝", "玫瑰紫", "桔棕"];
        $color[] = ["桔红", "樱草黄", "原野绿", "石青", "深湖蓝", "丁香紫", "橄榄棕"];
        $color[] = ["石榴红", "稻草黄", "森林绿", "铁青", "中湖蓝", "钴紫", "褐色"];
        $color[] = ["枣红", "芥末黄", "洋蓟绿", "蟹青", "浅湖蓝", "绛紫", "深褐"];
        $color[] = ["莲红", "杏黄", "苔藓绿", "鳝鱼青", "赤褐", "赤褐", "灰褐"];
        $color[] = ["灰色", "莲灰", "铅灰", "豆灰", "梅红", "蓝色", "绿色"];
        $color[] = ["银灰", "茶褐", "碳灰", "藕灰", "玫瑰红", "天蓝", "豆绿"];
        $color[] = ["铁灰", "紫褐", "驼灰", "天青", "桃红", "蔚蓝", "浅豆绿"];
        $flowers = array(
            array("牡丹", "月季", "兰花", "桂花", "菊花", "荷花", "水仙"),
            array("玫瑰", "百合", "茉莉", "杜鹃", "郁金香", "紫罗兰", "风信子"),
            array("夜来香", "丁香", "芍药", "君子兰", "蒲公英", "栀子花", "冬青"),
            array("桃花", "梅花", "素馨", "金盏花", "矢车菊", "薰衣草", "牛王花"),
            array("罂粟", "鸢尾花", "紫菀", "落新妇", "杜英", "玉兰", "塔菊"),
            array("穗穗金", "凌霄", "勿忘我", "满天星", "雏菊", "迎春花", "常春藤"),
            array("蓝莓花", "牵牛花", "金鸡菊", "木蓝", "桔梗", "翠菊", "薹草"),
            array("七里香", "油桐花", "洋水仙", "石斛兰", "龙胆", "虞美人", "凤仙花"),
            array("蝴蝶花", "火鹤花", "曼陀罗", "金盏黄", "黄槐花", "柳兰", "百日菊"),
            array("唐菖蒲", "海桐花", "葵花", "安哥拉", "萱草", "风车草", "白香花"),
            array("芝樱", "飞燕草", "金花", "鸡冠花", "锦葵", "仙客来", "五色梅"),
            array("雏菊", "铁线莲", "夜来香", "寿菊", "秋桜", "云雾菊", "百子莲"),
            array("阳光菊", "银盏花", "丹参花", "金鱼草", "睡莲", "马利筋", "鹿蹄草")
        );
        $stones = array(
            array("长石", "黑曜石", "石英", "黄玉", "绿松石", "孔雀石", "青金石"),
            array("红玛瑙", "蓝玛瑙", "黄玛瑙", "紫晶", "白玉", "软玉", "葡萄石"),
            array("青玉", "橄榄石", "黄金矿", "碧玺", "锡矿", "绿耀石", "磷灰石"),
            array("猫眼石", "蛋白石", "蓝宝石", "红宝石", "翡翠", "舍丹石", "孔雀石"),
            array("镉矾", "粉晶", "象牙石", "氧化铝", "石榴石", "草绿宝石", "珍珠"),
            array("琥珀", "黑玄武岩", "刚玉", "斜长石", "和田玉", "金星石", "云母石"),
            array("玻璃石", "彩虹石", "钻石", "海蓝宝石", "蓝铜矿", "烟熏石", "石榴石"),
            array("橄榄石", "黄铜矿", "方解石", "硅石", "海蓝宝石", "镉黄矿", "坦桑石"),
            array("紫水晶", "玻璃石", "隕石", "辉石", "影青石", "石碱", "杂石矿"),
            array("石膏", "蜜蜡石", "月石", "硼石", "樟柏石", "雪花石", "朱砂"),
            array("花岗岩", "石墨", "滑石", "煤", "间砂岩", "石灰岩", "粉砂岩"),
            array("赭石", "黄铁矿", "云母", "霞石", "蛇纹岩", "石英箨", "紫砂"),
            array("金刚砂", "孔雀石", "水滴石", "铁矿石", "白珪石", "光母石", "橄榄岩")
        );
        $data['lucky_color'] = $color[$lucky_day][date('w', $life_birth_time)];
        $data['lucky_flower'] = $flowers[$lucky_day][date('w', $life_birth_time)];
        $data['lucky_stone'] = $stones[$lucky_day][date('w', $life_birth_time)];
        return $data;
    }




    /**
     * 商城商品列表单条编辑
     */
    public function getCorpusInfo($corpusConstellationWhere)
    {

        $corpusConstellationModify='';

        $corpusConstellationInfo = $this->logicCorpusConstellation->getCorpusConstellationInfo($corpusConstellationWhere, 'oneself,other,keywords,content');

        if(!empty($corpusConstellationInfo)){
            $corpusConstellationModify = $corpusConstellationWhere;

            $corpusConstellationModify['title'] = $corpusConstellationInfo['oneself'] . $corpusConstellationInfo['other'];

            $corpusConstellationModify['content'] = $corpusConstellationInfo['content'];
        }


//        if(!empty($planet_desc)){
//            $corpusConstellationDescribeInfo = $this->logicCorpusConstellation->getCorpusConstellationInfo(['chartType' => $chartType, 'type' => 1, 'oneself' => $planetName[$planet_desc]], 'oneself,other,keywords,content');
//            $corpusConstellationModify['describe'] = $corpusConstellationDescribeInfo['content'];
//        }
        return $corpusConstellationModify;
    }



}
