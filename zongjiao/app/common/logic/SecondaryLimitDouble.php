<?php
// +---------------------------------------------------------------------+
// | ThinkTree    | [ Only you can't imagine,nothing you can't achieve]  |
// +---------------------------------------------------------------------+
// | Author     | feixiaoguai <<EMAIL>>                         |
// +---------------------------------------------------------------------+
// | Repository | http://blog.feixiaoguai.com                            |
// +---------------------------------------------------------------------+

namespace app\common\logic;

use astrology\SweTest as SweTest;

/**
 * 次限盘盘数据生成
 */
class SecondaryLimitDouble extends LogicBase
{

    /**
     * 获取站点导航搜索条件
     */
    public function plateData($param)
    {
        $house = $param['longitude'] . ',' . $param['latitude'] . ',' . $param['h_sys'];

        if (!empty($param['phase'])) {
            foreach ($param['phase'] as $key => $value) {
                $allow_degree[$key] = $value;
            }
        }

        $planet_degree=array();
        !empty($param['planet_degree']) && $planet_degree=$param['planet_degree'];

        if (empty($allow_degree)) {
            $allow_degree['0'] = 5;
            $allow_degree['30'] = 5;
            $allow_degree['45'] = 5;
            $allow_degree['60'] = 5;
            $allow_degree['90'] = 5;
            $allow_degree['180'] = 5;
        }

        $ay=false;
        if(isset($param['ay'])){
            $ay=$param['ay'];
        }

        $starsCode = $param['planets'];

        $birthdayToTime = strtotime($param['birthday']) - $param['tz'] * 3600;

        $utdatenow = date('d.m.Y', $birthdayToTime);

        $utnow = date('H:i:s', $birthdayToTime);

        $planets = implode('', $starsCode);

        $arr = [
            'b' => $utdatenow,
            'p' => $planets,
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];

        $exSweTest = get_sington_object('exSweTest', SweTest::class);
        if (!empty($param['virtual']) and !empty($param['virtual'][0])) {
            $starsCode['virtual'] = $param['virtual'];
        }

        if (!empty($param['planet_xs']) and !empty($param['planet_xs'][0])) {
            $starsCode['planet_xs'] = $param['planet_xs'];
        }
        if (!empty($param['planet_xf']) and !empty($param['planet_xf'][0])) {
            $starsCode['planet_xf'] = $param['planet_xf'];
        }
        if (!empty($param['planet_hel']) and !empty($param['planet_hel'][0])) {
            $starsCode['planet_hel'] = $param['planet_hel'];
        }

        $ay=false;
        if(isset($param['ay'])){
            $ay=$param['ay'];
        }


        $life_sweTest = $exSweTest->calculate($arr, $starsCode,[],$ay);

        $life_sum = $life_sweTest['planet'][0]['longitude'];
        $life_mc = $life_sweTest['house'][9]['longitude'];

        $transitdateToTime = strtotime($param['transitday'])- $param['tz'] * 3600;

        $fff= $transitdateToTime-$birthdayToTime;

        $prog_timedd = $fff / (365.2422*86400);
        $prog_timedd=$birthdayToTime+$prog_timedd*86400;

        $utdatenow = date('d.m.Y', $prog_timedd);

        $utnow = date('H:i:s', $prog_timedd);

        $arr2 = [
            'b' => $utdatenow,
            'p' => $planets,
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];
        $data2 = $exSweTest->calculatePlanet($arr2, $starsCode);

        $data2_sum = explode(',', str_replace(' ', '', $data2[0]));

        $p_mc = $life_mc + $data2_sum[1] - $life_sum;

        if ($p_mc >= 0) {
            $p_mc = $p_mc - floor($p_mc / 360) * 360;
        } else {
            $p_mc = 360 + ($p_mc - ((1 + floor($p_mc / 360)) * 360));
        }


        $utdatenow = date('d.m.Y', $prog_timedd);

        $utnow = '0:30:0';
        $arr = [
            'b' => $utdatenow,
            'house' => $house,
            'n' => '24',
            's' => '60m',
            'ut' => $utnow,
            'f' => 'PTlsj',  //名字 经度 度数经度 速度 宫位 名字id pTlsj
            'g' => ',',
            'head',
            'roundsec'
        ];
        $Progressed_Time = $exSweTest->universalProgressed($arr, 'house10', $p_mc, 'h');

        $utdatenow = date('d.m.Y', $Progressed_Time);

        $utnow = date('H:i:s', $Progressed_Time);

        $arr3 = [
            'b' => $utdatenow,
            'p' => '0',
            'house' => $house,
            'ut' => $utnow,
            'f' => 'Plsj',  //名字 经度 度数经度 速度 宫位 名字id
            'g' => ',',
            'head',
            'roundsec'
        ];
        $data3 = $exSweTest->calculatePlanet($arr3, [0],false);

        $data4=array_merge($data2,$data3);

        $data  = $exSweTest->calculate($arr2, $starsCode, $data4,$ay);

        $one_data = $life_sweTest;
        $second_data = $data;
        $planets_data['house'] = $this->logicComparision->housePlanet($exSweTest, $data);

        $sign_attribute = $this->logicComparision->signPlanet($one_data);
        $planets_data['sign'] = $sign_attribute['sign'];
        $planet = $this->logicComparision->planetSecondPhase($exSweTest, $one_data, $second_data, $allow_degree,$planet_degree);

        $planets_data['planet'] = $planet['planet'];
        $planets_data['planet_second'] = $planet['planet_second'];

        if(!isset($param['format'])){
            $param['format']=1;
        }

        if(!empty($param['svg_type']) and $param['svg_type']==1){
            $planets_data['svg'] = $this->logicComparision->simpleSvg($planets_data,$one_data,$param['format']);
        }else if(!empty($param['svg_type']) and $param['svg_type']==-1){

        }else{
            $planets_data['svg'] = $this->logicComparision->seniorSvg($planets_data,$one_data);
        }
        $planets_data['attribute'] = $sign_attribute['attribute'];

        return $planets_data;



    }

}
