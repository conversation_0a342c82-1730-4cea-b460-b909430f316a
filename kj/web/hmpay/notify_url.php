<?php
/**
 * ---------------------通知异步回调接收页-------------------------------
 * 
 * 此页就是您之前传给的notify_url页的网址
 * 支付成功，平台会根据您之前传入的网址，回调此页URL，post回参数
 * 
 * --------------------------------------------------------------
 */
require_once("epay.config.php");

require_once("lib/epay_notify.class.php");
 
$config = require_once("../app/Common/Conf/db.php");

$link = @mysqli_connect($config['DB_HOST'], $config['DB_USER'], $config['DB_PWD'], $config['DB_NAME'], $config['DB_PORT']);

if ($link) {
	echo '数据库连接成功<br />';
}
	


    $platform_trade_no = $_POST["platform_trade_no"];
    $orderid = $_POST["orderid"];

    $price = $_POST["price"];
    $realprice = $_POST["realprice"];
    $orderuid = $_POST["orderuid"];
    $key = $_POST["key"];

    //校验传入的参数是否格式正确，略

    $token = "VYMPJ9jMJjmkVCkXEZXFfCJM9CNWtXJJ";
    
    $temps = md5($orderid . $orderuid . $platform_trade_no . $price . $realprice . $token);

    if ($temps != $key){
        return jsonError("key值不匹配");
    }else{
        $sql = "select * from caipiao_recharge where `trano`='".$orderid."'";
		$pay = mysqli_query($link, $sql);
		$pay = mysqli_fetch_assoc($pay);
		if (empty($pay)) {
			file_put_contents('../../data/runtime/log.txt', '找不到订单号', FILE_APPEND);
			echo '找不到订单号';
			file_put_contents("B.txt", "找不到订单号");
			exit;
		}
		
		if ($pay['amount'] != $price) {
			//file_put_contents('../../data/runtime/log.txt', '金额不正确', FILE_APPEND);
			echo '金额不正确';
			file_put_contents("B.txt", "金额不正确");
			exit;
		}
		
		if ($pay['state'] == 1) {
			echo 'ok';
			exit;
		}
		
		$sql = "update caipiao_recharge set state='1' where `trano`='".$orderid."'";
		//$sql2 = "update caipiao_recharge set threetrano='".$platform_trade_no."' where `trano`='".$orderid."'";
		$result = mysqli_query($link, $sql);
		//$result2 = mysqli_query($link, $sql2);
		if ($result) {
			
			//给用户加金额
			$sql = "select * from caipiao_member where id='".$pay['uid']."'";
			$user = mysqli_query($link, $sql);
			$user = mysqli_fetch_assoc($user);
			if (!$user) {
				//file_put_contents('../../data/runtime/log.txt', '找不到充值的用户信息', FILE_APPEND);
				echo 'fail';
				exit;
			}
			
			$sql = "update caipiao_member set balance='".$pay['newaccountmoney']."' where id='".$user['id']."'";
			
			mysqli_query($link, $sql);
			
			echo "ok";		//请不要修改或删除
			
			exit;
		} else  {
			//file_put_contents('../../data/runtime/log.txt', '修改pay的数据失败', FILE_APPEND);
			echo 'fail';
			exit;
		}
	} 
	



  

?>