<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <title>{:GetVar('webtitle')}</title>
	<meta name="keywords" content="{:GetVar('keywords')}" />
	<meta name="description" content="{:GetVar('description')}" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" >
	<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no, viewport-fit=cover">
    <link rel="icon" type="image/x-icon" href="/static/img/favicon.ico">
    <link rel="stylesheet" href="/static/css/material-icons.css">
	<link rel="stylesheet" href="/mobile/css/material-icons.css">
    <link rel="stylesheet" href="/static/css/DINAlternate-bold.css">
    <link rel="stylesheet" href="/static/css/styles.41928e9497559161f9b8.css">
	<script src="__JS__/jquery-3.1.1.min.js"></script> 
	<script type="text/javascript" src="/resources/js/artDialog.js"></script>
	<script type="text/javascript" src="/resources/js/way.min.js"></script>
	<script type="text/javascript" src="/resources/main/common.js"></script>
	<script src="__JS__/require.js" data-main="__JS__/main"></script>
<style>div.reminder[_ngcontent-bua-c1] {
font-size:16px;
color:#fff;
display:flex;
margin-bottom:7px;
}

div.reminder[_ngcontent-bua-c1] > div.icon[_ngcontent-bua-c1] {
width:20px;
height:20px;
background:url(warm_reminder.1ce0c724cf587167f1da.svg);
margin-right:10px;
}

div.content-container[_ngcontent-bua-c1] {
padding:15px;
}

input.username.verified[_ngcontent-bua-c1] {
background-image:url(username_verified.e76c16e5767d290ded2c.svg);
background-repeat:no-repeat;
background-origin:content-box;
background-position-x:right;
}

button.action-btn[_ngcontent-bua-c1] {
box-sizing:border-box;
width:100%;
font-size:18px;
font-weight:400;
color:#fff;
border-radius:10px;
background-image:linear-gradient(179deg,rgba(19,162,186,1),rgba(8,124,149,1));
padding:15px;
}

button.action-btn.next[_ngcontent-bua-c1] {
margin-top:37px;
}

div.send-container[_ngcontent-bua-c1] {
color:#333;
font-size:24px;
font-weight:500;
line-height:33px;
text-align:center;
}

div.send-detail[_ngcontent-bua-c1] {
color:#111;
font-size:16px;
}

div.send-success-icon[_ngcontent-bua-c1] {
width:100%;
padding-bottom:calc(92/127*100%);
background-position:center top;
background-repeat:no-repeat;
background-size:100% 80%;
background-image:url(send-success-icon.899873304e76de0e84e5.svg);
margin:auto;
}

div.send-fail-icon[_ngcontent-bua-c1] {
width:100%;
padding-bottom:calc(92/127*100%);
background-position:center top;
background-repeat:no-repeat;
background-size:100% 80%;
background-image:url(send-fail-icon.8225c7f4b01ff892a836.svg);
margin:auto;
}

</style><style>.header-view__nav-row-wrapper[_ngcontent-bua-c2] {
position:fixed;
top:0;
left:0;
right:0;
z-index:999;
background-color:rgba(12,25,44,1);
}

.header-view__nav-row-wrapper--gradient[_ngcontent-bua-c2] {
background-color:transparent;
background-image:linear-gradient(tobottom,#0c192c,rgba(12,25,44,0));
}

.header-view__nav-row-wrapper--gradient-black[_ngcontent-bua-c2] {
background-color:transparent;
background-image:linear-gradient(tobottom,rgba(0,0,0,1),rgba(12,25,44,0));
}

</style><style>.header-view__footer-row-wrapper[_ngcontent-bua-c2] {
position:fixed;
bottom:0;
left:0;
right:0;
z-index:999;
background-color:rgba(19,34,53,1);
}

.header-view__footer-row-wrapper[_ngcontent-bua-c2]:empty {
display:none;
}

.header-view__footer-row-wrapper--gradient[_ngcontent-bua-c2] {
background-color:transparent;
background-image:linear-gradient(totop,#0c192c,rgba(19,34,53,0));
}

.header-view__footer-row-wrapper--gradient-black[_ngcontent-bua-c2] {
background-color:transparent;
background-image:linear-gradient(totop,rgba(0,0,0,1),rgba(19,34,53,0));
}

</style><style>.header-view__nav-row-wrapper__container__nav-row[_ngcontent-bua-c2] {
min-height:64px;
color:#fff;
font-size:30px;
font-weight:500;
line-height:34px;
display:flex;
flex-flow:row nowrap;
justify-content:space-between;
align-items:flex-start;
}

.header-view__nav-row-wrapper__container__nav-row__prefix[_ngcontent-bua-c2] {
flex:0 0 auto;
box-sizing:border-box;
width:44px;
height:64px;
color:#fff;
border-radius:0;
display:flex;
flex-flow:column nowrap;
justify-content:center;
align-items:center;
padding:0;
}

.header-view__nav-row-wrapper__container__nav-row__prefix__icon[_ngcontent-bua-c2] {
width:1em;
height:1em;
font-size:44px;
}

.header-view__nav-row-wrapper__container__nav-row__title[_ngcontent-bua-c2] {
flex:0 1 auto;
padding:15px 0;
}

.header-view__nav-row-wrapper__container__nav-row__title[_ngcontent-bua-c2]:first-child {
padding-left:15px;
}

.header-view__nav-row-wrapper__container__nav-row__content[_ngcontent-bua-c2] {
flex:1 1 auto;
min-height:inherit;
display:flex;
flex-flow:row nowrap;
justify-content:space-between;
align-items:stretch;
}

.header-view__nav-row-wrapper__container__nav-row__suffix[_ngcontent-bua-c2],.header-view__nav-row-wrapper__subordinate-container__nav-row__suffix[_ngcontent-bua-c2] {
flex:0 0 auto;
font-size:34px;
border-radius:50%;
display:flex;
flex-flow:column nowrap;
justify-content:center;
align-items:center;
margin:15px;
padding:0;
}

.header-view__nav-row-wrapper__subordinate-container__nav-row__suffix[_ngcontent-bua-c2] {
display:flex;
flex-direction:row;
}

.header-view__nav-row-wrapper__subordinate-container__nav-row__suffix[_ngcontent-bua-c2] jx-avatar[_ngcontent-bua-c2] {
width:20px;
}

.header-view__nav-row-wrapper__subordinate-container__nav-row__suffix[_ngcontent-bua-c2] span[_ngcontent-bua-c2] {
font-size:14px;
margin:0 0 0 10px;
}

.header-view__content-wrapper--full-screen[_ngcontent-bua-c2] {
position:absolute;
top:0;
bottom:0;
left:0;
right:0;
width:100%;
height:100%;
}

.header-view__content-wrapper__content-container--full-screen[_ngcontent-bua-c2] {
position:relative;
width:100%;
height:100%;
}

div.message-centre[_ngcontent-bua-c2] {
width:34px;
height:34px;
background-image:url(message_icon.141b2826df1d2bf607c3.svg);
background-repeat:no-repeat;
background-position:center;
}

div.message-centre.unread[_ngcontent-bua-c2] {
background-image:url(message_unread_icon.eaa5a9c778c1e6c3f136.svg);
}

button.create-agent-link-btn[_ngcontent-bua-c2] > div[_ngcontent-bua-c2] {
width:28px;
height:28px;
background-image:url(create-agentlink.1b7d4b7a8cd476e6b31b.svg);
background-repeat:no-repeat;
background-size:contain;
}

</style><style>@supports (padding-top:constant(safe-area-inset-top)) or (padding-top:env(safe-area-inset-top)) {
safe-area-toppadding-top:env(safe-area-inset-top);
}

.safe-area-fix-top {
margin-top:calc(-1*env(safe-area-inset-top));
padding-top:env(safe-area-inset-top);
}

.safe-area-fix-bottom {
margin-bottom:calc(-1*env(safe-area-inset-bottom));
padding-bottom:env(safe-area-inset-bottom);
}

.safe-area-fix-left {
margin-left:calc(-1*env(safe-area-inset-left));
padding-left:env(safe-area-inset-left);
}

.safe-area-fix-right {
margin-right:calc(-1*env(safe-area-inset-right));
padding-right:env(safe-area-inset-right);
}

</style><style>[_nghost-bua-c4] {
box-sizing:border-box;
display:block;
padding:15px 0;
}

div.form-field-header[_ngcontent-bua-c4] {
box-sizing:border-box;
margin-bottom:15px;
color:inherit;
font-size:18px;
font-weight:500;
line-height:25px;
display:flex;
flex-flow:row nowrap;
justify-content:flex-start;
align-items:center;
}

div.form-field-footer[_ngcontent-bua-c4]:not(:empty) {
margin-top:15px;
}

div.form-field-content[_ngcontent-bua-c4] {
box-sizing:border-box;
position:relative;
width:100%;
min-height:calc(30px+22px);
border-radius:10px;
}

</style><style>[_nghost-bua-c5] {
-webkit-appearance:none;
-moz-appearance:none;
appearance:none;
background:#132235;
border:1px solid transparent;
outline:0;
display:block;
box-sizing:border-box;
width:100%;
font-size:16px;
font-weight:500;
line-height:22px;
color:#fff;
border-radius:inherit;
padding:14px;
}

[_nghost-bua-c5]:disabled {
-webkit-filter:opacity(50%);
filter:opacity(50%);
}

[_nghost-bua-c5]:-webkit-autofill {
border-radius:inherit;
}

.ng-dirty.ng-invalid[_nghost-bua-c5],.ng-touched.ng-invalid[_nghost-bua-c5] {
border-color:#b72639;
}

</style><style>.mat-icon {
background-repeat:no-repeat;
display:inline-block;
fill:currentColor;
height:24px;
width:24px;
}

.mat-icon.mat-icon-inline {
font-size:inherit;
height:inherit;
line-height:inherit;
width:inherit;
}

[dir=rtl] .mat-icon-rtl-mirror {
transform:scale(-1,1);
}

.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon {
display:block;
}

.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon {
margin:auto;
}

.header-view__content-wrapper[_ngcontent-bua-c2],.header-view__content-wrapper__content-container[_ngcontent-bua-c2],div.form-field-footer[_ngcontent-bua-c4] {
box-sizing:border-box;
}

[_nghost-bua-c5]::-webkit-input-placeholder,[_nghost-bua-c5]::-moz-placeholder,[_nghost-bua-c5]::-ms-input-placeholder,[_nghost-bua-c5]::placeholder {
color:#8e8e93;
}</style>
</head>
<body style="color: white; background-color: #0c192c;">
        <div _ngcontent-bua-c2="" class="header-view__nav-row-wrapper safe-area-top safe-area-left safe-area-right" jxsafearealeft="" jxsafearearight="" jxsafeareatop="">
          <jx-header-row _ngcontent-bua-c2="" class="header-view__nav-row-wrapper__container" _nghost-bua-c8="">
            <div _ngcontent-bua-c2="" class="header-view__nav-row-wrapper__container__nav-row">
              <!---->
              <button _ngcontent-bua-c2="" class="header-view__nav-row-wrapper__container__nav-row__prefix" onclick="location.href='javascript:history.back(-1)'">
                  <i class="icon iconfont">&#xe67c;</i></button>
              <!---->
              <!---->
              <div _ngcontent-bua-c2="" class="header-view__nav-row-wrapper__container__nav-row__title">验证资金密码</div>
              <div _ngcontent-bua-c2="" class="header-view__nav-row-wrapper__container__nav-row__content"></div>
              <!---->
              <!---->
              <!---->
              <!----></div>
        </div>
        <div _ngcontent-bua-c2="" class="header-view__content-wrapper" style="padding-top: 64px;">
          <div _ngcontent-bua-c2="" class="header-view__content-wrapper__content-container">
              <div _ngcontent-bua-c1="" class="content-container">
			  <form class="am-form" action="" method="post" id="form1">
			  <input type="hidden" name="settype" value="1">
                  <!---->
                  <!---->
                  <div _ngcontent-bua-c4="" class="form-field-header">原资金密码:</div>
                  <div _ngcontent-bua-c4="" class="form-field-content">
                    <!---->
                    <!---->
                    <input _ngcontent-bua-c1="" autocapitalize="off" autocorrect="off" onkeyup="checkContent(this)" class="username ng-untouched ng-pristine ng-invalid" jxforminput="" class="input_txt" type="password" name="oldpassword" id="oldpassword" placeholder="请输入当前所使用的密码" spellcheck="false" _nghost-bua-c5="">
                    <!----></div>
                  <div _ngcontent-bua-c4="" class="form-field-footer">
                    <!----></div>
				  <p style="text-align: right;padding-right: 0.25rem;padding-top: 0.1rem;"><a style="color: #0d556a;text-decoration:none;" href="{:U('find_safepass')}">找回资金密码?</a></p>
				<div _ngcontent-bua-c1="" class="reminder">*资金密码用于提现、绑定银行卡等操作，可保障资金安全。</div>
                <button disabled="disabled" _ngcontent-bua-c1="" type="submit" id="sub" class="action-btn next">提交</button>
				</form>	
				</div>
          </div>
        </div>
<script>
function checkContent(obj) {
	 if($(obj).val() === '' ||$(obj).val() === null){
		   if($("#oldpassword").val() !== ''){
			   $('#sub').attr('disabled',false);
		   }else{
			   $('#sub').attr('disabled',true);
		   }
	   }else{
		   $('#sub').attr('disabled',false);
	   }
}
</script>
</body>
</html>