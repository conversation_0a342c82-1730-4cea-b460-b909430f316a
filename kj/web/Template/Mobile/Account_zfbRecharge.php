<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8">
    <title>{:GetVar('webtitle')}</title>
	<meta name="keywords" content="{:GetVar('keywords')}" />
	<meta name="description" content="{:GetVar('description')}" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" >
	<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1, user-scalable=no, viewport-fit=cover">
    <link rel="icon" type="image/x-icon" href="/static/img/favicon.ico">
    <link rel="stylesheet" href="/static/css/material-icons.css">
	<link rel="stylesheet" href="/mobile/css/material-icons.css">
    <link rel="stylesheet" href="/static/css/DINAlternate-bold.css">
    <link rel="stylesheet" href="/static/css/styles.41928e9497559161f9b8.css">
	<link rel="stylesheet" href="/resources/css/artDialog.css">
	<script src="__JS__/jquery-3.1.1.min.js"></script> 
	<script type="text/javascript" src="/resources/js/artDialog.js"></script>
	<script type="text/javascript" src="/resources/js/way.min.js"></script>
	<script type="text/javascript" src="/resources/main/common.js"></script>
	<script src="__JS__/require.js" data-main="__JS__/main"></script>
  <style>span.deposit-icon[_ngcontent-umf-c2] {display:block;box-sizing:border-box;margin:auto;width:25px;height:25px;background-repeat:no-repeat;background-position:center center;background-size:contain}
span.deposit-icon.bank-id-0[_ngcontent-umf-c2] {background-image:url(/mobile/img/0-quick.fb6cfb510ff805fb0b00.svg)}
span.deposit-icon.bank-id-1[_ngcontent-umf-c2] {background-image:url(/mobile/img/1-jingdong.1118f9b6f23466438c8c.svg)}
span.deposit-icon.bank-id-2[_ngcontent-umf-c2],span.deposit-icon.bank-id-99[_ngcontent-umf-c2] {background-image:url(/mobile/img/2-alipay.0691d59869edc30815f0.svg)}
span.deposit-icon.bank-id-100[_ngcontent-umf-c2] {background-image:url(/mobile/img/2-alipay-tran-card.081a4410cd17b2b68f66.svg)}
span.deposit-icon.bank-id-101[_ngcontent-umf-c2] {background-image:url(/mobile/img/2-alipay-scan.cef81dffdf7485b460ff.svg)}
span.deposit-icon.bank-id-102[_ngcontent-umf-c2] {background-image:url(/mobile/img/2-alipay-fix-amount.e356d74363b8e46f07de.svg)}
span.deposit-icon.bank-id-3[_ngcontent-umf-c2] {background-image:url(/mobile/img/3-wechat.81f380261513089f232f.svg)}
span.deposit-icon.bank-id-5[_ngcontent-umf-c2] {background-image:url(/mobile/img/5-qqpay.9d71f7e7f47da144bbf1.svg)}
span.deposit-icon.bank-id-6[_ngcontent-umf-c2] {background-image:url(/mobile/img/6-bank.11dfe8dc385d8937debb.svg)}
span.deposit-icon.bank-id-8[_ngcontent-umf-c2] {background-image:url(/mobile/img/8-online-atm.74d42b6acf0065517c5b.svg)}
</style><style>span.deposit-line-icon[_ngcontent-umf-c2] {display:block;box-sizing:border-box;margin-right:10px;width:20px;height:20px;background-repeat:no-repeat;background-position:center center;background-size:contain}
span.deposit-line-icon.bank-id-2-direct[_ngcontent-umf-c2] {background-image:url(/mobile/img/2-alipay-direct.caab3332d6b1cec4b14b.svg)}
span.deposit-line-icon.bank-id-2-card[_ngcontent-umf-c2] {background-image:url(/mobile/img/card.bd11a79ad99c348e6127.svg)}
span.deposit-line-icon.bank-id-2-scan[_ngcontent-umf-c2] {background-image:url(/mobile/img/alipay_card_scan.e8f1360bbd0dfbee6718.svg)}
span.deposit-line-icon.bank-id-2-fix-amount[_ngcontent-umf-c2] {background-image:url(/mobile/img/fix_amount.5412658266d2515b21d5.svg)}
span.deposit-line-icon.bank-id-3-direct[_ngcontent-umf-c2] {background-image:url(/mobile/img/3-wechat-direct.b0318dc31bb62c116c8b.svg)}
span.deposit-line-icon.bank-id-3-card[_ngcontent-umf-c2] {background-image:url(/mobile/img/card.bd11a79ad99c348e6127.svg)}
span.deposit-line-icon.bank-id-3-fix-amount[_ngcontent-umf-c2] {background-image:url(/mobile/img/fix_amount.5412658266d2515b21d5.svg)}
span.deposit-line-icon.bank-id-0-card[_ngcontent-umf-c2] {background-image:url(/mobile/img/0-quick.683f1d1eccdfdb93e178.png)}
span.deposit-line-icon.bank-id-0-direct[_ngcontent-umf-c2] {background-image:url(/mobile/img/0-cloud.9837f78a6e4e3a7037e3.png)}
</style><style>div.deposit-container[_ngcontent-umf-c2] {padding:15px}
div.deposit-method-header[_ngcontent-umf-c2] {font-size:18px;font-weight:500;line-height:25px;color:#fff}
div.deposit-method-container[_ngcontent-umf-c2] {position:relative;margin-top:15px;min-height:98px;border-radius:10px}
div.deposit-method-placeholder[_ngcontent-umf-c2] {min-height:inherit;border-radius:inherit;font-size:16px;font-weight:500;line-height:22px;color:#fff;background-color:#132235;display:flex;justify-content:center;align-items:center}
div.deposit-method-grid-container[_ngcontent-umf-c2] {min-height:inherit;border-radius:inherit;display:grid;grid-template-columns:repeat(auto-fit,minmax(165px,1fr));grid-template-rows:repeat(auto-fit,minmax(98px,1fr));grid-row-gap:10px;grid-column-gap:15px}
div.deposit-method-wrapper[_ngcontent-umf-c2] {box-sizing:border-box;min-width:165px;min-height:98px;position:relative;overflow:hidden;border-radius:inherit;color:#fff;background-color:#132235}
@supports not (display:grid) {div.deposit-method-grid-container[_ngcontent-umf-c2] {min-height:inherit;border-radius:inherit;display:flex;flex-flow:column nowrap;justify-content:flex-start;align-items:stretch}
div.deposit-method-wrapper[_ngcontent-umf-c2]:not(:first-child) {margin-top:10px}
}
button.deposit-method-primary-btn[_ngcontent-umf-c2] {display:block;box-sizing:border-box;padding:0;width:100%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0;border-radius:inherit;color:inherit;background-color:inherit}
div.deposit-method-secondary-btn-wrapper[_ngcontent-umf-c2] {box-sizing:border-box;width:100%;height:100%;position:absolute;top:0;bottom:0;left:0;right:0;border-radius:inherit;color:inherit;background-color:inherit;display:flex;flex-flow:column nowrap;justify-content:space-between;align-items:stretch}
button.deposit-method-secondary-btn[_ngcontent-umf-c2] {flex:1 1 auto;margin:0;padding:0;font-size:14px;font-weight:400;line-height:20px;border-radius:0;color:inherit;background-color:inherit;display:flex;flex-flow:row nowrap;justify-content:center;align-items:center}
button.deposit-method-secondary-btn[_ngcontent-umf-c2]:first-child {border-top-left-radius:inherit;border-top-right-radius:inherit}
button.deposit-method-secondary-btn[_ngcontent-umf-c2]:last-child {border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}
span.deposit-title[_ngcontent-umf-c2] {display:block;margin-top:6px;font-size:16px;font-weight:500;line-height:22px;color:#fff}
span.deposit-subtitle[_ngcontent-umf-c2] {display:block;margin-top:2px;font-size:12px;font-weight:400;line-height:17px;color:#8e8e93;white-space:nowrap}
span.deposit-subtitle[_ngcontent-umf-c2] > span[_ngcontent-umf-c2] {color:#d4a85e}
div.question-row-container[_ngcontent-umf-c2] {margin-top:15px;display:flex;flex-flow:row nowrap;justify-content:flex-start;align-items:center}
div.question-icon[_ngcontent-umf-c2] {flex:0 0 auto;margin-right:10px;width:20px;height:20px;background-repeat:no-repeat;background-position:center center;background-size:contain;background-image:url(/mobile/img/question.2c5ca90525c62410bf4f.svg)}
div.question-title[_ngcontent-umf-c2] {font-size:14px;font-weight:500;line-height:20px;color:#fff;text-align:start}
button.question-btn[_ngcontent-umf-c2] {flex:0 0 auto;margin-left:auto;padding:1px 6px;font-size:14px;font-weight:400;line-height:20px;color:#fff;background-image:linear-gradient(179deg,#13a2ba,#087c95);border-radius:2px}
div.hint-row-container[_ngcontent-umf-c2] {margin-top:15px}
div.hint-row-title-wrapper[_ngcontent-umf-c2] {display:flex;flex-flow:row nowrap;justify-content:flex-start;align-items:center}
div.hint-icon[_ngcontent-umf-c2] {flex:0 0 auto;margin-right:10px;width:20px;height:20px;background-repeat:no-repeat;background-position:center center;background-size:contain;background-image:url(/mobile/img/hint.ee056437b8db12aed65e.svg)}
div.hint-row-title[_ngcontent-umf-c2] {font-size:16px;font-weight:500;line-height:22px;color:#fff;text-align:start}
ul.hint-row-content[_ngcontent-umf-c2] {box-sizing:border-box;margin:5px 0 0;padding:0;font-size:14px;font-weight:400;line-height:20px;color:#404b5e;list-style-type:decimal;list-style-position:inside}
ul.hint-row-content[_ngcontent-umf-c2] > li[_ngcontent-umf-c2]:not(:first-child) {margin-top:5px}
</style><style>.header-view__nav-row-wrapper[_ngcontent-umf-c4] {position:fixed;top:0;left:0;right:0;z-index:999;background-color:rgba(12,25,44,1)}
@supports ((webkit-backdrop-filter:blur(20px)) or (backdrop-filter:blur(20px))) {.header-view__nav-row-wrapper[_ngcontent-umf-c4] {background-color:rgba(12,25,44,.8);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}
}
.header-view__nav-row-wrapper--gradient[_ngcontent-umf-c4] {background-color:transparent;background-image:linear-gradient(to bottom,#0c192c,rgba(12,25,44,0))}
.header-view__nav-row-wrapper--gradient-black[_ngcontent-umf-c4] {background-color:transparent;background-image:linear-gradient(to bottom,rgba(0,0,0,1),rgba(12,25,44,0))}
@supports ((-webkit-backdrop-filter:none) or (backdrop-filter:none)) {.header-view__nav-row-wrapper--gradient[_ngcontent-umf-c4],.header-view__nav-row-wrapper--gradient-black[_ngcontent-umf-c4] {-webkit-backdrop-filter:none;backdrop-filter:none}
}
</style><style>.header-view__footer-row-wrapper[_ngcontent-umf-c4] {position:fixed;bottom:0;left:0;right:0;z-index:999;background-color:rgba(19,34,53,1)}
@supports ((-webkit-backdrop-filter:blur(20px)) or (backdrop-filter:blur(20px))) {.header-view__footer-row-wrapper[_ngcontent-umf-c4] {background-color:rgba(19,34,53,.8);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}
}
.header-view__footer-row-wrapper[_ngcontent-umf-c4]:empty {display:none}
.header-view__footer-row-wrapper--gradient[_ngcontent-umf-c4] {background-color:transparent;background-image:linear-gradient(to top,#0c192c,rgba(19,34,53,0))}
.header-view__footer-row-wrapper--gradient-black[_ngcontent-umf-c4] {background-color:transparent;background-image:linear-gradient(to top,rgba(0,0,0,1),rgba(19,34,53,0))}
@supports ((-webkit-backdrop-filter:none) or (backdrop-filter:none)) {.header-view__footer-row-wrapper--gradient[_ngcontent-umf-c4],.header-view__footer-row-wrapper--gradient-black[_ngcontent-umf-c4] {-webkit-backdrop-filter:none;backdrop-filter:none}
}
</style><style>.header-view__nav-row-wrapper__container__nav-row[_ngcontent-umf-c4] {min-height:64px;color:#fff;font-size:30px;font-weight:500;line-height:34px;display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:flex-start}
.header-view__nav-row-wrapper__container__nav-row__prefix[_ngcontent-umf-c4] {flex:0 0 auto;box-sizing:border-box;padding:0;width:44px;height:64px;color:#fff;border-radius:0;display:flex;flex-flow:column nowrap;justify-content:center;align-items:center}
.header-view__nav-row-wrapper__container__nav-row__prefix__icon[_ngcontent-umf-c4] {width:1em;height:1em;font-size:44px}
.header-icons-ctn[_ngcontent-umf-c4] {height:34px;width:100%;margin:15px;display:flex;flex-flow:row nowrap}
.header-icon[_ngcontent-umf-c4] {background-repeat:no-repeat;background-size:cover;background-position:center}
.header-jx-icon[_ngcontent-umf-c4] {background-image:url(/mobile/img/jx-logo.443c8ffbee580d3672f7.png);height:100%;width:91px;margin-right:10px}
.header-vertical-line[_ngcontent-umf-c4] {border:1px solid #3f4f66}
.header-laliga-icon[_ngcontent-umf-c4] {background-image:url(/mobile/img/laliga-logo.01f82bb57660e2ece9d1.png);height:100%;width:128px;margin-left:10px}
.header-view__nav-row-wrapper__container__nav-row__title[_ngcontent-umf-c4] {flex:0 1 auto;padding:15px 0}
.header-view__nav-row-wrapper__container__nav-row__title[_ngcontent-umf-c4]:first-child {padding-left:15px}
.header-view__nav-row-wrapper__container__nav-row__content[_ngcontent-umf-c4] {flex:1 1 auto;min-height:inherit;display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:stretch}
.header-view__nav-row-wrapper__container__nav-row__suffix[_ngcontent-umf-c4],.header-view__nav-row-wrapper__subordinate-container__nav-row__suffix[_ngcontent-umf-c4] {flex:0 0 auto;margin:15px;padding:0;font-size:34px;border-radius:50%;display:flex;flex-flow:column nowrap;justify-content:center;align-items:center}
.header-view__nav-row-wrapper__subordinate-container__nav-row__suffix[_ngcontent-umf-c4] {display:flex;flex-direction:row}
.header-view__nav-row-wrapper__subordinate-container__nav-row__suffix[_ngcontent-umf-c4]   jx-avatar[_ngcontent-umf-c4] {width:20px}
.header-view__nav-row-wrapper__subordinate-container__nav-row__suffix[_ngcontent-umf-c4]   span[_ngcontent-umf-c4] {font-size:14px;margin:0 0 0 10px}
.header-view__content-wrapper[_ngcontent-umf-c4] {box-sizing:border-box}
.header-view__content-wrapper--full-screen[_ngcontent-umf-c4] {position:absolute;top:0;bottom:0;left:0;right:0;width:100%;height:100%}
.header-view__content-wrapper__content-container[_ngcontent-umf-c4] {box-sizing:border-box}
.header-view__content-wrapper__content-container--full-screen[_ngcontent-umf-c4] {position:relative;width:100%;height:100%}
.msg-centre-btn[_ngcontent-umf-c4] {margin:25px 15px 15px;border-radius:1px}
div.message-centre[_ngcontent-umf-c4] {width:24px;height:16px;background-image:url(/mobile/img/letter.a6d96d31aad6d4b972f8.png);background-repeat:no-repeat;background-position:center;background-size:cover}
div.message-centre.unread[_ngcontent-umf-c4] {background-image:url(/mobile/img/unread-letter.2071a72b2e3b5bc9b34c.png)}
button.create-agent-link-btn[_ngcontent-umf-c4] > div[_ngcontent-umf-c4] {width:28px;height:28px;background-image:url(/mobile/img/create-agentlink.1b7d4b7a8cd476e6b31b.svg);background-repeat:no-repeat;background-size:contain}
</style><style>@supports (padding-top:constant(safe-area-inset-top)) or (padding-top:env(safe-area-inset-top)) {.safe-area-top {padding-top:env(safe-area-inset-top)}
.safe-area-fix-top {margin-top:calc(-1 * env(safe-area-inset-top));padding-top:env(safe-area-inset-top)}
}
@supports (padding-bottom:constant(safe-area-inset-bottom)) or (padding-bottom:env(safe-area-inset-bottom)) {.safe-area-bottom {padding-bottom:env(safe-area-inset-bottom)}
.safe-area-fix-bottom {margin-bottom:calc(-1 * env(safe-area-inset-bottom));padding-bottom:env(safe-area-inset-bottom)}
}
@supports (padding-left:constant(safe-area-inset-left)) or (padding-left:env(safe-area-inset-left)) {.safe-area-left {padding-left:env(safe-area-inset-left)}
.safe-area-fix-left {margin-left:calc(-1 * env(safe-area-inset-left));padding-left:env(safe-area-inset-left)}
}
@supports (padding-right:constant(safe-area-inset-right)) or (padding-right:env(safe-area-inset-right)) {.safe-area-right {padding-right:env(safe-area-inset-right)}
.safe-area-fix-right {margin-right:calc(-1 * env(safe-area-inset-right));padding-right:env(safe-area-inset-right)}
}
</style>
<style>[_nghost-umf-c3] {box-sizing:border-box;position:absolute;top:0;left:0;bottom:0;right:0;width:100%;height:100%;border-radius:inherit;background-color:transparent;display:flex;flex-flow:column nowrap;justify-content:center;align-items:center}
.backdrop[_nghost-umf-c3] {background-color:rgba(0,0,0,.5)}
</style>
<style>.mat-progress-spinner {display:block;position:relative}
.mat-progress-spinner svg {position:absolute;transform:rotate(-90deg);top:0;left:0;transform-origin:center;overflow:visible}
.mat-progress-spinner circle {fill:transparent;transform-origin:center;transition:stroke-dashoffset 225ms linear}
._mat-animation-noopable.mat-progress-spinner circle {transition:none;animation:none}
.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] {animation:mat-progress-spinner-linear-rotate 2s linear infinite}
._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] {transition:none;animation:none}
.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle {transition-property:stroke;animation-duration:4s;animation-timing-function:cubic-bezier(.35,0,.25,1);animation-iteration-count:infinite}
._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-animation[mode=indeterminate] circle {transition:none;animation:none}
.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] {animation:mat-progress-spinner-stroke-rotate-fallback 10s cubic-bezier(.87,.03,.33,1) infinite}
._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] {transition:none;animation:none}
.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle {transition-property:stroke}
._mat-animation-noopable.mat-progress-spinner.mat-progress-spinner-indeterminate-fallback-animation[mode=indeterminate] circle {transition:none;animation:none}
@keyframes mat-progress-spinner-linear-rotate {0% {transform:rotate(0)}
100% {transform:rotate(360deg)}
}
@keyframes mat-progress-spinner-stroke-rotate-100 {0% {stroke-dashoffset:268.60617px;transform:rotate(0)}
12.5% {stroke-dashoffset:56.54867px;transform:rotate(0)}
12.5001% {stroke-dashoffset:56.54867px;transform:rotateX(180deg) rotate(72.5deg)}
25% {stroke-dashoffset:268.60617px;transform:rotateX(180deg) rotate(72.5deg)}
25.0001% {stroke-dashoffset:268.60617px;transform:rotate(270deg)}
37.5% {stroke-dashoffset:56.54867px;transform:rotate(270deg)}
37.5001% {stroke-dashoffset:56.54867px;transform:rotateX(180deg) rotate(161.5deg)}
50% {stroke-dashoffset:268.60617px;transform:rotateX(180deg) rotate(161.5deg)}
50.0001% {stroke-dashoffset:268.60617px;transform:rotate(180deg)}
62.5% {stroke-dashoffset:56.54867px;transform:rotate(180deg)}
62.5001% {stroke-dashoffset:56.54867px;transform:rotateX(180deg) rotate(251.5deg)}
75% {stroke-dashoffset:268.60617px;transform:rotateX(180deg) rotate(251.5deg)}
75.0001% {stroke-dashoffset:268.60617px;transform:rotate(90deg)}
87.5% {stroke-dashoffset:56.54867px;transform:rotate(90deg)}
87.5001% {stroke-dashoffset:56.54867px;transform:rotateX(180deg) rotate(341.5deg)}
100% {stroke-dashoffset:268.60617px;transform:rotateX(180deg) rotate(341.5deg)}
}
@keyframes mat-progress-spinner-stroke-rotate-fallback {0% {transform:rotate(0)}
25% {transform:rotate(1170deg)}
50% {transform:rotate(2340deg)}
75% {transform:rotate(3510deg)}
100% {transform:rotate(4680deg)}
}
</style><style mat-spinner-animation="36"> @keyframes mat-progress-spinner-stroke-rotate-36 {0% {stroke-dashoffset:77.59733854366789;transform:rotate(0);}
12.5% {stroke-dashoffset:16.336281798666928;transform:rotate(0);}
12.5001% {stroke-dashoffset:16.336281798666928;transform:rotateX(180deg) rotate(72.5deg);}
25% {stroke-dashoffset:77.59733854366789;transform:rotateX(180deg) rotate(72.5deg);}
25.0001% {stroke-dashoffset:77.59733854366789;transform:rotate(270deg);}
37.5% {stroke-dashoffset:16.336281798666928;transform:rotate(270deg);}
37.5001% {stroke-dashoffset:16.336281798666928;transform:rotateX(180deg) rotate(161.5deg);}
50% {stroke-dashoffset:77.59733854366789;transform:rotateX(180deg) rotate(161.5deg);}
50.0001% {stroke-dashoffset:77.59733854366789;transform:rotate(180deg);}
62.5% {stroke-dashoffset:16.336281798666928;transform:rotate(180deg);}
62.5001% {stroke-dashoffset:16.336281798666928;transform:rotateX(180deg) rotate(251.5deg);}
75% {stroke-dashoffset:77.59733854366789;transform:rotateX(180deg) rotate(251.5deg);}
75.0001% {stroke-dashoffset:77.59733854366789;transform:rotate(90deg);}
87.5% {stroke-dashoffset:16.336281798666928;transform:rotate(90deg);}
87.5001% {stroke-dashoffset:16.336281798666928;transform:rotateX(180deg) rotate(341.5deg);}
100% {stroke-dashoffset:77.59733854366789;transform:rotateX(180deg) rotate(341.5deg);}
}
</style><style>.mat-icon {background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px}
.mat-icon.mat-icon-inline {font-size:inherit;height:inherit;line-height:inherit;width:inherit}
[dir=rtl] .mat-icon-rtl-mirror {transform:scale(-1,1)}
.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon {display:block}
.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon {margin:auto}
</style>
<style>span.deposit-icon[_ngcontent-umf-c10] {display:block;box-sizing:border-box;margin:auto;width:25px;height:25px;background-repeat:no-repeat;background-position:center center;background-size:contain}
span.deposit-icon.bank-id-0[_ngcontent-umf-c10] {background-image:url(/mobile/img/0-quick.fb6cfb510ff805fb0b00.svg)}
span.deposit-icon.bank-id-1[_ngcontent-umf-c10] {background-image:url(/mobile/img/1-jingdong.1118f9b6f23466438c8c.svg)}
span.deposit-icon.bank-id-2[_ngcontent-umf-c10],span.deposit-icon.bank-id-99[_ngcontent-umf-c10] {background-image:url(/mobile/img/2-alipay.0691d59869edc30815f0.svg)}
span.deposit-icon.bank-id-100[_ngcontent-umf-c10] {background-image:url(/mobile/img/2-alipay-tran-card.081a4410cd17b2b68f66.svg)}
span.deposit-icon.bank-id-101[_ngcontent-umf-c10] {background-image:url(/mobile/img/2-alipay-scan.cef81dffdf7485b460ff.svg)}
span.deposit-icon.bank-id-102[_ngcontent-umf-c10] {background-image:url(/mobile/img/2-alipay-fix-amount.e356d74363b8e46f07de.svg)}
span.deposit-icon.bank-id-3[_ngcontent-umf-c10] {background-image:url(/mobile/img/3-wechat.81f380261513089f232f.svg)}
span.deposit-icon.bank-id-5[_ngcontent-umf-c10] {background-image:url(/mobile/img/5-qqpay.9d71f7e7f47da144bbf1.svg)}
span.deposit-icon.bank-id-6[_ngcontent-umf-c10] {background-image:url(/mobile/img/6-bank.11dfe8dc385d8937debb.svg)}
span.deposit-icon.bank-id-8[_ngcontent-umf-c10] {background-image:url(/mobile/img/8-online-atm.74d42b6acf0065517c5b.svg)}
</style><style>span.deposit-line-icon[_ngcontent-umf-c10] {display:block;box-sizing:border-box;margin-right:10px;width:20px;height:20px;background-repeat:no-repeat;background-position:center center;background-size:contain}
span.deposit-line-icon.bank-id-2-direct[_ngcontent-umf-c10] {background-image:url(/mobile/img/2-alipay-direct.caab3332d6b1cec4b14b.svg)}
span.deposit-line-icon.bank-id-2-card[_ngcontent-umf-c10] {background-image:url(/mobile/img/card.bd11a79ad99c348e6127.svg)}
span.deposit-line-icon.bank-id-2-scan[_ngcontent-umf-c10] {background-image:url(/mobile/img/alipay_card_scan.e8f1360bbd0dfbee6718.svg)}
span.deposit-line-icon.bank-id-2-fix-amount[_ngcontent-umf-c10] {background-image:url(/mobile/img/fix_amount.5412658266d2515b21d5.svg)}
span.deposit-line-icon.bank-id-3-direct[_ngcontent-umf-c10] {background-image:url(/mobile/img/3-wechat-direct.b0318dc31bb62c116c8b.svg)}
span.deposit-line-icon.bank-id-3-card[_ngcontent-umf-c10] {background-image:url(/mobile/img/card.bd11a79ad99c348e6127.svg)}
span.deposit-line-icon.bank-id-3-fix-amount[_ngcontent-umf-c10] {background-image:url(/mobile/img/fix_amount.5412658266d2515b21d5.svg)}
span.deposit-line-icon.bank-id-0-card[_ngcontent-umf-c10] {background-image:url(/mobile/img/0-quick.683f1d1eccdfdb93e178.png)}
span.deposit-line-icon.bank-id-0-direct[_ngcontent-umf-c10] {background-image:url(/mobile/img/0-cloud.9837f78a6e4e3a7037e3.png)}
</style><style>div.deposit-amount-container[_ngcontent-umf-c10] {padding:15px 15px calc(15px + 55px)}
jx-form-field-subheading[_ngcontent-umf-c10] {font-size:12px;font-weight:400;line-height:17px;color:#959596}
jx-form-field-subheading[_ngcontent-umf-c10] > span.deposit-balance-amount[_ngcontent-umf-c10] {font-size:20px;font-weight:700;line-height:17px;color:#d4a85e;vertical-align:baseline}
div.amount-shortcuts-container[_ngcontent-umf-c10] {margin-bottom:15px;display:grid;grid-template-columns:repeat(auto-fit,minmax(80px,1fr));grid-template-rows:repeat(auto-fit,minmax(34px,1fr));grid-column-gap:8px;grid-row-gap:10px}
button.amount-shortcut-btn[_ngcontent-umf-c10] {padding:.5em;min-width:80px;min-height:34px;font-size:14px;font-weight:400;line-height:20px;color:#fff;background-color:#132235;border-radius:10px}
button.amount-shortcut-btn.active[_ngcontent-umf-c10] {background-image:linear-gradient(179deg,#13a2ba,#087c95)}
button.recharge-route-btn[_ngcontent-umf-c10] {display:flex;margin-top:10px;color:#8e8e93}
button.recharge-route-btn.selected[_ngcontent-umf-c10] {color:#fff}
button.recharge-route-btn[_ngcontent-umf-c10]   div.icon[_ngcontent-umf-c10] {width:22px;height:22px;margin-right:15px;background-repeat:no-repeat;background-size:contain;background-image:url(/mobile/img/recharegline_default.8d12e9e5151c5f767bc3.svg)}
button.recharge-route-btn.selected[_ngcontent-umf-c10]   div.icon[_ngcontent-umf-c10] {background-image:url(/mobile/img/recharegline_selected.9ec078756c1d99832bd1.svg)}
div.footer-fixed-container[_ngcontent-umf-c10] {position:fixed;bottom:0;left:0;right:0;height:calc(55px + 15px);background-image:linear-gradient(to bottom,rgba(12,25,44,0),rgba(12,25,44,1))}
div.footer-wrapper[_ngcontent-umf-c10] {padding:0 15px 15px}
button.back-btn[_ngcontent-umf-c10] {width:100%;height:55px;font-size:18px;font-weight:400;border-radius:10px;color:#fff;background-image:linear-gradient(179deg,#13a2ba,#087c95)}
</style><style>[_nghost-umf-c11] {flex:1 1 auto;box-sizing:border-box;position:relative;padding:15px 0;display:flex;flex-flow:row wrap;justify-content:flex-start;align-items:center}
</style><style>[_nghost-umf-c12] {box-sizing:border-box;display:block;padding:15px 0}
div.form-field-header[_ngcontent-umf-c12] {box-sizing:border-box;margin-bottom:15px;color:inherit;font-size:18px;font-weight:500;line-height:25px;display:flex;flex-flow:row nowrap;justify-content:flex-start;align-items:center}
div.form-field-footer[_ngcontent-umf-c12] {box-sizing:border-box}
div.form-field-footer[_ngcontent-umf-c12]:not(:empty) {margin-top:15px}
div.form-field-content[_ngcontent-umf-c12] {box-sizing:border-box;position:relative;width:100%;min-height:calc(30px + 22px);border-radius:10px}
</style><style>[_nghost-umf-c14] {webkit-appearance:none;-moz-appearance:none;appearance:none;background:#132235;border:1px solid transparent;outline:0;display:block;box-sizing:border-box;padding:14px;width:100%;font-size:16px;font-weight:500;line-height:22px;color:#fff;border-radius:inherit}
[_nghost-umf-c14]::-webkit-input-placeholder {color:#8e8e93}
[_nghost-umf-c14]::-moz-placeholder {color:#8e8e93}
[_nghost-umf-c14]::-ms-input-placeholder {color:#8e8e93}
[_nghost-umf-c14]::placeholder {color:#8e8e93}
[_nghost-umf-c14]:disabled {webkit-filter:opacity(50%);filter:opacity(50%)}
[_nghost-umf-c14]:-webkit-autofill {border-radius:inherit}
.ng-dirty.ng-invalid[_nghost-umf-c14],.ng-touched.ng-invalid[_nghost-umf-c14] {border-color:#b72639}
</style><style>[_nghost-umf-c16] {display:block;box-sizing:border-box;color:#404b5e;font-size:14px;font-weight:400;line-height:20px}
.show-asterisk[_nghost-umf-c16]::before {content:"*";color:#fcce3f;padding-right:.5em}
</style><style>[_nghost-umf-c13] {display:block;box-sizing:border-box;position:relative;margin-left:auto}
</style><style>div.tutorial-container[_ngcontent-umf-c18] {padding:15px 15px calc(54px + 15px + 15px)}
div.ali-pay-tutorial[_ngcontent-umf-c18] {padding-bottom:calc(4440 / 1500 * 100%);width:100%;background-repeat:no-repeat;background-size:contain;background-position:center center;background-image:url(/mobile/img/ali-pay-tutorial.2723050b4b1641b3573f.png)}
div.we-chat-pay-tutorial[_ngcontent-umf-c18] {padding-bottom:calc(5160 / 1005 * 100%);width:100%;background-repeat:no-repeat;background-size:contain;background-position:center center;background-image:url(/mobile/img/we-chat-pay-tutorial.718985364655072b9e81.png)}
ul[_ngcontent-umf-c18] {margin:0;padding:0;list-style-type:decimal;list-style-position:inside}
li[_ngcontent-umf-c18]:not(:first-child) {margin-top:50px}
li[_ngcontent-umf-c18] {font-size:18px;font-weight:500;line-height:25px;color:#13a2ba}
li[_ngcontent-umf-c18] > div[_ngcontent-umf-c18]:not(.tutorial-image) {font-size:16px;font-weight:400;line-height:22px;color:#fff}
li[_ngcontent-umf-c18] > div.tutorial-image[_ngcontent-umf-c18] {margin-top:10px;width:100%;padding-bottom:calc(660 / 864 * 100%);border-radius:10px;background-repeat:no-repeat;background-position:center center;background-size:contain}
li[_ngcontent-umf-c18] > div.tutorial-image.tutorial-image-1[_ngcontent-umf-c18] {background-image:url(/mobile/img/tutorial-1.0dc5ad73e3a81e2f0b65.jpg)}
li[_ngcontent-umf-c18] > div.tutorial-image.tutorial-image-2[_ngcontent-umf-c18] {background-image:url(/mobile/img/tutorial-2.cab959a1f968562ba55d.jpg)}
li[_ngcontent-umf-c18] > div.tutorial-image.tutorial-image-3[_ngcontent-umf-c18] {background-image:url(/mobile/img/tutorial-3.5c38434d0945c1eef841.jpg)}
li[_ngcontent-umf-c18] > div.tutorial-image.tutorial-image-4[_ngcontent-umf-c18] {background-image:url(/mobile/img/tutorial-4.6598f13e2cb916fa90e3.jpg)}
li[_ngcontent-umf-c18] > div[_ngcontent-umf-c18]:first-child {margin-top:10px}
div.footer-fixed-container[_ngcontent-umf-c18] {position:fixed;bottom:0;left:0;right:0;background-image:linear-gradient(to bottom,rgba(12,25,44,0),rgba(12,25,44,1))}
div.footer-wrapper[_ngcontent-umf-c18] {padding:0 15px 15px}
button.back-btn[_ngcontent-umf-c18] {width:100%;height:54px;font-size:18px;font-weight:400;border-radius:10px;color:#fff;background-image:linear-gradient(179deg,#13a2ba,#087c95)}
</style><style>[_nghost-umf-c15] {display:block;box-sizing:border-box;color:#c7283b;font-size:14px;font-weight:400;line-height:20px}
.show-icon[_nghost-umf-c15]::before {content:"";display:inline-block;box-sizing:content-box;width:10px;height:10px;padding-right:5px;vertical-align:middle;background-image:url(/mobile/img/form-error-icon.ec79cff1d3c06a25d7af.svg);background-repeat:no-repeat;background-position:left center;background-size:contain}
</style><style>[_nghost-umf-c17] {webkit-appearance:none;-moz-appearance:none;appearance:none;background:#132235;border:1px solid transparent;outline:0;display:block;box-sizing:border-box;padding:14px;width:100%;font-size:16px;font-weight:500;line-height:22px;color:#fff;border-radius:inherit}
[_nghost-umf-c17]::-webkit-input-placeholder {color:#8e8e93}
[_nghost-umf-c17]::-moz-placeholder {color:#8e8e93}
[_nghost-umf-c17]::-ms-input-placeholder {color:#8e8e93}
[_nghost-umf-c17]::placeholder {color:#8e8e93}
[_nghost-umf-c17]:disabled {webkit-filter:opacity(50%);filter:opacity(50%)}
[_nghost-umf-c17]:-webkit-autofill {border-radius:inherit}
.ng-dirty.ng-invalid[_nghost-umf-c17],.ng-touched.ng-invalid[_nghost-umf-c17] {border-color:#b72639}
</style><style mat-spinner-animation="18"> @keyframes mat-progress-spinner-stroke-rotate-18 {0% {stroke-dashoffset:23.876104167282428;transform:rotate(0);}
12.5% {stroke-dashoffset:5.026548245743669;transform:rotate(0);}
12.5001% {stroke-dashoffset:5.026548245743669;transform:rotateX(180deg) rotate(72.5deg);}
25% {stroke-dashoffset:23.876104167282428;transform:rotateX(180deg) rotate(72.5deg);}
25.0001% {stroke-dashoffset:23.876104167282428;transform:rotate(270deg);}
37.5% {stroke-dashoffset:5.026548245743669;transform:rotate(270deg);}
37.5001% {stroke-dashoffset:5.026548245743669;transform:rotateX(180deg) rotate(161.5deg);}
50% {stroke-dashoffset:23.876104167282428;transform:rotateX(180deg) rotate(161.5deg);}
50.0001% {stroke-dashoffset:23.876104167282428;transform:rotate(180deg);}
62.5% {stroke-dashoffset:5.026548245743669;transform:rotate(180deg);}
62.5001% {stroke-dashoffset:5.026548245743669;transform:rotateX(180deg) rotate(251.5deg);}
75% {stroke-dashoffset:23.876104167282428;transform:rotateX(180deg) rotate(251.5deg);}
75.0001% {stroke-dashoffset:23.876104167282428;transform:rotate(90deg);}
87.5% {stroke-dashoffset:5.026548245743669;transform:rotate(90deg);}
87.5001% {stroke-dashoffset:5.026548245743669;transform:rotateX(180deg) rotate(341.5deg);}
100% {stroke-dashoffset:23.876104167282428;transform:rotateX(180deg) rotate(341.5deg);}
}
</style><style>.mat-dialog-container {display:block;padding:24px;border-radius:4px;box-sizing:border-box;overflow:auto;outline:0;width:100%;height:100%;min-height:inherit;max-height:inherit}
@media (-ms-high-contrast:active) {.mat-dialog-container {outline:solid 1px}
}
.mat-dialog-content {display:block;margin:0 -24px;padding:0 24px;max-height:65vh;overflow:auto;-webkit-overflow-scrolling:touch}
.mat-dialog-title {margin:0 0 20px;display:block}
.mat-dialog-actions {padding:8px 0;display:flex;flex-wrap:wrap;min-height:52px;align-items:center;margin-bottom:-24px}
.mat-dialog-actions[align=end] {justify-content:flex-end}
.mat-dialog-actions[align=center] {justify-content:center}
.mat-dialog-actions .mat-button-base+.mat-button-base {margin-left:8px}
[dir=rtl] .mat-dialog-actions .mat-button-base+.mat-button-base {margin-left:0;margin-right:8px}
</style><style>div.dialog-wrapper[_ngcontent-umf-c20] {box-sizing:border-box;padding:16px 16px 10px}
div.content-wrapper[_ngcontent-umf-c20] {display:flex;flex-flow:column nowrap;justify-content:flex-start;align-items:center}
div.content-plain-text[_ngcontent-umf-c20] {color:#333;font-size:20px;font-weight:500;line-height:28px;text-align:center;word-break:break-word;white-space:pre-line}
mat-dialog-content[_ngcontent-umf-c20] {margin:0 -40px;padding:0 40px;max-height:45vh}
mat-dialog-actions[_ngcontent-umf-c20] {margin:20px 0 0;padding:0;min-height:44px;display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:stretch}
button[_ngcontent-umf-c20] {flex:1 1 100%;padding:10px;border-radius:10px;font-size:18px;font-weight:500;line-height:25px;color:#fff;text-align:center}
button[_ngcontent-umf-c20]:not(:first-child) {margin-left:10px}
button[_ngcontent-umf-c20]:active {webkit-filter:brightness(80%);filter:brightness(80%)}
button.ok-btn[_ngcontent-umf-c20] {background:linear-gradient(to bottom,#13a2ba,#087c95)}
button.cancel-btn[_ngcontent-umf-c20] {background:linear-gradient(to bottom,#9ea3aa,#59585d)}
</style><style>mat-dialog-content.open-url-content[_ngcontent-umf-c21] {margin:-14px;padding:0}
mat-dialog-content.open-url-content[_ngcontent-umf-c21] > a[_ngcontent-umf-c21] {display:none}
button.open-url-btn[_ngcontent-umf-c21] {padding:10px;width:100%;min-height:20px;font-size:14px;font-weight:400;line-height:20px;color:#0c192c;background-color:#fff;border-radius:10px}
span.open-url-btn-icon[_ngcontent-umf-c21] {box-sizing:border-box;display:inline-block;margin-right:15px;width:20px;height:20px;vertical-align:middle;background-image:url(/mobile/img/new-window.bccbf35675ae5815dd71.svg)}
</style><style>button.jx-app-download-button[_ngcontent-umf-c22] {width:100%;height:44px;background-color:#000000aa}
div.jx-app-download-container[_ngcontent-umf-c22] {width:100%;display:flex;align-items:center}
div.jx-app-download-container[_ngcontent-umf-c22] > img[_ngcontent-umf-c22] {width:32px;height:auto;margin:auto 10px auto 15px}
div.jx-app-download-container[_ngcontent-umf-c22] > span[_ngcontent-umf-c22] {color:#fff;font-size:16px;flex-grow:1;text-align:left}
div.jx-app-download-dismiss[_ngcontent-umf-c22] {width:44px;height:44px;background-image:url(/mobile/img/close.07a630d9b4dd917a2c57.svg);background-repeat:no-repeat;background-position:center;background-size:20px 20px}
div.dialog-header-row[_ngcontent-umf-c22] {font-size:24px;font-weight:700;margin-bottom:.5em}
.center[_ngcontent-umf-c22] {text-align:center}
.jx-brand-url-btn[_ngcontent-umf-c22] {display:block;margin-left:auto;padding:0;width:23px;height:23px;border-radius:0;background-size:contain;background-repeat:no-repeat;background-position:center center;background-image:url(/mobile/img/jx_brand_icon.573366da01d92eacea3d.svg)}
</style><style>div.app-background[_ngcontent-umf-c23] {position:fixed;top:0;left:0;width:100%;height:100%;z-index:-999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;touch-action:none;pointer-events:none;background-image:url(/mobile/img/pi-xiu.a23ab2aff01478fd81ad.svg);background-position:center 20%;background-repeat:no-repeat;background-size:contain}
</style><style>[_nghost-umf-c24] {position:relative;display:block;padding-bottom:calc(480 / 1125 * 100%)}
@media screen and (min-aspect-ratio:1125/960) {[_nghost-umf-c24] {padding-bottom:50vh}
}
div.banner-board-wrapper[_ngcontent-umf-c24] {position:absolute;top:0;left:0;width:100%;height:100%}
div.banner-board-container[_ngcontent-umf-c24] {width:100%;height:100%;overflow:hidden}
div.banner-item-container[_ngcontent-umf-c24] {width:100%;height:100%;display:flex;flex-flow:row nowrap;justify-content:flex-start;align-items:center}
img.banner-item[_ngcontent-umf-c24] {flex:0 0 auto;width:100%;height:100%;-o-object-fit:contain;object-fit:contain;pointer-events:none;background-image:linear-gradient(to right,transparent,#000 calc(50% - 50vh / 480 * 1125 / 2),#000 calc(50% + 50vh / 480 * 1125 / 2),transparent)}
</style><style>button.bulletin-board-btn[_ngcontent-umf-c25] {display:block;margin:0;padding:8px 15px;width:100%;border-radius:0;color:#fff}
div.bulletin-board-container[_ngcontent-umf-c25] {box-sizing:border-box;height:20px;display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center}
div.bulletin-icon[_ngcontent-umf-c25] {flex:0 0 auto;width:20px;height:20px;background-size:contain;background-repeat:no-repeat;background-position:center center;background-image:url(/mobile/img/bulletin-icon.a996c907273958ac2d65.svg)}
.more-announcement-icon[_ngcontent-umf-c25] {flex:0 0 auto;width:70px;height:100%;background-size:contain;background-repeat:no-repeat;background-position:center center;background-image:url(/mobile/img/more-announcement.e9aba3242f773db1453a.png)}
div.bulletin-board[_ngcontent-umf-c25] {flex:1 1 auto;margin-left:1em;height:20px;overflow:hidden;text-align:start;font-size:14px;font-weight:400;color:#878e97;line-height:20px}
</style><style>div.util-bar-container[_ngcontent-umf-c26] {padding:0 10px 10px;display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center}
.daily-gift[_ngcontent-umf-c26] {flex:1 0 auto;max-width:50%}
.daily-gift-title-row[_ngcontent-umf-c26] {display:flex;flex-direction:row;height:24px;align-items:center;margin-bottom:4px}
.daily-gift-username-title[_ngcontent-umf-c26] {color:#7998b1;font-size:12px;margin-right:8px;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}
.daily-gift-logo[_ngcontent-umf-c26] {background-image:url(/mobile/img/bgDailyGift.f36b818e9b284312109a.png);background-repeat:no-repeat;background-position:center;background-size:contain;min-width:70px;height:100%}
.daily-gift-logo-title[_ngcontent-umf-c26] {margin-left:27px;white-space:nowrap;font-family:"PingFangSC-Medium,PingFang SC";font-size:9px;color:#fff}
.daily-gift-value[_ngcontent-umf-c26] {font-size:19px;width:100%}
.daily-gift-value-sign[_ngcontent-umf-c26] {font-size:12px}
.util-bar[_ngcontent-umf-c26] {flex:1 0 auto;display:flex;justify-content:space-between;margin-left:auto;margin-right:16 px;max-width:50%}
button.util-btn[_ngcontent-umf-c26] {padding:0;width:50px;height:50px;color:#7998b1;font-size:12px;display:inline-flex;flex-flow:column nowrap;justify-content:space-between;align-items:center}
span.util-btn-icon[_ngcontent-umf-c26] {display:block;width:25px;height:25px;background-size:contain;background-repeat:no-repeat;background-position:center center}
span.util-btn-icon.deposit[_ngcontent-umf-c26] {background-image:url(/mobile/img/deposit.862cd26ec57e83f225c4.png)}
span.util-btn-icon.transfer[_ngcontent-umf-c26] {background-image:url(/mobile/img/transfer.a1c5b153615b288c7824.png)}
span.util-btn-icon.withdraw[_ngcontent-umf-c26] {background-image:url(/mobile/img/withdraw.a41c76f1715915566d01.png)}
span.util-btn-icon.ac[_ngcontent-umf-c26] {background-image:url(/mobile/img/ac.6e82c2d8f893679d58fb.png)}
div.cs-dialog-icon[_ngcontent-umf-c26] {display:block;width:99px;height:92px;background-size:contain;background-repeat:no-repeat;background-position:center center;background-image:url(/mobile/img/cs_img.c95648a5d29ec2d84117.svg)}
div.cs-dialog-title[_ngcontent-umf-c26] {margin-top:22px;color:#333;font-size:24px;font-weight:500}
</style><style>.home-game-board-ctn[_ngcontent-umf-c27] {display:flex;flex-flow:row nowrap}
.side-menu-ctn[_ngcontent-umf-c27] {margin-left:10px;display:flex;flex-flow:column nowrap;align-items:center;justify-content:flex-start}
.side-menu-item[_ngcontent-umf-c27] {display:flex;flex-flow:row nowrap;align-items:center;justify-content:center;width:100%;box-sizing:border-box;margin:0 0 5px;padding:10px;font-size:12px;color:#7998b1;background-size:cover;background-repeat:no-repeat;background-position:center;background-image:url(/mobile/img/bg.bb9d51fea2a2599c8737.png)}
.side-menu-item__tag[_ngcontent-umf-c27] {white-space:nowrap}
.active-side-menu[_ngcontent-umf-c27] {color:#fff;background-image:url(/mobile/img/bg_active.4290af043bcec3617ad5.png)}
.side-menu-item__icon[_ngcontent-umf-c27] {background-size:cover;background-repeat:no-repeat;background-position:center;width:24px;padding-bottom:24px;margin-right:5px}
.game-board-ctn[_ngcontent-umf-c27] {margin:0 10px;width:100%}
.side-menu-icon-lottery[_ngcontent-umf-c27] {background-image:url(/mobile/img/lottery.45d67018e32eec8b862c.png)}
.side-menu-icon-lottery.active-side-menu-icon[_ngcontent-umf-c27] {background-image:url(/mobile/img/lottery_active.88066d8dcaf479878e64.png)}
.side-menu-icon-live[_ngcontent-umf-c27] {background-image:url(/mobile/img/live.3be00c21be6f02e81489.png)}
.side-menu-icon-live.active-side-menu-icon[_ngcontent-umf-c27] {background-image:url(/mobile/img/live_active.a0070344ba437bf32dea.png)}
.side-menu-icon-slot[_ngcontent-umf-c27] {background-image:url(/mobile/img/slot.8c1c064dca40f3e255e7.png)}
.side-menu-icon-slot.active-side-menu-icon[_ngcontent-umf-c27] {background-image:url(/mobile/img/slot_active.9725d9aea1b9d087c9b7.png)}
.side-menu-icon-sport[_ngcontent-umf-c27] {background-image:url(/mobile/img/sport.56210547767ef73a4d32.png)}
.side-menu-icon-sport.active-side-menu-icon[_ngcontent-umf-c27] {background-image:url(/mobile/img/sport_active.d66e7bcb8a1eeb5070c7.png)}
.side-menu-icon-fishing[_ngcontent-umf-c27] {background-image:url(/mobile/img/fishing.149177d7d963fea179e2.png)}
.side-menu-icon-fishing.active-side-menu-icon[_ngcontent-umf-c27] {background-image:url(/mobile/img/fishing_active.016cd2b3678134b02ace.png)}
.side-menu-icon-boardgame[_ngcontent-umf-c27] {background-image:url(/mobile/img/boardgame.201c62bfbf568040859b.png)}
.side-menu-icon-boardgame.active-side-menu-icon[_ngcontent-umf-c27] {background-image:url(/mobile/img/boardgame_active.eb4ce6f334b0fc8e9137.png)}
.side-menu-icon-esport[_ngcontent-umf-c27] {background-image:url(/mobile/img/esport.a4eb4a91f21db4f5ee2c.png)}
.side-menu-icon-esport.active-side-menu-icon[_ngcontent-umf-c27] {background-image:url(/mobile/img/esport_active.23c7638fe647fe2598c0.png)}
</style><style>.tab-bar[_ngcontent-umf-c29] {box-sizing:content-box;min-height:49px;display:flex;flex-flow:row nowrap;justify-content:space-around;align-items:stretch}
.tab-bar__nav-btn[_ngcontent-umf-c29] {flex:1 1 0;min-height:49px;color:#5d636e;display:flex;flex-flow:column nowrap;justify-content:flex-start;align-items:center}
.tab-bar__nav-btn--active[_ngcontent-umf-c29] {color:#0b93ae}
.tab-bar__nav-btn__icon[_ngcontent-umf-c29] {flex:0 0 auto;display:block;margin-top:6px;width:24px;height:24px;background-size:contain;background-repeat:no-repeat;background-position:center}
.tab-bar__nav-btn__icon--home[_ngcontent-umf-c29] {background-image:url(/mobile/img/home.93b20d6e835f71c043b8.png)}
.tab-bar__nav-btn__icon--activity[_ngcontent-umf-c29] {background-image:url(/mobile/img/activity.8a6a35ba2ac648314ace.png)}
.tab-bar__nav-btn__icon--cs[_ngcontent-umf-c29] {background-image:url(/mobile/img/datin.svg)}
.tab-bar__nav-btn__icon--brand[_ngcontent-umf-c29] {background-image:url(/mobile/img/huodong.svg)}
.tab-bar__nav-btn__icon--my[_ngcontent-umf-c29] {background-image:url(/mobile/img/my.87c43b85171cb8232892.png)}
.tab-bar__nav-btn--active[_ngcontent-umf-c29] > .tab-bar__nav-btn__icon--home[_ngcontent-umf-c29] {background-image:url(/mobile/img/active_home.485e6c98acecf897b8ba.png)}
.tab-bar__nav-btn--active[_ngcontent-umf-c29] > .tab-bar__nav-btn__icon--activity[_ngcontent-umf-c29] {background-image:url(/mobile/img/active_activity.0eee109d4b6a4ccedf91.png)}
.tab-bar__nav-btn--active[_ngcontent-umf-c29] > .tab-bar__nav-btn__icon--cs[_ngcontent-umf-c29] {background-image:url(/mobile/img/active_cs.9be7ce116557877dec91.png)}
.tab-bar__nav-btn--active[_ngcontent-umf-c29] > .tab-bar__nav-btn__icon--brand[_ngcontent-umf-c29] {background-image:url(/mobile/img/active_brand.26b0bef9602b57eac72e.png)}
.tab-bar__nav-btn--active[_ngcontent-umf-c29] > .tab-bar__nav-btn__icon--my[_ngcontent-umf-c29] {background-image:url(/mobile/img/active_my.030b4715dce6d516f9c8.png)}
.tab-bar__nav-btn__title[_ngcontent-umf-c29] {flex:0 0 auto;display:block;margin-top:6px;color:inherit;font-size:10px;font-weight:400;line-height:14px}
</style><style>.lottery-board-ctn[_ngcontent-umf-c30] {display:flex;flex-flow:column wrap;justify-content:flex-start;height:100%}
.lottery-btns-ctn[_ngcontent-umf-c30] {display:flex;flex-flow:row wrap;margin-bottom:10px}
.lottery-btn[_ngcontent-umf-c30] {display:flex;flex-flow:column nowrap;align-items:center;width:25%;box-sizing:border-box;border-radius:0;padding:10px;font-size:12px;background-size:cover;background-repeat:no-repeat;background-position:center;background-image:url(/mobile/img/lottery_bg.5b0db9f6401c9434ec75.png)}
.title[_ngcontent-umf-c30] {color:#a9bed8}
.sub-title[_ngcontent-umf-c30] {color:#7998b1}
.lottery-btn[_ngcontent-umf-c30]:first-child {border-radius:10px 0 0}
.lottery-btn[_ngcontent-umf-c30]:nth-child(4) {border-radius:0 10px 0 0}
.lottery-btn[_ngcontent-umf-c30]:nth-child(5) {border-radius:0 0 0 10px}
.lottery-btn[_ngcontent-umf-c30]:nth-child(8) {border-radius:0 0 10px}
.lottery-btn-icon[_ngcontent-umf-c30] {background-size:cover;background-repeat:no-repeat;background-position:center;width:50px;height:50px}
.lottery-lobby[_ngcontent-umf-c30] {background-size:cover;background-repeat:no-repeat;background-position:center;background-image:url(/mobile/img/lottery_lobby.3ebc4b9cfc4f865db015.png);border-radius:10px;width:100%;padding-bottom:calc((100% * 144 / 314))}
.game-1[_ngcontent-umf-c30] {background-image:url(/mobile/img/1.94732245082a337d973a.png)}
.game-6[_ngcontent-umf-c30] {background-image:url(/mobile/img/6.f45183d305b4055e0491.png)}
.game-26[_ngcontent-umf-c30] {background-image:url(/mobile/img/26.b99820baf827d5c86613.png)}
.game-27[_ngcontent-umf-c30] {background-image:url(/mobile/img/27.b66a27dc46bf56e60c6a.png)}
.game-28[_ngcontent-umf-c30] {background-image:url(/mobile/img/28.5004283522d159495603.png)}
.game-32[_ngcontent-umf-c30] {background-image:url(/mobile/img/32.552f1aa9ef799e732bce.png)}
.game-43[_ngcontent-umf-c30] {background-image:url(/mobile/img/43.c402a9fb3d3945ac39b8.png)}
.game-45[_ngcontent-umf-c30] {background-image:url(/mobile/img/45.36dd88e459f576fea52e.png)}
</style><style>div.dialog-wrapper[_ngcontent-umf-c35] {box-sizing:border-box;padding:16px 16px 10px}
div.content-wrapper[_ngcontent-umf-c35] {display:flex;flex-flow:column nowrap;justify-content:flex-start;align-items:center}
div.content-plain-text[_ngcontent-umf-c35] {color:#333;font-size:20px;font-weight:500;line-height:28px;text-align:center;word-break:break-word;white-space:pre-line}
mat-dialog-content[_ngcontent-umf-c35] {margin:0 -40px;padding:0 40px;max-height:45vh}
mat-dialog-actions[_ngcontent-umf-c35] {margin:20px 0 0;padding:0;min-height:44px;display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:stretch}
button[_ngcontent-umf-c35] {flex:1 1 100%;padding:10px;border-radius:10px;font-size:18px;font-weight:500;line-height:25px;color:#fff;text-align:center}
button[_ngcontent-umf-c35]:not(:first-child) {margin-left:10px}
button[_ngcontent-umf-c35]:active {webkit-filter:brightness(80%);filter:brightness(80%)}
button.ok-btn[_ngcontent-umf-c35] {background:linear-gradient(to bottom,#13a2ba,#087c95)}
button.cancel-btn[_ngcontent-umf-c35] {background:linear-gradient(to bottom,#9ea3aa,#59585d)}
</style><style>div.game-category[_ngcontent-umf-c36] {margin:30px 26px}
div.game-category[_ngcontent-umf-c36]:not(:first-child) {margin-top:15px}
.suggested-games-wrapper-outer[_ngcontent-umf-c36] {margin-left:-15px;margin-right:-15px}
.suggested-games-wrapper-inner[_ngcontent-umf-c36] {display:inline-block;padding-left:15px;padding-right:15px;vertical-align:top}
jx-scroll-x-view.suggested-games-wrapper-scroller[_ngcontent-umf-c36] {position:relative}
div.game-category-header[_ngcontent-umf-c36] {color:#fff;font-size:18px;font-weight:500;line-height:25px;display:flex;flex-flow:row nowrap;justify-content:flex-start;align-items:center}
jx-game-category-icon[_ngcontent-umf-c36]:not(:empty) {margin-right:10px}
div.game-category-content[_ngcontent-umf-c36] {position:relative;margin-top:10px}
button.game-btn[_ngcontent-umf-c36] {box-sizing:border-box;padding:10px;width:100%;min-height:70px;border-radius:10px;background-color:rgba(20,35,55,.4);display:flex;flex-flow:row nowrap;justify-content:flex-start;align-items:stretch;overflow:hidden}
button.game-btn[_ngcontent-umf-c36]:not(:first-child) {margin-top:10px}
jx-game-icon[_ngcontent-umf-c36] {flex:0 0 auto;width:60px;height:auto;margin:-10px 0 -10px -10px}
div.game-info[_ngcontent-umf-c36] {text-align:left;margin-left:5px;display:flex;flex-flow:column;justify-content:space-between;align-items:flex-start}
div.game-info-name[_ngcontent-umf-c36] {box-sizing:border-box;font-size:18px;font-weight:500;color:#fff}
div.game-info-description[_ngcontent-umf-c36] {box-sizing:border-box;font-size:14px;color:#878e97}
div.corner-indicator[_ngcontent-umf-c36] {position:absolute;top:0;right:0;width:37px;height:37px;background-size:contain;background-repeat:no-repeat;background-position:top right;border-top-right-radius:inherit}
div.corner-indicator.hot-game[_ngcontent-umf-c36] {background-image:url(/mobile/img/hot-game-top-right-corner.585af3838d7d0c33c982.svg)}
div.corner-indicator.new-game[_ngcontent-umf-c36] {background-image:url(/mobile/img/new-game-top-right-corner.130d3b347f095336c1d0.svg)}
#lottery_lobby_wrapper[_ngcontent-umf-c36] {display:flex;position:absolute;left:0;right:0;top:55px;bottom:0}
#lottery_lobby_menu[_ngcontent-umf-c36] {display:block;overflow-y:scroll;overflow-x:hidden;margin-bottom:16px}
#lottery_lobby_menu[_ngcontent-umf-c36]   button.game-category-header[_ngcontent-umf-c36] {font-size:16px;margin-top:29px;color:#878e97;text-align:start;width:100%;border-radius:0}
#lottery_lobby_menu[_ngcontent-umf-c36]   button.game-category-header.selected[_ngcontent-umf-c36],#lottery_lobby_menu[_ngcontent-umf-c36]   button.game-subcategory-header.selected[_ngcontent-umf-c36] {color:#fff}
#lottery_lobby_menu[_ngcontent-umf-c36]   button.game-category-header.selected[_ngcontent-umf-c36] > div[_ngcontent-umf-c36] {display:flex}
#lottery_lobby_menu[_ngcontent-umf-c36]   button.game-category-header[_ngcontent-umf-c36] > div[_ngcontent-umf-c36]::before {content:"";margin-right:10px}
#lottery_lobby_menu[_ngcontent-umf-c36]   button.game-category-header.selected[_ngcontent-umf-c36] > div[_ngcontent-umf-c36]::before {width:2px;background:#13a2ba;height:20px;border-radius:1px}
#lottery_lobby_menu[_ngcontent-umf-c36]   button.game-category-header[_ngcontent-umf-c36]:nth-of-type(1) {margin-top:23px}
#lottery_lobby_lottery_subcategory[_ngcontent-umf-c36] {display:block}
#lottery_lobby_lottery_subcategory[_ngcontent-umf-c36]   button.game-subcategory-header[_ngcontent-umf-c36] {color:#878e97;font-size:14px;margin-top:15px;margin-left:16px;text-align:start;width:100%}
#lottery_lobby_list[_ngcontent-umf-c36] {width:74.4%;overflow-y:scroll;right:0;position:absolute;top:0}
.lottery_lobby_holder[_ngcontent-umf-c36] {display:flex;flex-direction:column;justify-content:space-between;position:fixed;left:0;bottom:0;top:55px;padding-left:5px;padding-bottom:10px;width:25.6%;min-width:100px;background:#111f32}
.trend_and_afk[_ngcontent-umf-c36] {bottom:10px;padding-left:16px;height:125px}
.circle_btn[_ngcontent-umf-c36] {display:block;height:56px;width:56px;border-radius:50%;background-color:#132e47;margin-bottom:6px;background-repeat:no-repeat;background-position:top 7px center;background-size:30px 30px;text-align:center;color:#7998b1;font-size:11px;padding-top:30px}
.circle_btn.afk[_ngcontent-umf-c36] {background-image:url(/mobile/img/icLottoAfk.d8c0b0c0a5400bc95ef0.png)}
.circle_btn.trend[_ngcontent-umf-c36] {background-image:url(/mobile/img/icLottoTrend.1ff1b0e5ef6e81cf9ec7.png)}
</style><style>[_nghost-umf-c40] {box-sizing:border-box;display:block;padding:15px}
</style><style>.game-category-icon[_ngcontent-umf-c37] {display:block;box-sizing:border-box;width:20px;height:20px;background-size:contain;background-repeat:no-repeat;background-position:center center}
.game-category-icon--pk10[_ngcontent-umf-c37],.game-category-icon.game-category-PK10[_ngcontent-umf-c37] {background-image:url(/mobile/img/playlist_icon_pk10.c006f297e87d63e10e3a.svg)}
.game-category-icon--ssc[_ngcontent-umf-c37],.game-category-icon.game-category-时时彩[_ngcontent-umf-c37] {background-image:url(/mobile/img/playlist_icon_ssc.a159e254711a0b1e29e8.svg)}
.game-category-icon--ffc[_ngcontent-umf-c37],.game-category-icon.game-category-分分彩[_ngcontent-umf-c37] {background-image:url(/mobile/img/playlist_icon_ffc.8ce5c475644c49ef4e65.svg)}
.game-category-icon--suggested[_ngcontent-umf-c37],.game-category-icon.game-category-推荐游戏[_ngcontent-umf-c37] {background-image:url(/mobile/img/playlist_icon_recommend.c5bf1ee7c08f663ed8e7.svg)}
</style><style>img[_ngcontent-umf-c39] {width:100%;height:100%;-o-object-position:center center;object-position:center center;-o-object-fit:contain;object-fit:contain}
</style><style>.open-sport-btn-height-holder[_ngcontent-umf-c38] {position:relative;min-height:45px}
.open-sport-dialog-message[_ngcontent-umf-c38] {text-align:center}
.open-sport-dialog-btn[_ngcontent-umf-c38] {padding:10px;width:100%;color:#fff;font-size:18px;font-weight:400;line-height:25px;border-radius:10px;background-image:linear-gradient(179deg,#13a2ba,#087c95)}
.game-container[_ngcontent-umf-c38] {box-sizing:border-box;margin-right:-15px;margin-bottom:-15px;min-height:calc(100px + 10px + 22px + 15px);display:flex;flex-flow:row wrap;justify-content:flex-start;align-items:flex-start}
.game-container__suggested-game[_ngcontent-umf-c38] {flex:0 0 auto;box-sizing:border-box;margin-right:15px;margin-bottom:15px;padding:0;width:100px;min-height:calc(100px + 32px);display:flex;flex-flow:column nowrap;justify-content:flex-start;align-items:flex-start}
.game-container__suggested-game__icon[_ngcontent-umf-c38] {display:block;width:100px;height:100px;border-radius:10px}
.game-container__suggested-game__name[_ngcontent-umf-c38] {display:block;margin-top:10px;color:#fff;font-size:16px;font-weight:500;line-height:22px;text-align:start}
button.game-btn[_ngcontent-umf-c38] {box-sizing:border-box;padding:10px;width:100%;min-height:70px;border-radius:10px;background-color:rgba(20,35,55,.4);display:flex;flex-flow:row nowrap;justify-content:flex-start;align-items:stretch;overflow:hidden}
button.game-btn[_ngcontent-umf-c38]:not(:first-child) {margin-top:10px}
jx-game-icon[_ngcontent-umf-c38] {flex:0 0 auto;width:60px;height:auto;margin:-10px 0 -10px -10px}
div.game-info[_ngcontent-umf-c38] {text-align:left;margin-left:5px;display:flex;flex-flow:column;justify-content:space-between;align-items:flex-start}
div.game-info-name[_ngcontent-umf-c38] {box-sizing:border-box;font-size:18px;font-weight:500;color:#fff}
div.game-info-description[_ngcontent-umf-c38] {box-sizing:border-box;font-size:14px;color:#878e97}
</style><style>button.client-service-btn[_ngcontent-umf-c41] {display:block;margin-left:auto;margin-right:15px;padding:0;width:22px;height:22px;background:url(cs.22e7bc4160b01286ee59.svg)}
button.activity-btn[_ngcontent-umf-c41] {display:block;margin:0;padding:15px;width:100%;min-height:calc(25px + 5px + 20px + 10px);position:relative;overflow:hidden;text-align:start;border-radius:10px;background-color:#132235}
button.activity-btn[_ngcontent-umf-c41]:not(:first-child) {margin-top:10px}
span.activity-status[_ngcontent-umf-c41] {position:absolute;right:0;top:0;width:56px;height:56px;background-repeat:no-repeat;background-size:contain}
span.activity-status.open[_ngcontent-umf-c41] {background-image:url(/mobile/img/activity_on.112eb4162510f8b41ae1.svg)}
span.activity-status.close[_ngcontent-umf-c41] {background-image:url(/mobile/img/activity_off.aad5a00e0282390a0a0b.svg)}
span.activity-btn-title[_ngcontent-umf-c41] {display:block;color:#fff;font-size:18px;font-weight:600;line-height:25px}
span.activity-btn-duration[_ngcontent-umf-c41] {display:block;margin-top:5px;color:#878e97;font-size:14px;font-weight:400;line-height:20px}
span.activity-btn-banner[_ngcontent-umf-c41] {display:block;margin-top:10px;padding-bottom:calc(465 / 1125 * 100%);border-radius:inherit;background-size:contain;background-repeat:no-repeat}
span.activity-btn-banner.activity-1[_ngcontent-umf-c41] {background-image:url(/mobile/img/activity_banner_1.d182dbd75070a26a3679.png)}
span.activity-btn-banner.activity-2[_ngcontent-umf-c41] {background-image:url(/mobile/img/activity_banner_2.222a8ebb8fd3dd32d2bb.png)}
span.activity-btn-banner.activity-3[_ngcontent-umf-c41] {background-image:url(/mobile/img/activity_banner_3.8a4de95932e2f72c6f89.png)}
</style><style>div.profile-header-btn-group[_ngcontent-umf-c42] {position:absolute;right:0;min-height:64px}
button.setting-btn[_ngcontent-umf-c42] {box-sizing:border-box;display:block;padding:0;width:48px;height:32px;background:url(icon_setting.981868f5f2ecdf640152.svg) center center no-repeat}
button.customer-service-btn[_ngcontent-umf-c42] {box-sizing:border-box;display:block;padding:0;width:48px;height:32px;background:url(icon_cs.2e3c77103d38ed1282c6.svg) center center no-repeat}
div.profile-detail[_ngcontent-umf-c42] {box-sizing:border-box;position:relative;width:100%;height:auto;display:flex;flex-flow:row nowrap;flex-direction:row}
div.profile-detail[_ngcontent-umf-c42] > jx-avatar[_ngcontent-umf-c42] {width:60px;height:60px;margin-left:8%;border:1px solid #fff;border-radius:30px}
div.profile-detail[_ngcontent-umf-c42] > div[_ngcontent-umf-c42] {align-self:center;margin-left:3.7%}
div.profile-detail[_ngcontent-umf-c42] > div[_ngcontent-umf-c42] > div.username[_ngcontent-umf-c42] {font-size:18px;font-weight:500}
div.profile-detail[_ngcontent-umf-c42] > div[_ngcontent-umf-c42] > div.rebate[_ngcontent-umf-c42] {color:#13a2ba;font-size:14px;font-weight:400}
div.profile-detail[_ngcontent-umf-c42] > button.add-member[_ngcontent-umf-c42] {position:absolute;right:0;background:url(icon_addmember.2ef4aece141e383c2644.svg) center center/contain no-repeat;width:32%;height:100%;display:flex;justify-content:center;flex-direction:column;align-items:flex-end;padding-right:3.2%;color:#fff;font-size:14px}
div.account-summary[_ngcontent-umf-c42] {position:relative;box-sizing:border-box;padding:15px 0;min-height:82px;border-radius:5px;background-color:rgba(20,35,55,.4);display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:flex-start}
div.account-field[_ngcontent-umf-c42] {flex:1 1 auto;box-sizing:border-box;width:calc(100% / 3);color:#fff;text-align:left;margin-left:15px}
div.account-field-title[_ngcontent-umf-c42] {font-size:12px;font-weight:400;color:#5d636e}
div.account-field-value[_ngcontent-umf-c42] {margin-top:5px;font-size:20px;overflow:hidden;text-overflow:ellipsis}
div.account-field-value.frozen[_ngcontent-umf-c42] {color:#878e97}
div.finance-btn-group[_ngcontent-umf-c42] {box-sizing:border-box;padding:20px 0;min-height:44px;display:flex;flex-flow:row nowrap;justify-content:space-between;align-items:center}
button.finance-btn[_ngcontent-umf-c42] {flex:1 1 auto;border-radius:10px;min-height:44px;color:#fff;font-size:18px;font-weight:600;display:flex;flex-flow:row nowrap;justify-content:center;align-items:center}
button.finance-btn[_ngcontent-umf-c42]:not(:first-child) {margin-left:15px}
button.finance-btn.deposit-btn[_ngcontent-umf-c42] {background-image:linear-gradient(179deg,#13a2ba,#087c95)}
button.finance-btn.withdraw-btn[_ngcontent-umf-c42] {background-image:linear-gradient(124deg,#efdaaf,#dbb579)}
button.finance-btn.transaction-btn[_ngcontent-umf-c42] {flex:0 0 auto;padding-left:1em;padding-right:1em;background-image:linear-gradient(to bottom,#fad961,#ff8f4f)}
button.finance-btn[_ngcontent-umf-c42] > span.finance-btn-icon[_ngcontent-umf-c42] {margin-right:10px}
span.finance-btn-icon[_ngcontent-umf-c42] {display:block;width:20px;height:15px;background-size:contain;background-repeat:no-repeat;background-position:center center}
span.finance-btn-icon.deposit-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_recharge.6651e6227f354934f309.svg)}
span.finance-btn-icon.withdraw-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_withdraw.4635218f77b26e3cbd09.svg)}
.finance-entry-btn-separator[_ngcontent-umf-c42] {border-radius:1px;border:1px solid rgba(151,151,151,.1283)}
.finance-entry-btn-group[_ngcontent-umf-c42]:not(:empty) {padding-top:10px;padding-bottom:10px}
.finance-entry-btn-group[_ngcontent-umf-c42] + .finance-entry-btn-group[_ngcontent-umf-c42]:not(:empty) {border-top:1px solid rgba(151,151,151,.1283)}
.finance-entry-btn-group__btn[_ngcontent-umf-c42] {box-sizing:border-box;padding:10px 0 10px 20px;width:100%;color:#fff;font-size:18px;font-weight:400;line-height:25px;border-radius:5px;background-color:transparent;display:flex;flex-flow:row nowrap;justify-content:flex-start;align-items:center}
.finance-entry-btn-group__btn[_ngcontent-umf-c42] > mat-icon[_ngcontent-umf-c42] {flex:0 0 auto;display:block;margin-left:auto;color:#404b5e}
span.finance-entry-icon[_ngcontent-umf-c42] {flex:0 0 auto;display:block;box-sizing:border-box;margin-right:30px;width:20px;height:20px;background-size:contain;background-repeat:no-repeat;background-position:center center}
span.finance-entry-icon.welfare-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_welfare.9a30f882b9214fb53409.svg)}
span.finance-entry-icon.transfer-records-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_transfer_records.e931e4169c9694ccaa4c.svg)}
span.finance-entry-icon.finance-detail-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_details.a5e498d3fbd113177709.svg)}
span.finance-entry-icon.bet-record-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_betting.c3b62d8f80c6cc9299de.svg)}
span.finance-entry-icon.bet-addition-detail-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_record.fcc3ccfdccae01457a71.svg)}
span.finance-entry-icon.agency-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_agency.b60905c7735122c746b4.svg)}
span.finance-entry-icon.payroll-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_payroll.3edeb6dbc1798f073ea4.svg)}
span.finance-entry-icon.bonus-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_bonus.2cabf0ac44aa9e1fd4f3.svg)}
span.finance-entry-icon.report-icon[_ngcontent-umf-c42] {background-image:url(/mobile/img/icon_report.eae6900d1a38a4302975.svg)}
</style><style>[_nghost-umf-c8] {display:inline-block;width:1em;height:1em}
img.avatar-icon[_ngcontent-umf-c8] {width:100%;height:100%}
.ng-invalid{
	background: #132235;
    border: 1px solid transparent;
    outline: 0;
    display: block;
    box-sizing: border-box;
    padding: 14px;
    width: 100%;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #fff;
    border-radius: inherit;
}
</style>
</head>
<body style="color: white; background-color: #0c192c;">
  <jx-root ng-version="8.2.12">
    <router-outlet></router-outlet>
    <jx-main-wrapper _nghost-umf-c0="" class="ng-star-inserted">
      <router-outlet _ngcontent-umf-c0=""></router-outlet>
      <jx-deposit-wrapper _nghost-umf-c1="" class="ng-star-inserted">
        <router-outlet _ngcontent-umf-c1=""></router-outlet>
        <jx-deposit-amount-page _nghost-umf-c10="" class="ng-star-inserted">
          <!---->
          <jx-header-view _ngcontent-umf-c10="" _nghost-umf-c4="">
            <div _ngcontent-umf-c4="" class="header-view__nav-row-wrapper safe-area-top safe-area-left safe-area-right" jxsafearealeft="" jxsafearearight="" jxsafeareatop="">
              <jx-header-row _ngcontent-umf-c4="" class="header-view__nav-row-wrapper__container" _nghost-umf-c6="">
                <div _ngcontent-umf-c4="" class="header-view__nav-row-wrapper__container__nav-row">
                  <!---->
                  <!---->
                  <button _ngcontent-umf-c4="" class="header-view__nav-row-wrapper__container__nav-row__prefix ng-star-inserted" onclick="location.href='javascript:history.back(-1)'">
                  <i class="icon iconfont">&#xe67c;</i></button>
                  <!---->
                  <!---->
                  <!---->
                  <div _ngcontent-umf-c4="" class="header-view__nav-row-wrapper__container__nav-row__title ng-star-inserted">支付宝直充</div>
                  <div _ngcontent-umf-c4="" class="header-view__nav-row-wrapper__container__nav-row__content">
                    <jx-header-nav-content _ngcontent-umf-c10="" _nghost-umf-c11="">
                      <span _ngcontent-umf-c10="" style="margin-left: 0.5em;" class="deposit-icon bank-id-99"></span>
                    </jx-header-nav-content>
                  </div>
                  <!---->
                  <!---->
                  <!---->
                  <!----></div>
              </jx-header-row>
            </div>
            <div _ngcontent-umf-c4="" class="header-view__content-wrapper" style="padding-top: 64px;">
              <div _ngcontent-umf-c4="" class="header-view__content-wrapper__content-container">
                <jx-safe-area _ngcontent-umf-c10="" class="safe-area-top safe-area-bottom safe-area-left safe-area-right" style="display: block; box-sizing: border-box;">
                  <div _ngcontent-umf-c10="" class="deposit-amount-container">
				  <form method="post" id="bank_recharge_from" action="{:U('Account/recharge')}" >
                    <jx-form-field _ngcontent-umf-c10="" title="充值金额" _nghost-umf-c12="">
                      <!---->
                      <!---->
                      <div _ngcontent-umf-c12="" class="form-field-header ng-star-inserted">充值金额
                        <!---->
                        <jx-form-field-subheading _ngcontent-umf-c10="" _nghost-umf-c13="" class="ng-star-inserted">账户余额：
                          <span _ngcontent-umf-c10="" class="deposit-balance-amount din-alternate-bold">{$userinfo['balance']}</span></jx-form-field-subheading>
                      </div>
                      <div _ngcontent-umf-c12="" class="form-field-content">
                        <!---->
                        <!---->
                        <input _ngcontent-umf-c10="" jxforminput="" type="text" name="amount" id="amount" placeholder="最低充值{$Allmsg.minmoney|floor}" class="ng-untouched ng-pristine ng-invalid ng-star-inserted">
                        <!----></div>
                      <div _ngcontent-umf-c12="" class="form-field-footer">
                        <!----></div>
					  
					  <div _ngcontent-umf-c12="" class="form-field-header ng-star-inserted">支付姓名
                        <!---->
                        <jx-form-field-subheading _ngcontent-umf-c10="" _nghost-umf-c13="" class="ng-star-inserted">
                          <span _ngcontent-umf-c10="" class="deposit-balance-amount din-alternate-bold"></span></jx-form-field-subheading>
                      </div>
                      <div _ngcontent-umf-c12="" class="form-field-content">
                        <!---->
                        <!---->
                        <input _ngcontent-umf-c10="" jxforminput="" type="text" name="payname" id="payname" placeholder="请输入您付款账号姓名" class="ng-untouched ng-pristine ng-invalid ng-star-inserted">
                        <!----></div>
						<input type="radio"  name="paytype"  value="{$Allmsg['paytype']}" checked="checked" class="input_txt" style="display:none;">
                      <div _ngcontent-umf-c12="" class="form-field-footer">
                        <!----></div>
                    </jx-form-field>
                    <!---->
                    <jx-form-hint _ngcontent-umf-c10="" _nghost-umf-c16="" class="show-asterisk">小提示：使用非整数金额充值能更快到账哦～
                      <div _ngcontent-umf-c10="">（例如：
                        <span _ngcontent-umf-c10="" style="color: #13A2BA;">101、1002、2011</span>）</div></jx-form-hint>
                    <jx-form-field _ngcontent-umf-c10="" title="请选择充值线路" _nghost-umf-c12="" class="ng-star-inserted">
                      <!---->
                      <!---->
                      <div _ngcontent-umf-c12="" class="form-field-header ng-star-inserted">请选择充值线路</div>
                      <div _ngcontent-umf-c12="" class="form-field-content">
                        <!---->
                        <!---->
                        <!---->
                        <button disabled="disabled" _ngcontent-umf-c10="" class="recharge-route-btn ng-star-inserted selected" jxformbutton="" _nghost-umf-c17="">
                          <div _ngcontent-umf-c10="" class="icon"></div>支付宝扫码</button>
                        <button disabled="disabled" _ngcontent-umf-c10="" class="recharge-route-btn ng-star-inserted" jxformbutton="" _nghost-umf-c17="">
                          <div _ngcontent-umf-c10="" class="icon"></div>*一键支付宝扫码（暂未开通）</button>
                        <!----></div>
                      <div _ngcontent-umf-c12="" class="form-field-footer"></div>
                    </jx-form-field>
                    <!---->
                    <jx-form-hint _ngcontent-umf-c10="" _nghost-umf-c16="" class="show-asterisk ng-star-inserted">请勿使用信用卡充值，导致的银行已扣款却充值不到账平台无法处理。</jx-form-hint>
                    <div _ngcontent-umf-c10="" class="footer-fixed-container safe-area-bottom safe-area-left safe-area-right" jxsafeareabottom="" jxsafearealeft="" jxsafearearight="">
                      <div _ngcontent-umf-c10="" class="footer-wrapper">
                        <button _ngcontent-umf-c10="" class="back-btn mat-elevation-z10 nextbtn" type="button">确认充值</button>
					  </div>
                    </div>
				  </form>	
                  
				  <!----><!----><!---->
				  <!----><!----><!---->
				  <!----><!----><!---->
				  <!----><!----><!---->
				  <div id="pay_alipay" style="display:none; ">
				  
                    <jx-form-field _ngcontent-umf-c10="" title="充值金额" _nghost-umf-c12="">
                      <!---->
                      <!---->
                      <div _ngcontent-umf-c12="" class="form-field-header ng-star-inserted">充值金额
                        <!---->
                        <jx-form-field-subheading _ngcontent-umf-c10="" _nghost-umf-c13="" class="ng-star-inserted">
                          <span _ngcontent-umf-c10="" class="deposit-balance-amount din-alternate-bold"></span></jx-form-field-subheading>
                      </div>
                      <div _ngcontent-umf-c12="" class="form-field-content">
                        <!---->
                        <!---->
                        <span><em class="mark" id="saomabill_amount" way-data="saomabill.amount" style="font-style:normal"></em>元</span>
                        <!----></div>
                      <div _ngcontent-umf-c12="" class="form-field-footer">
                        <!----></div>
					  
					  <div _ngcontent-umf-c12="" class="form-field-header ng-star-inserted">订单编号
                        <!---->
                          <span _ngcontent-umf-c10="" class="deposit-balance-amount din-alternate-bold"></span>
                      </div>
                      <div _ngcontent-umf-c12="" class="form-field-content">
                        <!---->
                        <!---->
                        <span way-data="saomabill.trano" id="saomabill_trano" class="mark"></span>
                        <!----></div>
                      <div _ngcontent-umf-c12="" class="form-field-footer">
                        <!----></div>
					  <div _ngcontent-umf-c12="" class="form-field-header ng-star-inserted">附言码
                        <!---->
                        
                      </div>
                      <div _ngcontent-umf-c12="" class="form-field-content">
                        <!---->
                        <!---->
                        <span way-data="saomabill.id" id="saomabill_id" class="mark"></span>
                        <!----></div>
                      <div _ngcontent-umf-c12="" class="form-field-footer">
                        <!----></div>
					  <div _ngcontent-umf-c12="" class="form-field-header ng-star-inserted">支付宝付款二维码
                        <!---->
                        <jx-form-field-subheading _ngcontent-umf-c10="" _nghost-umf-c13="" class="ng-star-inserted">
                          <span _ngcontent-umf-c10="" class="deposit-balance-amount din-alternate-bold"></span></jx-form-field-subheading>
                      </div>
					  <li class='payerweima' style='height:auto;list-style:none;'><span><img src="{$Allmsg['ewmurl']}" style='width:150px;border:none;display:block; margin:0 auto;'></span></li>
                    </jx-form-field>
                    <!---->
                    <jx-form-hint _ngcontent-umf-c10="" _nghost-umf-c16="" class="show-asterisk">
                      
                        <span _ngcontent-umf-c10="" style="color: #13A2BA;">尊敬的客户您好，您的充值订单已经生成，请您在该页面继续完成充值。</span></jx-form-hint>
                    <!---->
                    <jx-form-hint _ngcontent-umf-c10="" _nghost-umf-c16="" class="show-asterisk ng-star-inserted"><span _ngcontent-umf-c10="" style="color: #13A2BA;">扫码付款后，请点击提交按钮</span></jx-form-hint>
					<input type="hidden" way-data="saomabill_paytype" id="saomabill.paytype" />
				<a class="btn common_btn" style="display: block;margin: 1em auto;text-align: center;width: 100%;height: 55px;font-size: 18px;font-weight: 400;border-radius: 10px;color: #fff;background-image: linear-gradient(179deg,#13a2ba,#087c95);line-height: 55px;text-decoration:none;" href="javascript:;"  onclick="alt('充值成功申请已提交')">提交</a>	
                  </div>
                </jx-safe-area>
              </div>
            </div>
            <div _ngcontent-umf-c4="" class="header-view__footer-row-wrapper safe-area-bottom safe-area-left safe-area-right" jxsafeareabottom="" jxsafearealeft="" jxsafearearight=""></div>
          </jx-header-view>
        </jx-deposit-amount-page>
      </jx-deposit-wrapper>
    </jx-main-wrapper>
  </jx-root>
<script>
			$('.nextbtn').click(function () {
			if($('input[name=amount]').val()=="") {
				alert('请输入充值金额');return false;
			}
			if($('input[name=payname]').val()==""){
				alert('请输入支付账号');return false;
			}
			$.ajax({
				type : 'POST',
				url : "{:U('Apijiekou/addrecharge')}",
				data :{
					amount      : $('#amount').val(),
					paytype     : $("input[name='paytype']:checked").val(),
					userpayname : $("input[name='payname']").val(),
				},
				success : function (data) {
					if(data.sign == true){
						$('.nextbtn').hide();
						$('.choiceBank').hide();
						$('.choiceMoney').hide();
						$("#pay_alipay").show();
						$('#bank_recharge_from').hide();
						// way.set("saomabill.amount",data.data.amount);
						// way.set("saomabill.trano",data.data.trano);
						// way.set("saomabill.id",data.data.id);
						// way.set("saomabill.paytype",data.data.paytype);
						$('#saomabill_paytype').val(data.data.paytype);
						$('#saomabill_amount').text(data.data.amount);
						$('#saomabill_trano').text(data.data.trano);
						$('#saomabill_id').text(data.data.id);
						setTimeout(function () {checkispay(data.data.trano);}, 5000);	
					}else{
						alert(data.message);
					}
				}
			})
		})
var checkispayid;
function checkispay(trano){
	clearTimeout(checkispayid);
	$.ajax({
		url: "{:U('Apijiekou/checkrechargeisok1')}",
		data:{"trano": trano},  
		type: "post",
		dataType: "json",
		async:false,
		success: function(result) {
			if (result.sign === true) {
				if(result.state!=0){
					if(result.state==1){
						alt("充值成功");
					}else if(result.state==-1){
						alt("充值失败",-1);
					}
					//window.location.href = "{:U('Account/dealRecord2')}";
				}else{
					checkispayid = setTimeout(function () {
					checkispay(trano);
					}, 5000);	
				}
			} else {
					checkispayid = setTimeout(function () {
					checkispay(trano);
					}, 5000);	
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown) {
					checkispayid = setTimeout(function () {
					checkispay(trano);
					}, 5000);	
		}
	});
}; 
$('.common_btn').click(function () {
	setTimeout(function (){
		window.location.href = '/Account.dealRecord2.do';
	},1500)
})
	</script>
</body>

</html>