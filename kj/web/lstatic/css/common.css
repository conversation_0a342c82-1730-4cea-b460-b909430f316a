* {
    -webkit-touch-callout: none;
}

body {
    background-color: #f7f7f7;
    font-size: 14px;
}

h1, h2, h3, h4, h5, h6, p, form, ul, li {
    margin: 0px;
    padding: 0px;
    list-style-type: none;
}
.common-article img{width:100%;}
.am-list {
    margin: 0;
}

.am-with-fixed-header {
    padding-top:0;
}

.am-header-skin {
    background-color: #39bbae;
    z-index: 110;
    height: 35px;
    line-height: 35px;
}

    .am-header-skin .am-header-title {
        font-size: 16px;
        font-weight: bold;
    }

    .am-header-skin .am-header-title, .am-header-skin a {
        color: white;
    }

.am-header .am-header-nav img {
    height: 22px;
}

.am-tabs-d2 .am-tabs-nav > .am-active a {
    background: #39bbae;
    color: #fff;
}

.am-tabs-d2 .am-tabs-nav > .am-active {
    background: #39bbae;
    border-bottom: 2px solid #fff;
}

    .am-tabs-d2 .am-tabs-nav > .am-active:after {
        border-bottom-color: #39bbae;
    }

.am-tabs-bd {
    border: 0;
}

.am-btn-success,
.am-btn-skin {
    background: #39bbae;
    color: #fff;
}

.am-dimmer.am-active, .am-modal.am-modal-active {
    display: block;
}

.am-text-success {
    color: #39bbae;
}

.am-badge-sucLine {
    color: #39bbae;
    border: 1px #39bbae solid;
    background: transparent;
}

.am-navbar-nav {
    /*background: -webkit-linear-gradient(top, #3d3d3d, #2e2e2e);*/
   background: -webkit-gradient(linear, 0% 0%, 100% 0%, from(#34c1a0), to(#40b3be));

}
body .am-with-fixed-navbar{
     padding-bottom: 1.8rem
}
body .am-navbar{
    height: 1.8rem;
    line-height: 1.8rem;
    display: none;
}
    .am-navbar-nav a {
        color: #abe3ce;
    }
.am-navbar .am-navbar-nav li a{
    height: 1.8rem;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
}
        .am-navbar-nav a img {
            display: block;
            vertical-align: middle;
            height: 32px;
            width: 32px;
            margin: 0px auto 0;
        }

        .am-navbar-nav a .icon2 {
            display: none;
        }

    .am-navbar-nav .am-active .icon1 {
        display: none;
    }

    .am-navbar-nav .am-active .icon2 {
        display: block;
    }
    .am-navbar-nav a .am-navbar-label{
        color: #abe3ce;
    }
.am-navbar-nav .am-active a .am-navbar-label{
    color: #ffba15;
}
footer {
    height: 50px;
}

.am-modal-skin {
    transition: all 0.3s;
}

#UI-am-modal-loading .am-modal-dialog {
    border-radius: 6px;
}



/* 弹出框 */
.ui-bet-confirm {
    padding: 10px;
    font-size: 14px;
    text-align: left;
}

    .ui-bet-confirm textarea {
        width: 100%;
        border: 1px solid #CCC;
        border-radius: 4px;
        height: 80px;
        resize: none;
        white-space: nowrap;
    }

    .ui-bet-confirm p {
        padding: 5px 0px;
    }
/* 08335 add 通用 */
.app-box {
    background: #fff;
}

.app-box-hd {
    padding: 10px;
    color: #333;
}

.app-box-title {
    font-weight: bold;
}

/** g-table **/
.g-tbFixed {
    table-layout: fixed;
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
}

.g-select {
    border: 0;
    position: relative;
}

    .g-select select,
    .g-select input {
        width: 100%;
        padding: 5px 20px 5px 5px;
        border: 0;
        outline: 0;
        box-sizing: border-box;
        background: transparent;
        -webkit-appearance: none;
        appearance: none;
    }

    .g-select:after {
        position: absolute;
        right: 5px;
        top: 50%;
        margin-top: -3px;
        content: "";
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #39bbae;
    }
/**08335 通用数据列表 **/
.g-list {
    margin: 0;
    padding: 0;
}

    .g-list > li {
        padding: 10px;
        background: transparent;
        color: #999;
        border: 0;
        border-bottom: 1px solid #dedede;
    }

        .g-list > li .title {
            font-size: 16px;
            color: #666;
        }

.g-searchBar {
    padding: 2px;
    border-radius: 5px;
    border: 1px #CCC solid;
    background: #fff;
    display: flex;
    -webkit-display: flex;
}

    .g-searchBar .key {
        border: 0;
        outline: 0;
        flex: 1;
        display: block;
    }

.pull-action {
    text-align: center;
    height: 45px;
    line-height: 45px;
    color: #999;
}

    .pull-action .am-icon-spin {
        display: none;
    }

    .pull-action.loading .am-icon-spin {
        display: block;
    }

    .pull-action.loading .pull-label {
        display: none;
    }


    @media screen and (min-width: 320px) {
        html {
            font-size: 29.6296px;
        }
        body {
          font-size: 12px;
        }
      }
      @media screen and (min-width: 360px) {
        html {
            font-size: 33.3333px;
        }
        body {
          font-size: 12px;
        }
      }
      @media screen and (min-width: 375px) {
        html {
            font-size: 34.7222px;
        }
        body {
          font-size: 12px;
        }
      }
      @media screen and (min-width: 384px) {
        html {
            font-size: 35.5556px;
        }
        body {
          font-size: 14px;
        }
      }
      @media screen and (min-width: 400px) {
        html {
            font-size: 37.037px;
        }
        body {
          font-size: 14px;
        }
      }
      @media screen and (min-width: 414px) {
        html {
            font-size: 38.3333px;
        }
        body {
          font-size: 14px;
        }
      }
      @media screen and (min-width: 424px) {
        html {
            font-size: 39.2593px;
        }
        body {
          font-size: 14px;
        }
      }
      @media screen and (min-width: 480px) {
        html {
            font-size: 44.4444px;
        }
        body {
          font-size: 15.36px;
        }
      }
      @media screen and (min-width: 540px) {
        html {
          font-size: 50px;
        }
        body {
          font-size: 17.28px;
        }
      }
      @media screen and (min-width: 680px) {
        html {
            font-size: 62.963px;
        }
        body {
          font-size: 23.04px;
        }
      }

      
      body .am-badge{
        padding: 0.15rem;
        font-size: 0.4rem
    }

    body #mainBox .am-navbar{
        display: block!important;
    }