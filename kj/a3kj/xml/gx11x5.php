
<?php
$api = "http://kj.bw7788.com/api?token=6fd5a1c24e146c72&code=gd15&rows=5&format=json";
$data = file_get_contents($api);
if ($data === false) {
    die('{"sign":false,"message":"无法获取数据","data":[]}');
}
$data = json_decode($data, true);
if ($data === null) {
    die('{"sign":false,"message":"JSON解析失败","data":[]}');
}
if (!isset($data['data'][0])) {
    die('{"sign":false,"message":"数据格式错误","data":[]}');
}
$latestData = $data['data'][0];
$qh = $latestData['expect'];
$hm = $latestData['opencode'];
$rq = $latestData['opentime'];
echo json_encode([
    'sign' => true,
    'message' => '获取成功',
    'data' => [
        [
            'title' => '广东11选5',
            'name' => 'gd11x5',
            'expect' => $qh,
            'opencode' => $hm,
            'opentime' => $rq,
            'source' => '开彩采集',
            'sourcecode' => ''
        ]
    ]
], JSON_UNESCAPED_UNICODE);
?>