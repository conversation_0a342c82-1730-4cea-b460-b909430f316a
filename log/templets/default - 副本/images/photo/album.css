h2 {
	color:#666;
}
.clearit {
	CLEAR: both; FONT-SIZE: 0px; LINE-HEIGHT: 0; HEIGHT: 0px
}
.cC00 {
	COLOR: #c00
}
#wrap {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 5px; MARGIN: 0px auto; WIDTH: 960px; PADDING-TOP: 5px; TEXT-ALIGN: left
}
.nInfo {
	Z-INDEX: 101; BORDER-BOTTOM: #e5e6e6 1px solid; ZOOM: 1; POSITION: relative; HEIGHT: 34px
}
.nPath {
	FLOAT: left; COLOR: #3e7cbf; LINE-HEIGHT: 34px
}
.nPath A:link {
	COLOR: #3e7cbf
}
.nPath A:visited {
	COLOR: #3e7cbf
}
.nPath A:hover {
	COLOR: #f00
}
.nMore {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; FLOAT: right; PADDING-BOTTOM: 0px; PADDING-TOP: 7px
}
.eTitle {
	HEIGHT: 49px
}
.eTitle H1 {
	FONT: 18px/30px '黑体'; MARGIN-RIGHT: 50px; PADDING-TOP: 7px; TEXT-ALIGN: center
}
.eTitle #total {
	PADDING-LEFT: 6px; FONT-SIZE: 13px; FONT-FAMILY: Verdana
}
.eControl {
	MARGIN-TOP: -49px; Z-INDEX: 100; MARGIN-LEFT: 660px; POSITION: relative; HEIGHT: 49px
}
.ecCont {
	FLOAT: right; PADDING-TOP: 9px
}
.buttonCont {
	BACKGROUND: url(e_m_01.png) no-repeat 1000px 1000px; WIDTH: 100%; HEIGHT: 100%
}
#ecbSpeed {
	FONT-WEIGHT: bold; FONT-SIZE: 14px; BACKGROUND: url(e_m_01.png) no-repeat 0px -50px; FLOAT: left; WIDTH: 43px; CURSOR: default; COLOR: #4c4c4c; LINE-HEIGHT: 28px; HEIGHT: 28px
}
#ecbSpeed .buttonCont {
	PADDING-RIGHT: 6px; WIDTH: 37px; TEXT-ALIGN: center; font-size:12px
}
.hover#ecbSpeed .buttonCont {
	COLOR: #666
}
.active#ecbSpeed .buttonCont {
	PADDING-RIGHT: 6px; BACKGROUND-POSITION: 0px -100px; WIDTH: 37px; COLOR: #196cc8
}
#ecbPre {
	BACKGROUND: url(e_m_01.png) no-repeat -50px -50px; FLOAT: left; MARGIN-LEFT: 8px; WIDTH: 31px; HEIGHT: 28px
}
.hover#ecbPre .buttonCont {
	BACKGROUND-POSITION: -50px -100px
}
.active#ecbPre .buttonCont {
	BACKGROUND-POSITION: -50px -150px
}
#ecbPlay {
	FLOAT: left; MARGIN-LEFT: 8px; WIDTH: 31px; HEIGHT: 28px
}
#ecbPlay .stop {
	BACKGROUND: url(e_m_01.png) no-repeat -100px -50px; WIDTH: 31px; HEIGHT: 28px
}
.hover#ecbPlay .stop {
	BACKGROUND-POSITION: -100px -100px
}
.active#ecbPlay .stop {
	BACKGROUND-POSITION: -100px -150px
}
#ecbPlay .play {
	BACKGROUND: url(e_m_01.png) no-repeat -350px -50px; WIDTH: 31px; HEIGHT: 28px
}
.hover#ecbPlay .play {
	BACKGROUND-POSITION: -350px -100px
}
.active#ecbPlay .play {
	BACKGROUND-POSITION: -350px -150px
}
#ecbNext {
	BACKGROUND: url(e_m_01.png) no-repeat -150px -50px; FLOAT: left; MARGIN-LEFT: 8px; WIDTH: 31px; HEIGHT: 28px
}
.hover#ecbNext .buttonCont {
	BACKGROUND-POSITION: -150px -100px
}
.active#ecbNext .buttonCont {
	BACKGROUND-POSITION: -150px -150px
}
#ecbComm {
	BACKGROUND: url(e_m_01.png) no-repeat -200px -50px; FLOAT: left; MARGIN-LEFT: 8px; WIDTH: 31px; HEIGHT: 28px
}
.hover#ecbComm .buttonCont {
	BACKGROUND-POSITION: -200px -100px
}
.active#ecbComm .buttonCont {
	BACKGROUND-POSITION: -200px -150px
}
#ecbMode {
	BACKGROUND: url(e_m_01.png) no-repeat -250px -50px; FLOAT: left; MARGIN-LEFT: 8px; WIDTH: 31px; HEIGHT: 28px
}
.hover#ecbMode .buttonCont {
	BACKGROUND-POSITION: -250px -100px
}
.active#ecbMode .buttonCont {
	BACKGROUND-POSITION: -250px -150px
}
#ecbModeReturn {
	DISPLAY: none; BACKGROUND: url(e_m_01.png) no-repeat -400px -50px; FLOAT: left; MARGIN-LEFT: 8px; WIDTH: 31px; HEIGHT: 28px
}
.hover#ecbModeReturn .buttonCont {
	BACKGROUND-POSITION: -400px -100px
}
.active#ecbModeReturn .buttonCont {
	BACKGROUND-POSITION: -400px -150px
}
#ecbFullScreen {
	BACKGROUND: url(e_m_01.png) no-repeat -300px -50px; FLOAT: left; MARGIN-LEFT: 8px; WIDTH: 31px; HEIGHT: 28px
}
.hover#ecbFullScreen .buttonCont {
	BACKGROUND-POSITION: -300px -100px
}
.active#ecbFullScreen .buttonCont {
	BACKGROUND-POSITION: -300px -150px
}
#ecbLine {
	BACKGROUND: #d1d1d1; FLOAT: left; MARGIN-LEFT: 8px; WIDTH: 1px; HEIGHT: 28px
}
#SpeedBox {
	DISPLAY: none; Z-INDEX: 100; LEFT: 0px; OVERFLOW: hidden; WIDTH: 43px; POSITION: absolute; TOP: 37px; HEIGHT: 126px
}
#SpeedCont {
	MARGIN-TOP: -126px; BACKGROUND: url(e_m_01.png) no-repeat 0px -128px; WIDTH: 43px; POSITION: relative; HEIGHT: 126px
}
.speedStep_1 {
	MARGIN-TOP: -90px! important
}
.speedStep_2 {
	MARGIN-TOP: -60px! important
}
.speedStep_3 {
	MARGIN-TOP: -30px! important
}
.speedStep_4 {
	MARGIN-TOP: -10px! important
}
.speedStep_5 {
	MARGIN-TOP: -3px! important
}
.speedStep_6 {
	MARGIN-TOP: 0px! important
}
#SpeedSlide {
	LEFT: 17px; WIDTH: 10px; POSITION: absolute; TOP: 4px; HEIGHT: 110px
}
#SpeedNonius {
	BACKGROUND: url(e_m_01.png) no-repeat -50px -200px; LEFT: 11px; WIDTH: 23px; POSITION: absolute; TOP: 2px; HEIGHT: 9px
}
#CommFormTopBox {
	DISPLAY: none; Z-INDEX: 100; RIGHT: 0px; OVERFLOW: hidden; WIDTH: 247px; POSITION: absolute; TOP: 37px
}
#CommFormTopCont {
	MARGIN-TOP: -138px; BACKGROUND: url(e_m_01.png) no-repeat 0px -450px; WIDTH: 247px; POSITION: relative; HEIGHT: 138px
}
A#cftClose {
	FONT-SIZE: 0px; RIGHT: 13px; BACKGROUND: url(e_m_01.png) no-repeat -450px -50px; OVERFLOW: hidden; WIDTH: 8px; CURSOR: default; POSITION: absolute; TOP: 23px; HEIGHT: 8px
}
A#cftClose:hover {
	BACKGROUND-POSITION: -450px -75px
}
A#cftClose:unknown {
	outline: 0
}
.commTopStep_1 {
	MARGIN-TOP: -100px! important
}
.commTopStep_2 {
	MARGIN-TOP: -60px! important
}
.commTopStep_3 {
	MARGIN-TOP: -20px! important
}
.commTopStep_4 {
	MARGIN-TOP: -10px! important
}
.commTopStep_5 {
	MARGIN-TOP: -3px! important
}
.commTopStep_6 {
	MARGIN-TOP: 0px! important
}
#cftTextarea {
	BORDER-RIGHT: #ccc 1px solid; BORDER-TOP: #ccc 1px solid; BACKGROUND: url(e_m_06.png) #fff no-repeat 0px 0px; LEFT: 10px; BORDER-LEFT: #ccc 1px solid; WIDTH: 224px; COLOR: #666; BORDER-BOTTOM: #ccc 1px solid; POSITION: absolute; TOP: 44px; HEIGHT: 54px
}
#cftSubmit {
	RIGHT: 10px; BACKGROUND: url(e_m_01.png) no-repeat -300px -450px; WIDTH: 69px; COLOR: #fff; BORDER-TOP-STYLE: none; BOTTOM: 8px; BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; POSITION: absolute; HEIGHT: 21px; BORDER-BOTTOM-STYLE: none
}
#CommFormBottomBox {
	Z-INDEX: 100; RIGHT: 0px; OVERFLOW: hidden; WIDTH: 247px; BOTTOM: 147px; POSITION: absolute
}
#CommFormBottomCont {
	BACKGROUND: url(e_m_01.png) no-repeat 0px -900px; MARGIN-BOTTOM: -138px; WIDTH: 247px; POSITION: relative; HEIGHT: 133px
}
A#cfbClose {
	FONT-SIZE: 0px; RIGHT: 13px; BACKGROUND: url(e_m_01.png) no-repeat -450px -50px; OVERFLOW: hidden; WIDTH: 8px; CURSOR: default; POSITION: absolute; TOP: 10px; HEIGHT: 8px
}
A#cfbClose:hover {
	BACKGROUND-POSITION: -450px -75px
}
A#cfbClose:unknown {
	outline: 0
}
.commBottomStep_1 {
	MARGIN-BOTTOM: -100px! important
}
.commBottomStep_2 {
	MARGIN-BOTTOM: -60px! important
}
.commBottomStep_3 {
	MARGIN-BOTTOM: -20px! important
}
.commBottomStep_4 {
	MARGIN-BOTTOM: -10px! important
}
.commBottomStep_5 {
	MARGIN-BOTTOM: -3px! important
}
.commBottomStep_6 {
	MARGIN-BOTTOM: 0px! important
}
#cfbTextarea {
	BORDER-RIGHT: #ccc 1px solid; BORDER-TOP: #ccc 1px solid; BACKGROUND: url(e_m_06.png) #fff no-repeat 0px 0px; LEFT: 10px; BORDER-LEFT: #ccc 1px solid; WIDTH: 224px; COLOR: #666; BORDER-BOTTOM: #ccc 1px solid; POSITION: absolute; TOP: 28px; HEIGHT: 50px
}
#cfbSubmit {
	RIGHT: 10px; BACKGROUND: url(e_m_01.png) no-repeat -300px -450px; WIDTH: 69px; COLOR: #fff; BORDER-TOP-STYLE: none; BOTTOM: 26px; BORDER-RIGHT-STYLE: none; BORDER-LEFT-STYLE: none; POSITION: absolute; HEIGHT: 21px; BORDER-BOTTOM-STYLE: none
}
#eFramePic {
	BACKGROUND: url(e_m_03.gif) repeat-y 0px 0px; ZOOM: 1; POSITION: relative; 
}
#efpClew {
	Z-INDEX: 10; BACKGROUND: url(e_m_02.png) no-repeat 0px 0px; LEFT: 0px; WIDTH: 150px; POSITION: absolute; TOP: 0px; HEIGHT: 0px
}
#efpClewClose {
	Z-INDEX: 2; RIGHT: 5px; OVERFLOW: hidden; WIDTH: 5px; CURSOR: pointer; POSITION: absolute; TOP: 4px; HEIGHT: 5px
}
.efpClewStep_1 {
	TOP: -5px! important; HEIGHT: 5px! important
}
.efpClewStep_2 {
	TOP: -10px! important; HEIGHT: 10px! important
}
.efpClewStep_3 {
	TOP: -15px! important; HEIGHT: 15px! important
}
.efpClewStep_4 {
	TOP: -18px! important; HEIGHT: 18px! important
}
.efpClewStep_5 {
	TOP: -21px! important; HEIGHT: 21px! important
}
#efpBigPic {
	BORDER-TOP: #e5e6e6 1px solid; WIDTH: 960px; ZOOM: 1; POSITION: relative; TEXT-ALIGN: center
}
#efpLeftArea {
	Z-INDEX: 9; BACKGROUND: #fff; FILTER:Alpha(Opacity=0); LEFT: 0px; WIDTH: 50%; POSITION: absolute; TOP: 0px; HEIGHT: 100%; opacity: 0
}
#efpRightArea {
	Z-INDEX: 9; RIGHT: 0px; BACKGROUND: #fff; FILTER:Alpha(Opacity=0); WIDTH: 50%; POSITION: absolute; TOP: 0px; HEIGHT: 100%; opacity: 0
}
.arrLeft {
	CURSOR: url(arr_left.cur),auto
}
.arrRight {
	CURSOR: url(arr_right.cur),auto
}
#efpTxt {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 12px; PADDING-TOP: 8px
}
#d_BigPic {
	MIN-HEIGHT: 300px; OVERFLOW-X: hidden; WIDTH: 960px;
}
.loading {
	BACKGROUND: url(loading_01.gif) no-repeat center center
}
#d_picTit {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 5px; FONT: 16px/24px '思源黑体','宋体'; PADDING-TOP: 5px; TEXT-ALIGN: center
}
#d_picTime {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; FONT-SIZE: 12px; PADDING-BOTTOM: 5px; COLOR: #999; LINE-HEIGHT: 24px; PADDING-TOP: 5px; FONT-FAMILY: Verdana, Geneva, sans-serif; TEXT-ALIGN: center
}
#d_picIntro {
	MARGIN: 0px auto; WIDTH: 750px; COLOR: #666; TEXT-INDENT: 2em; LINE-HEIGHT: 23px
}
#efpTxt .others {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 0px; MARGIN: 0px auto; WIDTH: 750px; COLOR: #666; PADDING-TOP: 5px; HEIGHT: 25px; TEXT-ALIGN: center
}
#efpTxt .others .tblog_bg {
	PADDING-LEFT: 20px; FLOAT: right; PADDING-TOP: 10px; HEIGHT: 15px
}
.others A:link {
	MARGIN: 0px 3px; COLOR: #3e7cbf
}
.others A:visited {
	MARGIN: 0px 3px; COLOR: #3e7cbf
}
.others A:hover {
	COLOR: #f00
}
#efpContent {
	background-color:#FFF;BORDER-RIGHT: #e5e6e6 1px solid;OVERFLOW: hidden; BORDER-LEFT: #e5e6e6 1px solid; color:#666; font-size:13px; line-height:20px
}
#efpContent p{
	padding-left:10px; padding-right:10px
}
#efpPicList {
	BORDER-RIGHT: #e5e6e6 1px solid; BORDER-TOP: #e5e6e6 1px solid; BACKGROUND: url(e_m_05.gif) repeat-x 0px 0px; OVERFLOW: hidden; BORDER-LEFT: #e5e6e6 1px solid; BORDER-BOTTOM: #e5e6e6 1px solid; HEIGHT: 117px
}
#efpPreGroup {
	PADDING-RIGHT: 0px; PADDING-LEFT: 31px; FLOAT: left; PADDING-BOTTOM: 0px; WIDTH: 129px; PADDING-TOP: 11px
}
#efpPreGroup IMG{
	width:80px; height:61px;
}
#efpNextGroup IMG{
	width:80px; height:61px;
}
#efpPrePic {
	PADDING-RIGHT: 0px; PADDING-LEFT: 11px; BACKGROUND: url(e_m_01.png) no-repeat 0px -350px; PADDING-BOTTOM: 0px; WIDTH: 99px; PADDING-TOP: 9px; HEIGHT: 72px
}
#efpPrePic TD {
	WIDTH: 94px; HEIGHT: 68px; TEXT-ALIGN: center
}
#efpPrePic IMG {
	BORDER: #e7e4df 1px solid;
}
#efpPreTxt {
	PADDING-RIGHT: 0px; PADDING-LEFT: 11px; PADDING-BOTTOM: 0px; WIDTH: 91px; PADDING-TOP: 3px; TEXT-ALIGN: center
}
.selected#efpPrePic {
	BACKGROUND-POSITION: -250px -350px
}
.selected#efpPrePic IMG {
	BORDER-LEFT-COLOR: #ff9845; BORDER-BOTTOM-COLOR: #ff9845; BORDER-TOP-COLOR: #ff9845; BORDER-RIGHT-COLOR: #ff9845
}
#efpNextGroup {
	PADDING-RIGHT: 0px; PADDING-LEFT: 20px; FLOAT: left; PADDING-BOTTOM: 0px; PADDING-TOP: 11px;WIDTH: 129px;
}
#efpNextPic {
	PADDING-RIGHT: 0px; PADDING-LEFT: 5px; BACKGROUND: url(e_m_01.png) no-repeat -125px -350px; PADDING-BOTTOM: 0px; WIDTH: 105px; PADDING-TOP: 9px; HEIGHT: 72px
}
#efpNextPic TD {
	WIDTH: 94px; HEIGHT: 68px; TEXT-ALIGN: center
}
#efpNextPic IMG {
	BORDER: #e7e4df 1px solid;
}
#efpNextTxt {
	PADDING-RIGHT: 0px; PADDING-LEFT: 11px; PADDING-BOTTOM: 0px; WIDTH: 91px; PADDING-TOP: 3px; TEXT-ALIGN: center
}
.selected#efpNextPic {
	BACKGROUND-POSITION: -375px -350px
}
.selected#efpNextPic IMG {
	BORDER-LEFT-COLOR: #ff9845; BORDER-BOTTOM-COLOR: #ff9845; BORDER-TOP-COLOR: #ff9845; BORDER-RIGHT-COLOR: #ff9845
}
#efpListLeftArr {
	BACKGROUND: url(e_m_01.png) no-repeat -100px -200px; FLOAT: left; WIDTH: 42px; HEIGHT: 117px
}
.selected#efpListLeftArr {
	BACKGROUND-POSITION: -200px -200px
}
#efpListRightArr {
	BACKGROUND: url(e_m_01.png) no-repeat -150px -200px; FLOAT: left; WIDTH: 42px; HEIGHT: 117px
}
.selected#efpListRightArr {
	BACKGROUND-POSITION: -250px -200px
}
#efpPicListCont {
	BACKGROUND: url(e_m_05.gif) repeat-x 0px -150px; FLOAT: left; OVERFLOW: hidden; WIDTH: 545px; HEIGHT: 117px
}
#efpPicListCont TABLE {
	MARGIN: 0px auto
}
#efpPicListCont .pic {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; FLOAT: left; PADDING-BOTTOM: 0px; WIDTH: 109px; PADDING-TOP: 8px; TEXT-ALIGN: center
}
#efpPicListCont .picOn {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; FLOAT: left; PADDING-BOTTOM: 0px; WIDTH: 109px; PADDING-TOP: 8px; TEXT-ALIGN: center
}
#efpPicListCont .picCont {
	WIDTH: 100px; HEIGHT: 100px
}
#efpPicListCont .pic IMG {
	BORDER-RIGHT: #b4b4b4 1px solid; BORDER-TOP: #b4b4b4 1px solid; BORDER-LEFT: #b4b4b4 1px solid; BORDER-BOTTOM: #b4b4b4 1px solid
}
.pb_01 {
	WIDTH: 4px; HEIGHT: 4px
}
.pb_03 {
	WIDTH: 4px; HEIGHT: 4px
}
.pb_06 {
	WIDTH: 4px; HEIGHT: 4px
}
.pb_08 {
	WIDTH: 4px; HEIGHT: 4px
}
.picOn IMG {
	BORDER-LEFT-COLOR: #ffb96c; BORDER-BOTTOM-COLOR: #ffb96c; BORDER-TOP-COLOR: #ffb96c; BORDER-RIGHT-COLOR: #ffb96c
}
.picOn .pb_02 {
	BACKGROUND: #ff8c00
}
.picOn .pb_04 {
	BACKGROUND: #ff8c00
}
.picOn .pb_05 {
	BACKGROUND: #ff8c00
}
.picOn .pb_07 {
	BACKGROUND: #ff8c00
}
.picOn .pb_01 {
	BACKGROUND: url(e_m_01.png) no-repeat 0px -275px
}
.picOn .pb_03 {
	BACKGROUND: url(e_m_01.png) no-repeat -25px -275px
}
.picOn .pb_06 {
	BACKGROUND: url(e_m_01.png) no-repeat -50px -275px
}
.picOn .pb_08 {
	BACKGROUND: url(e_m_01.png) no-repeat -75px -275px
}
#endSelect {
	BORDER-RIGHT: #8a8a8a 1px solid; BORDER-TOP: #8a8a8a 1px solid; DISPLAY: none; Z-INDEX: 20; LEFT: 359px; BORDER-LEFT: #8a8a8a 1px solid; WIDTH: 230px; BORDER-BOTTOM: #8a8a8a 1px solid; POSITION: absolute; TOP: 245px; HEIGHT: 71px
}

#endSelect .E_Cont {
	FONT-SIZE: 14px; LEFT: 26px; COLOR: #fff; POSITION: absolute; TOP: 10px
}
#endSelect .E_Cont P {
	PADDING-RIGHT: 0px; PADDING-LEFT: 0px; PADDING-BOTTOM: 4px; OVERFLOW: hidden; PADDING-TOP: 4px; ZOOM: 1
}
#rePlayBut {
	BACKGROUND: url(e_m_01.png) no-repeat -300px -900px; FLOAT: left; WIDTH: 69px; HEIGHT: 23px
}
#nextPicsBut {
	BACKGROUND: url(e_m_01.png) no-repeat -400px -900px; FLOAT: left; MARGIN-LEFT: 10px; WIDTH: 97px; HEIGHT: 23px
}
#endSelect #endSelClose {
	Z-INDEX: 2; RIGHT: 3px; BACKGROUND: url(e_m_01.png) no-repeat -300px -950px; OVERFLOW: hidden; WIDTH: 11px; CURSOR: pointer; POSITION: absolute; TOP: 3px; HEIGHT: 11px
}
.buttonBg {
	Z-INDEX: 2; BACKGROUND: url(e_m_01.png) no-repeat 100px 100px; WIDTH: 43px; POSITION: absolute; HEIGHT: 40px
}
.bBgS_1 {
	BACKGROUND-POSITION: -200px 0px
}
.bBgS_2 {
	BACKGROUND-POSITION: -300px 0px
}
.bBgS_3 {
	BACKGROUND-POSITION: -400px 0px
}
.buttonBg1 {
	Z-INDEX: 2; BACKGROUND: url(e_m_01.png) no-repeat 100px 100px; WIDTH: 55px; POSITION: absolute; HEIGHT: 40px
}
.bBg1S_1 {
	BACKGROUND-POSITION: -300px -200px
}
.bBg1S_2 {
	BACKGROUND-POSITION: -400px -200px
}
.bBg1S_3 {
	BACKGROUND-POSITION: -300px -250px
}
#d_BigPic IMG {
	FILTER:blendTrans(Duration=0.4)
}
#ePicList {
	DISPLAY: none; MARGIN-LEFT: -10px; OVERFLOW: hidden; ZOOM: 1
}
.picBox {
	DISPLAY: inline; BACKGROUND: url(e_m_01.png) no-repeat 0px -600px; FLOAT: left; MARGIN: 0px 0px 10px 10px; OVERFLOW: hidden; WIDTH: 230px; CURSOR: pointer; HEIGHT: 182px; TEXT-ALIGN: center
}
.picBox TD {
	PADDING-RIGHT: 2px; PADDING-LEFT: 2px; PADDING-BOTTOM: 0px; WIDTH: 226px; PADDING-TOP: 2px; HEIGHT: 180px
}
.picBox TD IMG {
	BORDER-RIGHT: #b4b4b4 1px solid; BORDER-TOP: #b4b4b4 1px solid; BORDER-LEFT: #b4b4b4 1px solid; BORDER-BOTTOM: #b4b4b4 1px solid
}
.picBox H3 {
	PADDING-RIGHT: 4px; PADDING-LEFT: 4px; FONT-WEIGHT: normal; FONT-SIZE: 12px; MIN-HEIGHT: 20px; PADDING-BOTTOM: 0px; COLOR: #858585; LINE-HEIGHT: 14px; PADDING-TOP: 4px; HEIGHT: 20px
}
.picBox .time {
	PADDING-RIGHT: 0px; BORDER-TOP: #e6e6e6 1px solid; PADDING-LEFT: 0px; FONT-SIZE: 11px; PADDING-BOTTOM: 0px; MARGIN: 0px auto; WIDTH: 188px; COLOR: #b6b6b6; LINE-HEIGHT: 20px; PADDING-TOP: 2px
}
.selected {
	BACKGROUND-POSITION: -250px -600px
}
.selected TD IMG {
	BORDER-LEFT-COLOR: #ff9845; BORDER-BOTTOM-COLOR: #ff9845; BORDER-TOP-COLOR: #ff9845; BORDER-RIGHT-COLOR: #ff9845
}
.selected H3 {
	COLOR: #ff8c46
}
.selected .time {
	BORDER-LEFT-COLOR: #ffe2cb; BORDER-BOTTOM-COLOR: #ffe2cb; COLOR: #ffad84; BORDER-TOP-COLOR: #ffe2cb; BORDER-RIGHT-COLOR: #ffe2cb
}
.s_select_01 {
	Z-INDEX: 200; BACKGROUND: url(e_m_07.gif) no-repeat 0px 0px; CURSOR: default; LINE-HEIGHT: 20px; POSITION: absolute; HEIGHT: 20px; TEXT-ALIGN: left
}
.s_select_01 .ds_cont {
	COLOR: #9d9d9d
}
.s_select_01 .ds_title {
	PADDING-LEFT: 3px; FLOAT: left
}
.s_select_01 .ds_button {
	DISPLAY: inline; BACKGROUND: url(e_m_07.gif) no-repeat 100% -25px; FLOAT: right; WIDTH: 20px; HEIGHT: 20px
}
.s_select_01 .ds_list {
	BACKGROUND: #fff; LEFT: 0px; WIDTH: 100%; POSITION: absolute; TOP: 19px
}
.s_select_01 .dsl_cont {
	BORDER-RIGHT: #ccc 1px solid; BORDER-TOP: #ccc 1px solid; BORDER-LEFT: #ccc 1px solid; BORDER-BOTTOM: #ccc 1px solid
}
.s_select_01 .ds_list P {
	PADDING-LEFT: 2px; BACKGROUND: #fff; MARGIN: 1px; COLOR: #666; LINE-HEIGHT: 20px
}
.s_select_01 .ds_list P.selected {
	BACKGROUND: #e2e2ff; COLOR: #333
}
#commNumBox {
	DISPLAY: none
}
A.videoNewsLeft {
	PADDING-LEFT: 20px; BACKGROUND: url(unfcn_mj_01.png) no-repeat -19982px 50%
}


