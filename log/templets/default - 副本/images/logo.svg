<svg width="200" height="60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="30" cy="30" r="25" fill="url(#logoGrad)" opacity="0.1"/>
  
  <!-- 主图标 -->
  <path d="M15 20 L30 10 L45 20 L45 40 L30 50 L15 40 Z" fill="url(#logoGrad)" stroke="#fff" stroke-width="1"/>
  
  <!-- 内部装饰 -->
  <circle cx="30" cy="25" r="3" fill="#fff"/>
  <path d="M25 35 L35 35" stroke="#fff" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 文字部分 -->
  <text x="65" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#fff">小仙元码</text>
  <text x="65" y="45" font-family="Arial, sans-serif" font-size="12" fill="#00d4ff">精品资源分享平台</text>
</svg>
