<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset={dede:global.cfg_soft_lang/}" />
<title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
<meta name="keywords" content="{dede:field name='keywords'/}" />
<meta name="description" content="{dede:field name='description' function='html2text(@me)'/}" />
<link href="{dede:global.cfg_templets_skin/}/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url={dede:global.cfg_mobileurl/}/list.php?tid={dede:field.id/}">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="{dede:global.cfg_mobileurl/}/list.php?tid={dede:field.id/}";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
</head>
</head>
<body class="picboxlist">
{dede:include filename="head.htm"/}
<!-- /header -->
<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
			<strong>当前位置:</strong> {dede:field name='position'/}
		</div><!-- /place -->
		<div class="listbox">
			<ul class="e8">
           {dede:list pagesize='12'}
				<li>
					<a href="[field:arcurl/]" class="pic"><img src="[field:litpic/]" alt="[field:title function='html2text(@me)'/]"/></a>
					<a href="[field:arcurl/]" class="title">[field:title/]</a>
					<span class="date"><small>最后更新：</small>[field:pubdate function="GetDateMK(@me)"/]</span>
				</li>
          {/dede:list}
			</ul>
		</div><!-- /listbox -->
		<div class="dede_pages">
			<ul class="pagelist">
				{dede:pagelist listitem="info,index,end,pre,next,pageno" listsize="5"/}
			</ul>
		</div><!-- /pages -->
	</div><!-- /pleft -->
	
	<div class="pright">
        <div>
          <dl class="tbox">
				<dt><strong>栏目列表</strong></dt>
				<dd>
					<ul class="d6">
                      {dede:channel type='son' currentstyle="<li><a href='~typelink~' class='thisclass'>~typename~</a></li>"}
		<li><a href='[field:typeurl/]'>[field:typename/]</a></li>{/dede:channel}
					</ul>
				</dd>
			</dl>
        </div>
    	<div class="">
			<dl class="tbox">
				<dt><strong>热点图集</strong></dt>
				<dd>
					<ul class="e3">
                    {dede:arclist row='5' type='image.' orderby=click}
						<li>
							<a href="[field:arcurl/]" class="preview"><img src="[field:litpic/]" alt="[field:title function='html2text(@me)'/]"/></a>
							<a href="[field:arcurl/]" class="title">[field:title/]</a>
							<span class="intro">更新:[field:pubdate function="GetDateMK(@me)"/]</span>
						</li>
                     {/dede:arclist}

					</ul>
				</dd>
			</dl>
		</div>
		<div class="mt1">
			<dl class="tbox">
				<dt><strong>推荐图集</strong></dt>
				<dd>
					<ul class="e9">
                    {dede:arclist row='8' type='image.commend.'}
						<li><a href="[field:arcurl/]"><img src="[field:litpic/]" alt="[field:title function='html2text(@me)'/]"/><span class="title">[field:title/]</span></a></li>
                    {/dede:arclist}
					</ul>
				</dd>
			</dl>
		</div>
	</div><!-- /pright -->
</div>
{dede:include filename="footer.htm"/}
<!-- /footer -->

</body>
</html>
