<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset={dede:global.cfg_soft_lang/}" />
<title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
<meta name="keywords" content="{dede:field.keywords/}" />
<meta name="description" content="{dede:field.description function='html2text(@me)'/}" />
<meta http-equiv="mobile-agent" content="format=xhtml;url={dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="{dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<link href="{dede:global.cfg_templets_skin/}/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<script language="javascript" type="text/javascript">
<!--
function checkSubmit(){
	if(document.feedback.msg.value!='') document.feedback.submit();
	else alert("评论内容不能为空！");
}
-->
</script>
</head>
<body class="articleview">
{dede:include filename="head2.htm"/}
<!-- /header -->
<div class="channel-nav w960 center clear">
 <div class='sonnav'> {dede:channel type='self' currentstyle="<span><a href='~typelink~' class='thisclass'>~typename~</a></span>"} <span><a href='[field:typeurl/]'>[field:typename/]</a></span>{/dede:channel} </div>
</div>
<!-- /channel-nav -->
<div class="w960 center clear mt1">
 <div class="pleft">
  <div class="place"> <strong>当前位置:</strong> {dede:field name='position'/} </div>
  <!-- /place -->
  <div class="viewbox">
   <div class="title">
    <h2>{dede:field.title/}</h2>
   </div>
   <!-- /title -->
   <div class="info"> <small>时间:</small>{dede:field.pubdate function="MyDate('Y-m-d H:i',@me)"/}<small>来源:</small>{dede:field.source/} <small>作者:</small>{dede:field.writer/} <small>点击:</small>
    <script src="{dede:field name='phpurl'/}/count.php?view=yes&aid={dede:field name='id'/}&mid={dede:field name='mid'/}" type='text/javascript' language="javascript"></script>
    次</div>
   <!-- /info -->
   {dede:field.description runphp='yes'}
   if(@me<>'' )@me = '
   <div class="intro">'.@me.'</div>
   ';
   {/dede:field.description}
   <div class="content">
    <div id="contentMidPicAD" style="float:right; clear:both; top:0; vertical-align:top;">{dede:myad name='contentMidPicAD'/}</div>
    {dede:field.body/} </div>
   <!-- /content -->
   <div class="dede_pages">
    <ul class="pagelist">
     {dede:pagebreak/}
    </ul>
   </div>
   <!-- /pages -->
   <iframe src="{dede:global.cfg_phpurl/}/digg_frame.php?id={dede:field.id/}" width="98%" height="100" style="overflow:hidden;" frameborder="0"></iframe>
   <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
   <div class="handle">
    <div class="context">
     <ul>
      <li>{dede:prenext get='pre'/}</li>
      <li>{dede:prenext get='next'/}</li>
     </ul>
    </div>
    <!-- /context -->
    <div class="actbox">
     <ul>
      <li id="act-fav"><a href="{dede:field name='phpurl'/}/stow.php?aid={dede:field.id/}" target="_blank">收藏</a></li>
      <li id="act-err"><a href="{dede:field name='phpurl'/}/erraddsave.php?aid={dede:field.id/}&title={dede:field.title/}" target="_blank">挑错</a></li>
      <li id="act-pus"><a href="{dede:field name='phpurl'/}/recommend.php?aid={dede:field.id/}" target="_blank">推荐</a></li>
      <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
     </ul>
    </div>
    <!-- /actbox -->
   </div>
   <!-- /handle -->
  </div>
  <!-- /viewbox -->
  <iframe id="frame_content" name="frame_content" src="{dede:field name='phpurl'/}/comments_frame.php?id={dede:field.id/}"
 width="100%" height="0" scrolling="auto" onload="resizeFrame()" frameborder="0"></iframe>
  <script language='javascript'>
var h = -1;
var frameName = 'frame_content';
var _frame = document.getElementById(frameName);
function resizeFrame()
{
	if(document.all) {
		_frame.height = _frame.document.body.scrollHeight;
	}
	else {
		t = setTimeout(timeCall, 20);
	}
}
function timeCall()
{
	if(h > -1)
	{
    _frame.style.height = h + 'px';
    clearTimeout(t);
	}
	else
	{
	  try{
      h = window.frames[frameName].frames['xclient'].location.hash.substring(1);
    }catch(e){}
	  setTimeout(timeCall, 20);
	}
}
</script>
 </div>
 <!-- /pleft -->
 <div class="pright">
  <div class="infos_userinfo"> {dede:memberinfos}
   <dl class="tbox">
    <dt><strong>发布者资料</strong></dt>
    <dd> <a href="[field:spaceurl /]" class="userface"><img src="[field:face/]" width="52" height="52" /></a> <a href='[field:spaceurl /]' class="username">[field:uname/]</a> <span class="useract"> <a href="[field:spaceurl /]" class="useract-vi">查看详细资料</a> <a href="[field:spaceurl /]&action=guestbook" class="useract-pm">发送留言</a> <a href="[field:spaceurl /]&action=newfriend" class="useract-af">加为好友</a> </span> <span class="userinfo-sp"><small>用户等级:</small>[field:rankname /]</span> <span class="userinfo-sp"><small>注册时间:</small>[field:jointime function="MyDate('Y-m-d H:m',@me)"/]</span> <span class="userinfo-sp"><small>最后登录:</small>[field:logintime function="MyDate('Y-m-d H:m',@me)"/]</span> </dd>
   </dl>
   {/dede:memberinfos} </div>
  <div class="commend mt1">
   <dl class="tbox">
    <dt><strong>推荐内容</strong></dt>
    <dd>
     <ul class="d4">
      {dede:arclist flag='c' titlelen=42 row=6}
      <li><a href="[field:arcurl/]">[field:title/]</a>
       <p>[field:description function='cn_substr(@me,80)'/]...</p>
      </li>
      {/dede:arclist}
     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox">
    <dt><strong>热点内容</strong></dt>
    <dd>
     <ul class="c1 ico2">
      {dede:arclist row=10 orderby=click}
      <li><a href="[field:arcurl/]">[field:title/]</a></li>
      {/dede:arclist}
     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>
{dede:include filename="footer.htm"/}
<!-- /footer -->
</body>
</html>
