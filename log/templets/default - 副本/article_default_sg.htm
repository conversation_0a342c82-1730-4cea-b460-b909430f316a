<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset={dede:global.cfg_soft_lang/}" />
<title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
<meta name="keywords" content="{dede:field.keywords/}" />
<meta name="description" content="{dede:field.description function='html2text(@me)'/}" />
<link href="{dede:global.cfg_templets_skin/}/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url={dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="{dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="{dede:global.cfg_cmsurl/}/include/dedeajax2.js"></script>
<script language="javascript" type="text/javascript">
<!--

	function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("{dede:global.cfg_cmsurl/}/member/ajax_feedback.php");
	  DedeXHTTP = null;
	}
	function checkSubmit(){
		if(document.feedback.msg.value!='') document.feedback.submit();
		else alert("评论内容不能为空！");
	}
-->
</script>
</head>
<body class="infosview">
{dede:include filename="head2.htm"/}
<!-- /header -->
<div class="w960 center clear mt1">
 <div class="pleft">
  <div class="place"> <strong>当前位置:</strong> {dede:field name='position'/} </div>
  <!-- /place -->
  <div class="viewbox">
   <div class="title">
    <h2>{dede:field.title/}</h2>
   </div>
   <!-- /title -->
   <font color='red'> 你会看到这个提示，那是因为你的系统无法识别某栏目的模型信息，或者你新建模型后，没为这个模型设计单独的模板。不同模型的文档浏览页的模板为：article_模型名字标识.htm
   如“article_article.htm”，更多的信息你可以在频道模型管理的地方查看。 </font> {dede:fieldlist}
   <table width="100%" border="0" cellpadding="1" cellspacing="1" bgcolor="#BED1AB">
    <tr>
     <td width="23%" height="24" align="center" bgcolor="#FBFEF5"><b>[field:name/]</b> 内容： <br />
      模板调用标记： <br />
      <script language='javascript'>document.write("{"+"dede:field.[field:tagname/] /"+"}");</script></td>
     <td width="77%" bgcolor="#FFFFFF"> [field:value/] </td>
    </tr>
   </table>
   {/dede:fieldlist}
   <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
   <div class="handle">
    <div class="actbox">
     <ul>
      <li id="act-fav"><a href="{dede:field name='phpurl'/}/stow.php?aid={dede:field.id/}">收藏</a></li>
      <li id="act-err"><a href="{dede:field name='phpurl'/}/erraddsave.php?aid={dede:field.id/}&title={dede:field.title/}">挑错</a></li>
      <li id="act-pus"><a href="{dede:field name='phpurl'/}/recommend.php?aid={dede:field.id/}">推荐</a></li>
      <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
     </ul>
    </div>
    <!-- /actbox -->
   </div>
   <!-- /handle -->
  </div>
  <!-- /viewbox -->
  <div class="mt1">
   <dl class="tbox">
    <dt> <strong>最新评论</strong> <span class="more"><a href="{dede:field name='phpurl'/}/feedback.php?aid={dede:field.id/}">查看所有评论</a></span> </dt>
    <dd>
     <div class="dede_comment">
      <script language='javascript' type='text/javascript' src="{dede:field name='phpurl'/}/feedback_js.php?aid={dede:field name='id'/}"></script>
     </div>
     <!-- /dede_comment -->
    </dd>
   </dl>
  </div>
  <!-- /comment -->
  <div class="mt1">
   <dl class="tbox">
    <dt> <strong>发表评论</strong> <span class="more"><a href="{dede:field name='phpurl'/}/feedback.php?aid={dede:field.id/}">查看所有评论</a></span> </dt>
    <dd>
     <div class="dede_comment_post">
      <form action="{dede:field name='phpurl'/}/feedback.php" method="post" name="feedback">
       <input type="hidden" name="action" value="send" />
       <input type="hidden" name="comtype" value="comments">
       <input type="hidden" name="aid" value="{dede:field name="id"/}" />
       <input type="hidden" name="isconfirm" value="yes" />
       <div class="dcmp-title"> <small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small> </div>
       <!-- /dcmp-title -->
       <div class="dcmp-stand"> <strong>评价:</strong>
        <input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" />
        <label for="dcmp-stand-neu"><img src="{dede:global.cfg_templeturl/}/images/cmt-neu.gif" />中立</label>
        <input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" />
        <label for="dcmp-stand-good"><img src="{dede:global.cfg_templeturl/}/images/cmt-good.gif" />好评</label>
        <input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" />
        <label for="dcmp-stand-bad"><img src="{dede:global.cfg_templeturl/}/images/cmt-bad.gif" />差评</label>
       </div>
       <!-- /dcmp-stand -->
       <div class="dcmp-content">
        <textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
       </div>
       <!-- /dcmp-content -->
       <div class="dcmp-mood"> <strong>表情:</strong>
        <ul>
         <li>
          <input type="radio" name='face' value='6' checked="1" />
          <img src="{dede:global.cfg_templeturl/}/images/mood/ico-mood-6.gif" /></li>
         <li>
          <input type="radio" name='face' value='4'/>
          <img src="{dede:global.cfg_templeturl/}/images/mood/ico-mood-4.gif" /></li>
         <li>
          <input type="radio" name='face' value='3'/>
          <img src="{dede:global.cfg_templeturl/}/images/mood/ico-mood-3.gif" /></li>
         <li>
          <input type="radio" name='face' value='5'/>
          <img src="{dede:global.cfg_templeturl/}/images/mood/ico-mood-5.gif" /></li>
         <li>
          <input type="radio" name='face' value='2'/>
          <img src="{dede:global.cfg_templeturl/}/images/mood/ico-mood-2.gif" /></li>
         <li>
          <input type="radio" name='face' value='1'/>
          <img src="{dede:global.cfg_templeturl/}/images/mood/ico-mood-1.gif" /></li>
         <li>
          <input type="radio" name='face' value='7'/>
          <img src="{dede:global.cfg_templeturl/}/images/mood/ico-mood-7.gif" /></li>
        </ul>
       </div>
       <!-- /dcmp-mood -->
       <div class="dcmp-post">
        <!--未登录-->
        <div class="dcmp-userinfo" id="_ajax_feedback"> 用户名:
         <input type="text" name="username" size="16" class="ipt-txt" />
         密码:
         <input name="pwd" type="password" size="16" class="ipt-txt" />
         验证码:
         <input type="text" name="validate" size="4" class="ipt-txt" style="text-transform: uppercase;"/>
         <img src="{dede:global.cfg_cmsurl/}/include/vdimgck.php" />
         <input type="checkbox" name="notuser" id="dcmp-submit-guest" />
         <label for="dcmp-submit-guest"> 匿名? </label>
        </div>
        <script language="javascript" type="text/javascript">CheckLogin();</script>
        <div class="dcmp-submit">
         <button type="button" onClick='checkSubmit()'>发表评论</button>
        </div>
       </div>
      </form>
     </div>
     <!-- /dede_comment_post -->
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pleft -->
 <div class="pright">
  <div class="infos_userinfo"> {dede:memberinfos}
   <dl class="tbox">
    <dt><strong>发布者资料</strong></dt>
    <dd> <a href="[field:spaceurl /]" class="userface"><img src="[field:face/]" width="52" height="52" /></a> <a href='[field:spaceurl /]' class="username">[field:uname/]</a> <span class="useract"> <a href="[field:spaceurl /]" class="useract-vi">查看详细资料</a> <a href="[field:spaceurl /]&action=guestbook" class="useract-pm">发送留言</a> <a href="[field:spaceurl /]&action=newfriend" class="useract-af">加为好友</a> </span> <span class="userinfo-sp"><small>用户等级:</small>[field:rankname /]</span> <span class="userinfo-sp"><small>注册时间:</small>[field:jointime function="MyDate('Y-m-d H:m',@me)"/]</span> <span class="userinfo-sp"><small>最后登录:</small>[field:logintime function="MyDate('Y-m-d H:m',@me)"/]</span> </dd>
   </dl>
   {/dede:memberinfos} </div>
  <div class="hot mt1">
   <dl class="tbox">
    <dt><strong>最新信息</strong></dt>
    <dd>
     <ul class="c1 ico2">
      {dede:arclistsg orderby='id' titlelen='60' row='20'}
      <li><a href="[field:arcurl/]">[field:title/]</a></li>
      {/dede:arclistsg}
     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>
{dede:include filename="footer.htm"/}
<!-- /footer -->
</body>
</html>
