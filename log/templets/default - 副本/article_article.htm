<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset={dede:global.cfg_soft_lang/}" />
<title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
<meta name="keywords" content="{dede:field.keywords/}" />
<meta name="description" content="{dede:field.description function='html2text(@me)'/}" />
<link href="{dede:global.cfg_templets_skin/}/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url={dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="{dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="{dede:global.cfg_cmsurl/}/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("{dede:global.cfg_cmsurl/}/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("{dede:field name='phpurl'/}/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "{dede:global.cfg_phpurl/}/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("{dede:global.cfg_phpurl/}/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
{dede:include filename="head2.htm"/}
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> {dede:field name='position'/} </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>{dede:field.title/}</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>{dede:field.pubdate function="MyDate('Y-m-d H:i',@me)"/}<small>来源:</small>{dede:field.source/} <small>作者:</small>{dede:field.writer/} <small>点击:</small>
   <script src="{dede:field name='phpurl'/}/count.php?view=yes&aid={dede:field name='id'/}&mid={dede:field name='mid'/}" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  {dede:field.description runphp='yes'}
  if(@me<>'' )@me = '
  <div class="intro">'.@me.'</div>
  ';
  {/dede:field.description}
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      {dede:field.body/}
      {dede:field.vote/}
      (责任编辑：{dede:adminname/})</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    {dede:pagebreak/}
   </ul>
  </div>

<center>{dede:qrcode/}</center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',{dede:field.id/})">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">({dede:field.goodpost/})</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:{dede:field.goodper/}%"></span></div>
     <div class="digg_percent_num">{dede:field.goodper/}%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',{dede:field.id/})">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">({dede:field.badpost/})</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:{dede:field.badper/}%"></span></div>
     <div class="digg_percent_num">{dede:field.badper/}%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg({dede:field.id/});</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  {dede:bshare/}
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>{dede:prenext get='pre'/}</li>
     <li>{dede:prenext get='next'/}</li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="{dede:field name='phpurl'/}/stow.php?aid={dede:field.id/}" target="_blank">收藏</a></li>
     <li id="act-err"><a href="{dede:field name='phpurl'/}/erraddsave.php?aid={dede:field.id/}&title={dede:field.title/}" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="{dede:field name='phpurl'/}/recommend.php?aid={dede:field.id/}" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 {dede:include file='ajaxfeedback.htm' /} </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> {dede:memberinfos}
 <div class="infos_userinfo">
  <dl class="tbox light">
   <dt class='light'><strong>发布者资料</strong></dt>
   <dd class='light'> <a href="[field:spaceurl /]" class="userface"><img src="[field:face/]" width="52" height="52" /></a> <a href='[field:spaceurl /]' class="username">[field:uname/]</a> <span class="useract"> <a href="[field:spaceurl /]" class="useract-vi">查看详细资料</a> <a href="[field:spaceurl /]&action=guestbook" class="useract-pm">发送留言</a> <a href="[field:spaceurl /]&action=newfriend" class="useract-af">加为好友</a> </span> <span class="userinfo-sp"><small>用户等级:</small>[field:rankname /]</span> <span class="userinfo-sp"><small>注册时间:</small>[field:jointime function="MyDate('Y-m-d H:m',@me)"/]</span> <span class="userinfo-sp"><small>最后登录:</small>[field:logintime function="MyDate('Y-m-d H:m',@me)"/]</span> </dd>
  </dl>
 </div>
 {/dede:memberinfos}
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      {dede:channel type='son' currentstyle="
      <li><a href='~typelink~' class='thisclass'>~typename~</a></li>
      "}
      <li><a href='[field:typeurl/]'>[field:typename/]</a></li>
      {/dede:channel}
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      {dede:arclist flag='c' titlelen=42 row=6}
      <li><a href="[field:arcurl/]">[field:title/]</a>
       <p>[field:description function='cn_substr(@me,80)'/]...</p>
      </li>
      {/dede:arclist}
     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      {dede:arclist row=10 orderby=click}
      <li><a href="[field:arcurl/]">[field:title/]</a></li>
      {/dede:arclist}
     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>
{dede:include filename="footer.htm"/}
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
