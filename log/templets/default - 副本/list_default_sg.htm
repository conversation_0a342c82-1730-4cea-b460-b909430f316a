<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset={dede:global.cfg_soft_lang/}" />
<title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
<meta name="keywords" content="{dede:field name='keywords'/}" />
<meta name="description" content="{dede:field name='description' function='html2text(@me)'/}" />
<link href="{dede:global.cfg_templets_skin/}/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url={dede:global.cfg_mobileurl/}/list.php?tid={dede:field.id/}">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="{dede:global.cfg_mobileurl/}/list.php?tid={dede:field.id/}";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
</head>
<body class="infoslist">

{dede:include filename="head.htm"/}
<!-- /header -->

<div class="channel-nav w960 center clear">
	<div class='sonnav'>
	{dede:channel currentstyle="<span><a href='~typelink~' class='thisclass'>~typename~</a></span>"}
		<span><a href='[field:typeurl/]'>[field:typename/]</a></span>{/dede:channel}
	</div>
</div><!-- /channel-nav -->

<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
			<strong>当前位置:</strong> {dede:field name='position'/}
		</div><!-- /place -->
		<div class="listbox">
			<ul class="d5">            
            {dede:list pagesize='20'}
            <li><a href="[field:arcurl/]" class="title">[field:title/]</a><span class="date">[field:pubdate function="GetDateTimeMK(@me)"/]</span></li>
            {/dede:list}
			</ul>
		</div><!-- /listbox -->
		<div class="dede_pages">
			<ul class="pagelist">
				 {dede:pagelist listitem="info,index,end,pre,next,pageno" listsize="5"/}
			</ul>
		</div><!-- /pages -->
	</div><!-- /pleft -->
	
	<div class="pright">
		<div class="strongrange  mt1">
			<dl class="tbox">
				<dt><strong>最新信息</strong></dt>
				<dd>
					<ul class="d1 ico2">
                     {dede:arclistsg orderby='id' titlelen='60' row='10'}
                        <li><span>[field:typename/]</span><a href="[field:arcurl/]">[field:title/]</a></li>
                    {/dede:arclistsg}
					</ul>
				</dd>
			</dl>
		</div><!-- /strongrange -->
        <div class="strongrange  mt1">
			<dl class="tbox">
				<dt><strong>热门信息</strong></dt>
				<dd>
					<ul class="d1 ico2">
                     {dede:arclistsg sort='click' titlelen='30' row='10'}
                    	<li><span>[field:typename/]</span><a href="[field:arcurl/]">[field:title/]</a></li>
                    {/dede:arclistsg}
					</ul>
				</dd>
			</dl>
		</div><!-- /strongrange -->
		{dede:qrcode/}
	</div><!-- /pright -->
	
</div>
{dede:include filename="footer.htm"/}
<!-- /footer -->

</body>
</html>
