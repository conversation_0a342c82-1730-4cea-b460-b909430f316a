<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset={dede:global.cfg_soft_lang/}" />
<title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
<meta name="keywords" content="{dede:field.keywords/}" />
<meta name="description" content="{dede:field.description function='html2text(@me)'/}" />
<link href="{dede:global.cfg_templets_skin/}/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url={dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="{dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
</head>
<body>
{dede:include filename="head2.htm"/}
<!-- /header -->
<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
		<strong>当前位置:</strong> <a href='{dede:global name='cfg_cmsurl' /}'>主页</a> &gt; 专题
		</div><!-- /place -->
		<div class="viewbox">
			<div class="title">
				<h2>{dede:field name="title"/}</h2>
			</div><!-- /title -->
			<div class="info">
				<small>时间:</small>{dede:field.pubdate function="MyDate('Y-m-d H:i',@me)"/}<small>责任编辑:</small>{dede:field.writer/} <small>点击:</small><script src="{dede:field name='phpurl'/}/count.php?view=yes&aid={dede:field name='id'/}&mid={dede:field name='mid'/}" type='text/javascript' language="javascript"></script>次 
			</div><!-- /info -->
			<div class="specialpic">
				<img src="{dede:field name='litpic'/}" />
			</div>
			<div class="content">
			　{dede:field name='description'/}
			</div><!-- /content -->
			<div class="boxoff">
				<strong>------分隔线----------------------------</strong>
			</div>
		</div><!-- /viewbox -->
		
		<div class="speciallist">
        {dede:field.note/}
		</div>
			
<!-- //AJAX评论区 -->
<script language="javascript" type="text/javascript" src="{dede:global.cfg_cmsurl/}/include/dedeajax2.js"></script>
<script language="javascript" type="text/javascript">
function CheckLogin()
{
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("{dede:global.cfg_cmsurl/}/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
</script>
{dede:include file='ajaxfeedback.htm' /}
		
	</div><!-- /pleft -->
	
	<div class="pright">
		<div class="commend">
			<dl class="tbox">
				<dt><strong>推荐专题</strong></dt>
				<dd>
					<ul class="d4">
           {dede:arclist flag='c' channelid='-1' titlelen=42 row=6}
          	<li><a href="[field:arcurl/]">[field:title/]</a>
            	<p>[field:description function='cn_substr(@me,80)'/]...</p>
            </li>{/dede:arclist}
					</ul>
				</dd>
			</dl>
		</div><!-- /commend -->
		<div class="hot mt1">
			<dl class="tbox">
				<dt><strong>热点内容</strong></dt>
				<dd>
					<ul class="c1 ico2">
                    {dede:arclist row=10 orderby=click}
                    	<li><a href="[field:arcurl/]">[field:title/]</a></li>
                    {/dede:arclist}
					</ul>
				</dd>
			</dl>
		</div>
	</div><!-- /pright -->
</div>
{dede:include filename="footer.htm"/}
<!-- /footer -->


</body>
</html>
