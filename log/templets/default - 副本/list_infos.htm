<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset={dede:global.cfg_soft_lang/}" />
<title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
<meta name="keywords" content="{dede:field name='keywords'/}" />
<meta name="description" content="{dede:field name='description' function='html2text(@me)'/}" />
<link href="{dede:global.cfg_templets_skin/}/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url={dede:global.cfg_mobileurl/}/list.php?tid={dede:field.id/}">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="{dede:global.cfg_mobileurl/}/list.php?tid={dede:field.id/}";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
</head>
<body class="infoslist">
{dede:include filename="head.htm"/}
<!-- /header -->

<div class="w960 center clear mt1">
	<div class="pleft" style="width: 655px;">
		
<div class="place">
			<strong>当前位置:</strong> {dede:field name='position'/}
</div>
<!-- end place -->

{dede:infolink}
<table id='dedeinfolink'>
<tr>
	<td colspan='3' class='iftitle'>
		信息附加条件：
	</td>
</tr>
<tr>
	<td width='50'>&nbsp;地 区：</td>
	<td align='center' nowrap='yes'>
		<div>[field:linkallplace/]</div>
	</td>
	<td>
  	[field:nativeplace /]&nbsp;
	</td>
</tr>
<tr><td colspan='3' class='spline'>&nbsp;</td></tr>
<tr>
	<td>&nbsp;类 型：</td>
  <td align='center' nowrap='yes'>
  	<div>[field:linkalltype/]</div>
  </td>
  <td>
  	[field:infotype /]&nbsp;
  </td>
</tr>
<tr><td colspan='3' class='spline'>&nbsp;</td></tr>
</table>
{/dede:infolink}		
		
	<div class="listbox">
			<ul class="d5">            
            {dede:list pagesize='20'}
            <li><a href="[field:arcurl/]" class="title">[field:title/]</a><span class="date">[field:pubdate function="GetDateTimeMK(@me)"/]</span></li>
            {/dede:list}
			</ul>
	</div>
	<!-- end listbox -->
		
		<div class="dede_pages">
			<ul class="pagelist">
				 {dede:pagelist listitem="info,index,end,pre,next,pageno" listsize="5"/}
			</ul>
		</div>
		<!-- end pages -->
	</div>
	<!-- end pleft -->
	
	<div class="pright" style="width: 295px;">
        <div>
          <dl class="tbox">
				<dt><strong>栏目列表</strong></dt>
				<dd>
					<ul class="d6">
                      {dede:channel type='son' currentstyle="<li><a href='~typelink~' class='thisclass'>~typename~</a></li>"}
		<li><a href='[field:typeurl/]'>[field:typename/]</a></li>{/dede:channel}
					</ul>
				</dd>
			</dl>
        </div>
    	<div class="infos_search">
			<dl class="tbox">
				<dt><strong>信息搜索</strong></dt>
				<dd>
                {dede:infoguide}
                    <form name='infoguide' method='get' action='/plus/list.php'>
                    [field:nativeplace /]
                    [field:infotype /]
                    <input type='hidden' name='tid' value='[field:typeid /]' />
                    <input type='hidden' name='channelid' value='-8' />
                    <span class='infosearchtxt'>关键字：</span><span><input type='text' name='keyword' value='' class="ipt-txt" /></span>
                    <input type='submit' value='搜索信息' class="btn-2" style='cursor:pointer' />
                    &nbsp;
                    <input type='button' value='发布信息' class="btn-2" 
                    onclick="location='/member/archives_sg_add.php?channelid=-8';" style='cursor:pointer' />
                    </form>
                {/dede:infoguide}
				</dd>
			</dl>
		</div>
        
		<div class="strongrange  mt1">
			<dl class="tbox">
				<dt><strong>最新信息</strong></dt>
				<dd>
					<ul class="d1 ico2">
                     {dede:arclistsg orderby='id' titlelen='60' row='10'}
                        <li><span>[field:typename/]</span><a href="[field:arcurl/]">[field:title/]</a></li>
                    {/dede:arclistsg}
					</ul>
				</dd>
			</dl>
		</div><!-- /strongrange -->
		<div class="strongrange  mt1">
		 <dl class="tbox">
				<dt><strong>热门信息</strong></dt>
				<dd>
					<ul class="d1 ico2">
                     {dede:arclistsg sort='click' titlelen='30' row='10'}
                    	<li><span>[field:typename/]</span><a href="[field:arcurl/]">[field:title/]</a></li>
                    {/dede:arclistsg}
					</ul>
				</dd>
			</dl>
		</div><!-- /strongrange -->

	</div><!-- /pright -->
	
</div>
{dede:include filename="footer.htm"/}
<!-- /footer -->

</body>
</html>
