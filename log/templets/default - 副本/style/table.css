/***
 *	DedeCMS table Style (gray)
 *	dedecms.com  Author pigz  2008-11-26 11:45
 **/

/*------------------------表格------------------------*/
.red{
	color:#F00;
	margin-right:5px;
	font-family:'思源黑体','宋体';
	}
.dede_table{
	width:100%;
	margin:0px auto 7px;
	font-size:12px;
	overflow:hidden;
	border:1px solid #EEE;
/*	border-collapse:collapse;*/
}
.dede_table tbody * a{
	color:#0066CC;
}
.dede_table * a:hover{
	color:#FF0000;
	text-decoration:underline;
}
/*表格标题*/
.dede_table caption{
	text-align:left;
	height:24px;
	line-height:24px;
	font-size:14px;
	text-indent:6px;
	color:#000;
	letter-spacing:2px;
}
/*Opera Hack @media all and (min-width: 0px){.uctable caption{min-height:25px;}}*/
/*表格头*/
.dede_table thead tr td{
	height:23px;
	line-height:23px;
	text-indent:12px;
	border-top:1px solid #FFF;
	border-bottom:1px solid #FFF;
	background:url(images/table_tbg.gif) 1px 1px repeat-x;
	background:#F8F8F8;
	font-weight:bold;
	letter-spacing:2px;
	color:#999;
}

.dede_table thead tr td strong{/*表格头:标题*/
	letter-spacing:1px;
	font-size:12px;
	color:#333;
}
.dede_table thead tr td span{/*表格头:介绍*/
	color:#999;
}
.dede_table thead tr td  p{/*表格头:链接区域*/
	height:22px;
	display:inline;
	float:right;
	margin:-24px 10px 0 0;
	overflow:hidden;
	line-height:21px;
}
.dede_table thead tr td  p *{/*表格头:全部向右浮动*/
	float:right;
}
.dede_table thead tr td a.thlink{/*表格头:链接样式*/
	text-decoration:underline;
}
.dede_table thead tr td a.thlink:hover{
	text-decoration:none;
}

.dede_table thead tr th{/*表格内容:分类*/
	height:25px;
	font-weight:normal;
	text-indent:10px;
	text-align:left;
	line-height:15px;
}
/*表格内容*/
.dede_table tbody {
	overflow:hidden;
	text-align:left;
}
.dede_table tbody tr th{/*表格内容:分类*/
	background:#FFE;
	padding-top:3px;
	line-height:15px;
	text-indent:10px;
	letter-spacing:1px;
	font-weight:normal;
	border-top:1px solid #EEE;
	border-bottom:1px solid #DBDFE1;
	padding-top:4px;
	padding-bottom:4px;
}
.dede_table tbody tr th .toggle{
	width:15px;
	height:13px;
	border:none;
	cursor:pointer;
	vertical-align:middle;
}
.dede_table tbody tr th small{
	font-size:12px;
	color:#999;
	margin-left:4px;
	margin-right:4px;
}
.dede_table tbody tr td{/*表格内容:列表*/
	padding:8px;
	color:#333;
	vertical-align:top;
	border-bottom:1px solid #F0F0F0;
}
.dede_table tbody tr td.td1{
	background:#FEFEFE;
	text-align:right;
	color:#787;
	vertical-align:middle;
}
.dede_table tbody tr td.tdbig{
	font-size:14px;
	vertical-align:middle;
	text-align:center;
}
.dede_table tbody tr td p{/*表格内容:列表:段落*/
	line-height:21px;
	padding:2px;
}
.dede_table tbody tr td p strong img{/*表格内容:列表:标题图标垂直居中对齐*/
	vertical-align:middle;
}
.dede_table tbody tr td img{ /*表格内容:列表:常规图片垂直顶部对齐*/
	vertical-align:top;
	margin:0px 10px 5px 0px;
}
.dede_table tbody tr td .middle{
	vertical-align:middle;
}
.dede_table tbody tr td small{ /*表格内容:列表:注释性文字*/
	color:#888;
	font-size:12px;
	overflow:hidden;
}
.dede_table tbody tr td big{ /*表格内容:列表:强调性文字*/
	font-size:14px;
	overflow:hidden;
	letter-spacing:1px;
}

/*表格底*/
.dede_table tfoot{
	background:url(images/table_fbg.jpg) left bottom repeat-x;
}
.dede_table tfoot tr td{
	padding:10px;
	line-height:25px;
	text-align:center;
}
.dede_table tfoot * a{
    color:#666;
}
.dede_table tfoot tr td p{
	line-height:21px;
	margin-bottom:10px;
}

/*其他*/
.trlist tbody tr td{/*列表类型的表格使行居中对齐*/
	vertical-align:middle;
}
/*选项卡*/
ul.label{
	height:25px;
	background:#EEF5E0;
	border-bottom:1px solid #DDD;
	text-indent:0px;
	letter-spacing:1px;
}
ul.label li{
	height:25px;
	line-height:25px;
    float:left;
	display:block;
	background:#F4F9EC;
	border-right:1px solid #D6D6D6;
}
ul.label li.this{
	background:#FFF;
	font-weight:bold;
	position:relative;
	bottom:-1px;
}
ul.label li.this a{
	color:#FF6600;
}
ul.label li a{
	float:left;
	padding-left:16px;
	padding-right:16px;
	display:block;
	color:#333;
}