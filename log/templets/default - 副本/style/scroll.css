.fLeft {
	float:left;
}
.mTB10 {
	margin-top: 10px;
	margin-bottom: 10px;
}
.overflow {
	overflow:hidden;
}

#bigpic {
	margin-top: 8px;
	margin-bottom: 25px;
}
.jCarouselLite button {
	border:none;
}
.jCarouselLite {
	margin-bottom: 25px;
}
.imgScroll {
	background: url(../images/img_scroll.gif) no-repeat;
	height: 100px;
	width: 14px;
	background-color: transparent;
}
.prev {
	background-position: left center;
}
.next {
	background-position: -14px center;
}
.artContent {
	clear:both;
	font-size:14px;
	line-height:23px;
	overflow:hidden;
	padding:9px 0;
	color:#2f2f2f;
}

#bigpic img {
	max-width: 560px;
	max-height:450px;
	width:expression(this.width>560 ? '560px' : true);
	width:expression(this.height>450 ? '450px' : true);
}
ul.w25 li {
	float: left;
	width: 25%;
}