/***
 *	DedeCMS v5.3 Style (grass green)
 *	dedecms.com  Author pigz  2008-11-17 17:35
 **/
 
/*---------- base ---------*/
*{
	padding:0px;
	margin:0px; 
}
html{
	background:#FFF;
}
body{
	margin:0px auto;
	font:12px <PERSON><PERSON><PERSON>,<PERSON><PERSON>,Ta<PERSON>a;
}
img{
	border:none;
}

a{
	color:#3366CC;
	text-decoration:none;
}
a:hover{
	color:#F33;
	text-decoration:underline;
}
ul{
	list-style:none;
}
input,select,button{
	font-size:12px;
	vertical-align:middle;
}
.fc_03c{
	color:#03C;
}
.fc_f30{
	color:#F30;
}

/*---------- frame ---------*/
.header{
	width:100%;
	margin:0px auto;
	padding-top:10px;
	clear:both;
	overflow:hidden;
}
.header .toplogo{
	width:300px;
	height:60px;
	float:left;
}
.header .toplogo h1{
	width:200px;
	height:56px;
}
.header .toplogo h1 a{
	width:200px;
	height:56px;
	display:block;
	font-size:0px;
	text-indent:-200px;
	overflow:hidden;
	background:url(../images/top-logo.gif) center center no-repeat;
}
.header .searchform{
	float:left;
	padding-top:12px;
}
.header .searchform .s1{
	float:left;
	height:29px;
	line-height:25px;
	font-size:14px;
	color:#333333;
	font-weight:bold;
	margin-right:6px;
}
.header .searchform .s2{
	float:left;
}
.header .searchform .s2 .sf-keyword{
	padding:3px 5px;
	height:14px;
	line-height:17px;
	overflow:hidden;
	color:#777;
}
.header .searchform .s2 .sf-option{
	font-size:14px;
	color:#444;
}
.header .searchform .s2 .sf-submit{
	width:62px;
	height:23px;
	font-size:14px;
	line-height:18px;
	color:#333;
	letter-spacing:5px;
	margin-left:5px;
	margin-top:-2px;
}
.header .searchform .s3{
	height:29px;
	float:left;
	margin-left:20px;
	line-height:29px;
}
.header .searchform .s3 a{
	margin-right:10px;
	font-size:14px;
}

.msgbar{
	height:27px;
	line-height:27px;
	border-top:1px solid #E4F0DF;
	border-bottom:1px solid #E4F0DF;
	font-size:14px;
	color:#666;
}
.msgbar p{
	padding-left:22px;
	background:#F3FAF1;
	border-top:1px solid #FFF;
	border-bottom:1px solid #FFF;
}

.resultlist{
	padding:2px 20px;
}
.resultlist h2{
	display:none;
}
.item h3 .title{
	font-size:14px;
	font-weight:normal;
	line-height:25px;
	text-decoration:underline;
}
.item h3 .title:hover{
	text-decoration:none;	
}
.item{
	margin-top:16px;
}
.item .intro{
	font-size:14px;
	line-height:19px;
	padding-left:2px;
	color:#777;
}
.item .info{
	font-size:12px;
	line-height:26px;
	color:#080;
}
.item .info small{
	font-size:12px;
}
.item .info span{
	margin-right:10px;
}
.item .info a{
	color:#444;
}
.advancedsearch{
	width:500px;
	float:left;
	padding:30px 40px;
}
.advancedsearch input,.advancedsearch select,.advancedsearch button{
	font-size:14px;
	margin-left:5px;
}
.advancedsearch input{
	font-size:12px;
	padding:3px;	
}
.advancedsearch .f1{
	width:500px;
	clear:both;
	overflow:hidden;
}
.advancedsearch .f1 small,.advancedsearch .f2 small{
	font-size:14px;
	width:80px;
	height:40px;
	float:left;
	text-align:right;
	line-height:25px;
	color:#666;
}
.advancedsearch .f1 span,.advancedsearch .f2 span{
	height:40px;
	float:left;
	font-size:14px;
}
.advancedsearch .f2{
	width:200px;
	float:left;
}
.othersearch{
	height:31px;
	overflow:hidden;
	clear:both;
	line-height:31px;
	padding-left:16px;
	margin-top:16px;
}
.othersearch h2{
	float:left;
	font-size:14px;
}
.othersearch ul{
	float:left;
}
.othersearch ul li{
	float:left;
	margin-left:10px;
	font-size:14px;
	line-height:33px;
}
.footer{
	color:#999;
	text-align:right;
	border-top:1px solid #E5EFD6;
	margin-top:16px;
	padding:8px 10px;
	clear:both;
	overflow:hidden;
}
.footer .link{
	text-align:center;
	padding:5px 0px;
	float:left;
}
.footer .link a{
	margin:0px 5px;
	color:#666666;
}
.footer .powered{
	font-size:10px;
	line-height:25px;
}
.footer .powered strong{
	color:#690;
}
.footer .powered strong span{
	color:#F93;
}
.footer .powered a:hover{
	text-decoration:none;	
}
.dede_pages{
	height:33px;
	clear:both;
	overflow:hidden;
	background:#FAFAFA;
	margin-top:16px;
}
.dede_pages ul{
	float:left;
	padding:6px 0px 0px 16px;
}
.dede_pages ul li{
	float:left;
	font-family:Tahoma;
	line-height:17px;
	margin-right:6px;
}
.dede_pages ul li a{
	float:left;
	padding:2px 4px 2px;
	color:#666;
}
.dede_pages ul li a:hover{
	color:#690;
	text-decoration:none;
	padding:2px 4px 1px;
	background:#EEE;
}
.dede_pages ul li.thisclass a,.pagebox ul li.thisclass a:hover{
	color:#F63;
	padding:2px 4px 1px;
	border-bottom:1px solid #F63;
	font-weight:bold;
}
.dede_pages .pageinfo{
	float:right;
	line-height:21px;
	padding:7px 10px 3px 16px;
	color:#999;
}
.dede_pages .pageinfo strong{
	color:#666;
	font-weight:normal;
	margin:0px 2px;
}

.center{
	margin:0px auto;
}
.w960{
	width:960px;
	/*position:relative;*/
}
.footer_body{
	text-align:center;
	}