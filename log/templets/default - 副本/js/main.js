// 主要JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    
    // 导航菜单功能
    initNavigation();
    
    // 搜索功能
    initSearch();
    
    // 筛选标签功能
    initFilterTabs();
    
    // 加载更多功能
    initLoadMore();
    
    // 移动端适配
    initMobileFeatures();
    
    // 滚动效果
    initScrollEffects();
    
    // 卡片悬停效果
    initCardEffects();
});

// 导航菜单功能
function initNavigation() {
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const menu = dropdown.querySelector('.dropdown-menu');
        
        dropdown.addEventListener('mouseenter', () => {
            menu.style.opacity = '1';
            menu.style.visibility = 'visible';
            menu.style.transform = 'translateY(0)';
        });
        
        dropdown.addEventListener('mouseleave', () => {
            menu.style.opacity = '0';
            menu.style.visibility = 'hidden';
            menu.style.transform = 'translateY(-10px)';
        });
    });
}

// 搜索功能
function initSearch() {
    const searchForm = document.querySelector('.search-box form');
    const searchInput = document.querySelector('.search-input');
    
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            const keyword = searchInput.value.trim();
            if (!keyword) {
                e.preventDefault();
                alert('请输入搜索关键词');
                return;
            }
        });
    }
    
    // 搜索建议功能
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const keyword = this.value.trim();
            
            if (keyword.length > 2) {
                searchTimeout = setTimeout(() => {
                    // 这里可以添加搜索建议的AJAX请求
                    console.log('搜索建议:', keyword);
                }, 300);
            }
        });
    }
}

// 筛选标签功能
function initFilterTabs() {
    const filterTabs = document.querySelectorAll('.filter-tabs a');
    const resourceGrid = document.querySelector('.resource-grid');
    
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有active类
            filterTabs.forEach(t => t.classList.remove('active'));
            
            // 添加active类到当前标签
            this.classList.add('active');
            
            // 获取筛选类型
            const filterType = this.textContent.trim();
            
            // 添加加载动画
            if (resourceGrid) {
                resourceGrid.style.opacity = '0.5';
                
                // 模拟加载延迟
                setTimeout(() => {
                    resourceGrid.style.opacity = '1';
                    console.log('筛选类型:', filterType);
                    // 这里可以添加AJAX请求来获取筛选后的内容
                }, 500);
            }
        });
    });
}

// 加载更多功能
function initLoadMore() {
    const loadMoreBtn = document.querySelector('.load-more-btn');
    const resourceGrid = document.querySelector('.resource-grid');
    let currentPage = 1;
    let isLoading = false;
    
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            if (isLoading) return;
            
            isLoading = true;
            this.textContent = '加载中...';
            this.disabled = true;
            
            // 模拟AJAX请求
            setTimeout(() => {
                currentPage++;
                
                // 模拟添加新内容
                const newCards = createMockResourceCards(6);
                newCards.forEach(card => {
                    resourceGrid.appendChild(card);
                });
                
                // 重置按钮状态
                this.textContent = '加载更多';
                this.disabled = false;
                isLoading = false;
                
                // 如果达到最大页数，隐藏按钮
                if (currentPage >= 5) {
                    this.style.display = 'none';
                    document.querySelector('.pagination-info').style.display = 'block';
                }
                
                // 添加动画效果
                animateNewCards();
                
            }, 1000);
        });
    }
}

// 创建模拟资源卡片
function createMockResourceCards(count) {
    const cards = [];
    const categories = ['精品手游', '网页游戏', '传奇版本库', '文字游戏', '手游源代码'];
    const titles = [
        '【XO传奇手游】三端互通1.80玲珑星王大极品合击版',
        '【天龙之柔情似水】八部天龙柔情似水版',
        '【白日门传奇】楼兰传奇单职业版',
        '【诛仙III小凡龙年地宫技改版】经典修仙DY地宫技改版',
        '【龙OL初始】3D魔幻神话端游',
        '【将军令OL】经典2DMMORPG网游'
    ];
    
    for (let i = 0; i < count; i++) {
        const card = document.createElement('div');
        card.className = 'resource-card';
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        const randomCategory = categories[Math.floor(Math.random() * categories.length)];
        const randomTitle = titles[Math.floor(Math.random() * titles.length)];
        const randomViews = Math.floor(Math.random() * 1000) + 100;
        const randomDownloads = Math.floor(Math.random() * 500) + 50;
        const randomPrice = Math.floor(Math.random() * 500) + 100;
        
        card.innerHTML = `
            <div class="card-image">
                <img src="/default/images/placeholder.jpg" alt="${randomTitle}">
                <div class="card-category">${randomCategory}</div>
            </div>
            <div class="card-content">
                <h3><a href="#">${randomTitle}</a></h3>
                <div class="card-meta">
                    <span class="time">${Math.floor(Math.random() * 30) + 1}天前</span>
                    <span class="views"><i class="fas fa-eye"></i> ${randomViews}</span>
                    <span class="comments"><i class="fas fa-comment"></i> 0</span>
                    <span class="downloads"><i class="fas fa-download"></i> ${randomDownloads}</span>
                    <span class="price">${randomPrice}</span>
                </div>
            </div>
        `;
        
        cards.push(card);
    }
    
    return cards;
}

// 新卡片动画
function animateNewCards() {
    const newCards = document.querySelectorAll('.resource-card[style*="opacity: 0"]');
    
    newCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// 移动端功能
function initMobileFeatures() {
    // 移动端导航
    const mobileNavItems = document.querySelectorAll('.mobile-nav .nav-item');
    
    mobileNavItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // 移除所有active类
            mobileNavItems.forEach(nav => nav.classList.remove('active'));
            
            // 添加active类到当前项
            this.classList.add('active');
        });
    });
    
    // 移动端搜索优化
    const searchInput = document.querySelector('.search-input');
    if (searchInput && window.innerWidth <= 768) {
        searchInput.placeholder = '搜索资源...';
    }
}

// 滚动效果
function initScrollEffects() {
    let lastScrollTop = 0;
    const header = document.querySelector('.header');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // 头部滚动隐藏/显示
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // 向下滚动，隐藏头部
            header.style.transform = 'translateY(-100%)';
        } else {
            // 向上滚动，显示头部
            header.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
        
        // 滚动到顶部按钮
        const scrollToTop = document.querySelector('.scroll-to-top');
        if (scrollToTop) {
            if (scrollTop > 500) {
                scrollToTop.style.display = 'block';
            } else {
                scrollToTop.style.display = 'none';
            }
        }
    });
    
    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// 卡片悬停效果
function initCardEffects() {
    const resourceCards = document.querySelectorAll('.resource-card');
    
    resourceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}

// 工具函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 懒加载图片
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// 主题切换功能（可选）
function initThemeToggle() {
    const themeToggle = document.querySelector('.theme-toggle');
    
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('light-theme');
            
            const isLight = document.body.classList.contains('light-theme');
            localStorage.setItem('theme', isLight ? 'light' : 'dark');
        });
        
        // 加载保存的主题
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'light') {
            document.body.classList.add('light-theme');
        }
    }
}

// 通知系统
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 15px 20px;
        border-radius: 5px;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// 表单验证
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('error');
            isValid = false;
        } else {
            input.classList.remove('error');
        }
    });
    
    return isValid;
}

// 初始化所有功能
function initAllFeatures() {
    initLazyLoading();
    initThemeToggle();
}

// 页面加载完成后初始化额外功能
window.addEventListener('load', initAllFeatures);
