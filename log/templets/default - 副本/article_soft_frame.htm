<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset={dede:global.cfg_soft_lang/}" />
<title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
<meta name="keywords" content="{dede:field.keywords/}" />
<meta name="description" content="{dede:field.description function='html2text(@me)'/}" />
<link href="{dede:global.cfg_templets_skin/}/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url={dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="{dede:global.cfg_mobileurl/}/view.php?aid={dede:field.id/}";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
</head>

<body class="downlist">
{dede:include filename="head2.htm"/}
<!-- /header -->

<div class="channel-nav w960 center clear">
	<div class='sonnav'>
	{dede:channel type='self' currentstyle="<span><a href='~typelink~' class='thisclass'>~typename~</a></span>"}
		<span><a href='[field:typeurl/]'>[field:typename/]</a></span>{/dede:channel}
	</div>
	<span class="back">
		<a href="{dede:global.cfg_cmsurl/}/">返回首页</a>
	</span>
</div><!-- /channel-nav -->

<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
			<strong>当前位置:</strong> {dede:field name='position'/}
		</div><!-- /place -->
		<div class="viewbox">
			<div class="title">
				<h2>{dede:field.title /}</h2>
			</div><!-- /title -->
			<div class="infolist">
				<small>软件类型：</small><span>{dede:field.softtype/}</span>
				<small>授权方式：</small><span>{dede:field.accredit/}</span>
				<small>界面语言：</small><span>{dede:field.language/}</span>
				<small>软件大小：</small><span>{dede:field.softsize/}</span>
				<small>文件类型：</small><span>{dede:field.filetype/}</span>
				<small>运行环境：</small><span>{dede:field.os/}</span>
				<small>软件等级：</small><span>{dede:field.softrank function='GetRankStar(@me)'/}</span>
				<small>发布时间：</small><span>{dede:field.pubdate function="GetDateMk('@me')"/}</span>
				<small>官方网址：</small><span>{dede:field.officialurl/}</span>
                <small>演示网址：</small><span>{dede:field.officialdemo/}</span>
                <small>下载次数：</small><span><script src="{dede:field.phpurl/}/disdls.php?aid={dede:field.id/}" language="javascript"></script></span>
			</div><!-- /info -->
			<div class="picview">
				{dede:field name='image'/}
			</div><!-- /info -->
			<div class="labeltitle">
				<strong>软件介绍</strong>
			</div>
			<div class="content">
				{dede:field.introduce/}
			</div>
			<div class="labeltitle">
				<strong>下载地址</strong>
			</div>
			<div class="content">
				<ul class="downurllist">
                	{dede:field name='softlinks'/}
                </ul>
			</div>
			<div class="labeltitle">
				<strong>下载说明</strong>
			</div>
			<div class="intro">
				{dede:softmsg/}
			</div>
<iframe src="{dede:global.cfg_phpurl/}/digg_frame.php?id={dede:field.id/}" width="98%" height="100" style="overflow:hidden;" frameborder="0"></iframe>
			<div class="boxoff">
				<strong>------分隔线----------------------------</strong>
			</div>
			<div class="handle">
				<div class="context">
					<ul>
						<li>{dede:prenext get='pre'/}</li>
						<li>{dede:prenext get='next'/}</li>
					</ul>
				</div><!-- /context -->
				<div class="actbox">
					<ul>
						<li id="act-fav"><a href="{dede:global name='cfg_phpurl'/}/stow.php?aid={dede:field.id/}" target="_blank">收藏</a></li>
						<li id="act-err"><a href="{dede:global name='cfg_phpurl'/}/erraddsave.php?aid={dede:field.id/}&title={dede:field.title/}" target="_blank">挑错</a></li>
						<li id="act-pus"><a href="{dede:global name='cfg_phpurl'/}/recommend.php?aid={dede:field.id/}" target="_blank">推荐</a></li>
						<li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
					</ul>
				</div><!-- /actbox -->
			</div><!-- /handle -->
		</div><!-- /viewbox -->

<iframe id="frame_content" name="frame_content" src="{dede:global name='cfg_phpurl'/}/comments_frame.php?id={dede:field.id/}"
 width="100%" height="0" scrolling="auto" onload="resizeFrame()" frameborder="0"></iframe>

<script language='javascript'>
var h = -1;
var frameName = 'frame_content';
var _frame = document.getElementById(frameName);
function resizeFrame()
{
	if(document.all) {
		_frame.height = _frame.document.body.scrollHeight;
	}
	else {
		t = setTimeout(timeCall, 20);
	}
}
function timeCall()
{
	if(h > -1)
	{
		_frame.style.height = h + 'px';
		clearTimeout(t);
	}
	else
	{
		try{
			h = window.frames[frameName].frames['xclient'].location.hash.substring(1);
		}catch(e){}
		setTimeout(timeCall, 20);
	}
}
</script>
	</div><!-- /pleft -->

	<div class="pright">
        	<div class="infos_userinfo">
 			{dede:memberinfos}
			<dl class="tbox">
				<dt><strong>发布者资料</strong></dt>
				<dd>
					<a href="[field:spaceurl /]" class="userface"><img src="[field:face/]" width="52" height="52" /></a>
					<a href='[field:spaceurl /]' class="username">[field:uname/]</a>
					<span class="useract">
						<a href="[field:spaceurl /]" class="useract-vi">查看详细资料</a>
                        <a href="[field:spaceurl /]&action=guestbook" class="useract-pm">发送留言</a>
						<a href="[field:spaceurl /]&action=newfriend" class="useract-af">加为好友</a>
					</span>
					<span class="userinfo-sp"><small>用户等级:</small>[field:rankname /]</span>
					<span class="userinfo-sp"><small>注册时间:</small>[field:jointime function="MyDate('Y-m-d H:m',@me)"/]</span>
					<span class="userinfo-sp"><small>最后登录:</small>[field:logintime function="MyDate('Y-m-d H:m',@me)"/]</span>
				</dd>
			</dl>
			{/dede:memberinfos}
   	 	</div>
		<div class="downrange mt1">
			<dl class="tbox">
				<dt><strong>推荐软件</strong></dt>
				<dd>
					<ul class="f1">
                    {dede:arclist flag='c' titlelen=42 row=8}
						<li>
							<a href="[field:arcurl/]">[field:title/]</a>
							<span><small>好评:</small>[field:scores/]</span>
							<span><small>人气:</small>[field:click/]</span>
						</li>
                    {/dede:arclist}
					</ul>
				</dd>
			</dl>
		</div><!-- /downrange -->

		<div class="hot mt1">
			<dl class="tbox">
				<dt><strong>热门软件</strong></dt>
				<dd>
					<ul class="c1 ico2">
                    {dede:arclist row=10 orderby=click}
                    	<li><a href="[field:arcurl/]">[field:title/]</a></li>
                    {/dede:arclist}
					</ul>
				</dd>
			</dl>
		</div>
	</div><!-- /pright -->
</div>
{dede:include filename="footer.htm"/}
<!-- /footer -->
<script src="{dede:global name='cfg_phpurl'/}/count.php?aid={dede:field name='id'/}&mid={dede:field name='mid'/}" language="javascript"></script>
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
