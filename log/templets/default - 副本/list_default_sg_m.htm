<!DOCTYPE html>
<html>
	<head lang="en">
		<meta charset="{dede:global.cfg_soft_lang/}">
		<title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<meta name="format-detection" content="telephone=no">
		<meta name="renderer" content="webkit">
		<meta http-equiv="Cache-Control" content="no-siteapp" />
		<link rel="alternate icon" type="image/png" href="assets/i/favicon.png">
		<link rel="stylesheet" href="assets/css/amazeui.min.css" />
		<style>
			@media only screen and (min-width: 1200px) {
			      .blog-g-fixed {
			        max-width: 1200px;
			      }
			    }
			
			    @media only screen and (min-width: 641px) {
			      .blog-sidebar {
			        font-size: 1.4rem;
			      }
			    }

			    .intro{
				    margin-top: 10px;
				    font-size: 12px;
				    color: #757575;
			    }
			
			    .blog-main {
			      padding: 20px 0;
			    }
			
			    .blog-title {
			      margin: 10px 0 20px 0;
			    }
			
			    .blog-meta {
			      font-size: 14px;
			      margin: 10px 0 20px 0;
			      color: #222;
			    }
			
			    .blog-meta a {
			      color: #27ae60;
			    }
			
			    .blog-pagination a {
			      font-size: 1.4rem;
			    }
			
			    .blog-team li {
			      padding: 4px;
			    }
			
			    .blog-team img {
			      margin-bottom: 0;
			    }
			
			    .blog-footer {
			      padding: 10px 0;
			      text-align: center;
			    }
		</style>
	</head>
	<body>{dede:include filename="header_m.htm"/}
		<div class="am-g am-g-fixed blog-g-fixed">
			<div class="am-u-md-8">
				<div data-am-widget="list_news" class="am-list-news am-list-news-default">
					<!--列表标题-->
					<div class="am-list-news-bd">
						<ul class="am-list">
							<!--缩略图在标题左边-->{dede:list pagesize='10'}
							<li class="am-g am-list-item-desced am-list-item-thumbed am-list-item-thumb-left">
								<div class="am-u-sm-4 am-list-thumb">
									<a href="view.php?aid=[field:id/]">
										<img src="[field:litpic/]" width="75px" height="75px">
									</a>
								</div>
								<div class=" am-u-sm-8 am-list-main">
									 <h3 class="am-list-item-hd">



            <a href="view.php?aid=[field:id/]" class="">[field:title/]</a>



          </h3>
									<div class="am-list-item-text"><b>日期：</b>[field:pubdate function="GetDateTimeMK(@me)"/] <b>点击：</b>[field:click/] <b>好评：</b>[field:scores/]</div>
									<p class="intro">[field:description/]...</p>
								</div>
							</li>{/dede:list}</ul>
					</div>
					<div class="dede_pages">
						<ul data-am-widget="pagination" class="am-pagination am-pagination-default">{dede:pagelist listitem="info,index,end,pre,next,pageno,option" listsize="5"/}</ul>
					</div>
				</div>
			</div>
			<div class="am-u-md-4 blog-sidebar">
				<div class="am-panel-group">
					<section class="am-panel am-panel-default">
						<div class="am-panel-hd">最近关注</div>
						<ul class="am-list blog-list">{dede:arclistsg row='5' orderby='pubdate' titlelen=40}
							<li><a href="view.php?aid=[field:id/]">[field:title/]</a>
							</li>{/dede:arclistsg}</ul>
					</section>
					<section class="am-panel am-panel-default">
						<div class="am-panel-hd">热点内容</div>
						<ul class="am-list blog-list">{dede:arclistsg row='5' orderby='hot' titlelen=40}
							<li><a href="view.php?aid=[field:id/]">[field:title/]</a>
							</li>{/dede:arclistsg}</ul>
					</section>
				</div>
			</div>
		</div>{dede:include filename="footer_m.htm"/}
		<!--[if lt IE 9]>
			<script src="http://libs.baidu.com/jquery/1.11.1/jquery.min.js"></script>
			<script src="http://cdn.staticfile.org/modernizr/2.8.3/modernizr.js"></script>
			<script src="assets/js/polyfill/rem.min.js"></script>
			<script src="assets/js/polyfill/respond.min.js"></script>
			<script src="assets/js/amazeui.legacy.js"></script>
		<![endif]-->
		<!--[if (gte IE 9)|!(IE)]>
			<!-->
			<script src="assets/js/jquery.min.js"></script>
			<script src="assets/js/amazeui.min.js"></script>
		<!--<![endif]-->
	</body>

</html>