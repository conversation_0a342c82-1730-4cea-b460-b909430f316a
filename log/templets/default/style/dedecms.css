/***
 *	DedeCMS v5.6 Style (Default Style)
 *	dedecms.com  Author Networker  2010.02.05
 **/
 
/*---------- import ---------*/
@import url("layout.css");
@import url("page.css");


/*---------- base ---------*/
*{
	padding:0px;
	margin:0px;
}
html{
	background:#FFF;
}
body{
	font:12px <PERSON>erdana,Arial,Tahoma;
}
img{
	border:none;
}

a{
	color:#256EB1;
	text-decoration:none;
}
a:hover{
	color:#ba2636;
	text-decoration:underline;
}
ul{
	list-style:none;
}
input,select,button{
	font:12px Verdana,Arial,Tahoma;
	vertical-align:middle;
}
/*---------- stock ---------*/
.center{
	margin:0px auto;
}
.w960{
	width:960px;
	/*position:relative;*/
}
.pright .infos_userinfo {
	margin-bottom: 0px;
}
.mt1{/* ( margin-top * 1 ) */
	margin-top:8px;
}
.pright .mt1{
	margin-top:0px;
}
.mt2{/* ( margin-top * 2 ) */
	margin-top:16px;
}
.clear{
	overflow:hidden;
}
.fs-12{
	font-size:12px;
}
.fc-f60{
	color:#F60;
}
.fc-f90{
	color:#F90;
}
.clr{
	clear:both;
	}
.ipt-txt{
	line-height:15px;
	padding:4px 5px;
	border-width:1px;
	border-style:solid;
	border-color:#666 #BBB #BBB #666;
	font-size:12px;
	margin-right:2px;
}
.nb{
	line-height:20x;
	padding:1px 2px;
	border-width:1px;
	border-style:solid;
	border-color:#666 #BBB #BBB #666;
	font-size:12px;
	margin-right:2px
}
.btn-1{
	width:56px;
	height:24px;
	border:none;
	background:url(../images/comm-bt.gif) no-repeat;
	line-height:25px;
	letter-spacing:1px;
	cursor:pointer;
	overflow:hidden;
	color:#585858;
}
.btn-2{
	width:70px;
	height:25px;
	border:none;
	background:url(../images/btn-bg2.gif) left top no-repeat;
	line-height:25px;
	overflow:hidden;
	color:#444;
	margin-right:2px;
	cursor:pointer;
}
/*---------- frame ---------*/
/*---------- frame : header ---------*/
.header{
	width:100%;
	width:960px;
	margin:auto;
	overflow:hidden;
}
.header_top{
	height:25px!important;
	height:24px;
	line-height:25px;
	border-bottom:1px solid #DBDBDB;
	color:#676767;
	overflow:hidden;
    background:url("../images/green_skin.png") repeat-x scroll 0 -188px transparent;
	}
.header_top .time{
	float:left;
	padding-left:10px;	
	}
.header_top a.rss{
    
	}
.header_top .toplinks{	
	float:right;
	text-align:right;
}
.header_top .toplinks a{
	margin:0 5px;
	}
.header_top .toplinks span{
	margin-left:15px;
	}
.header_top .toplinks span a{
	margin:0 2px;
	}
.header .search {
	overflow:hidden;
}
.header a{
	color:#777;
}
.header a:hover{
	color:#ff3333;
	text-decoration:none;
}
.header .top{
	clear:both;
	overflow:hidden;
	margin-top:10px;
}
.header .title{
	float:left;
}
.header .title h1 a{
	width:216px;
	height:54px;
	display:block;
	overflow:hidden;
}
.header .banner{
	width:468px;
	height:60px;
	float:right;
	margin-left:10px;
	overflow:hidden;
}
.header .banner img{
	width:468px;
	height:60px;
	display:block;
}
.banner2{
	width:950px;
	height:90px;
	margin-top: 10px;
	overflow:hidden;
}
.banner2 img{
	width:950px;
	height:90px;
	display:block;
}

.header .welcome{
	float:right;
	margin-top:20px;
	padding-right:10px;
	color:#999;
}
.header .welcome a{
	margin:0px 3px;	
}

/*----- 新版导航菜单位置的样式 -------*/
.header .nav { }
/*-------- 圆角模型 ---------*/
.module, .module .mid {
	overflow:hidden;
}
.module .top .t_l, .module .bottom .b_l {
	float:left;
	overflow:hidden;
}
.module .top .t_r, .module .bottom .b_r {
	float:right;
	overflow:hidden;
}
.module .top em {
	float:left;
	font-size:13px;
	font-weight:bold;
	font-family:Arial, Helvetica, sans-serif;
	margin-left: 5px;
}
.module .top em a:link, .module .top em a:visited {
	font-size:13px;
	font-weight:bold;
}
.module .top span {
	
}
.module .top strong {
	cursor:pointer;
	float:right;
	font-weight:normal;
	margin-right:4px;
}
.module .mid .m_l, .module .mid .m_r {
	overflow:hidden;
}
.module .mid .content {
	overflow:hidden;
	height:100%;
	clear: both;
	margin-right: 8px;
	margin-left: 8px;
	padding-top: 8px;/*padding-bottom: 10px;*/
}
.module .top, .module .top .t_l, .module .top .t_r, .module .bottom, .module .bottom .b_l, .module .bottom .b_r {
	background-image: url("../images/green_skin.png");
}
/*------ 主色 -------*/
.blue .top {
	background-position: 0 -72px;
	background-repeat: repeat-x;
	height: 70px;
}
.blue .top .t_l {
	background-position: 0 0;
	background-repeat: no-repeat;
	height: 70px;
	width: 5px;
}
.blue .top .t_r {
	background-position: -6px 0;
	background-repeat: no-repeat;
	height: 70px;
	width: 5px;
}
/* --------- 导航 ----------------*/
.w963 {
	width:960px;
}
#navMenu {
	width:915px;
	overflow:hidden;
	height: 28px;
	padding:8px 0 0 15px;
}
#navMenu ul {
	float:left;
	height: 22px;
}
#navMenu ul li {
    font:14px/1.5 '思源黑体',"宋体";
	float:left;	
	height: 22px;
	margin-right: 10px;
	margin-left: -3px;
	padding-left: 10px;
}
#navMenu ul li a {
	color: #FFF;
	height: 22px;
	text-decoration:none;
	display: inline-block;
	position: relative;
}
#navMenu ul li a.hover {
	color:#DEFF01;
	height: 22px;
	text-decoration:none;
	display: inline-block;
	position: relative;
}
    
#navMenu ul li span {
	cursor:pointer;
	display:inline-block;
	height:22px;
	line-height:20px;
	margin:0 0 0 5px;
	padding:0 5px 0 0;
	text-align:center;
	vertical-align:middle;
	font-weight:bold;
	color:#ebf5e9;
}
#navMenu ul li.hover {
	padding-top:0;
}
#navMenu ul li.hover a {
	display: inline-block;
	position: relative;
}
#navMenu ul li.hover span {
	cursor:pointer;
	display:inline-block;
	height:22px;
	line-height:20px;
	margin:0 0 0 5px;
	padding:0 5px 0 0;
	text-align:center;
	vertical-align:middle;
}
#navMenu ul li a.hover, #navMenu ul li a:hover {
	text-decoration:none;
    color:#DEFF01;
	display: inline-block;
	position: relative;
}
#navMenu ul li a.hover span, #navMenu ul li a:hover span {
	cursor:pointer;
	display:inline-block;
	height:22px;
    color:#DEFF01;
	line-height:20px;
	margin:0 0 0 5px;
	padding:0 5px 0 0;
	text-align:center;
	vertical-align:middle;
}
/*-------- 下拉菜单 --------------*/
.dropMenu {
	position:absolute;
	top: 0;
	z-index:100;
	width: 120px;
	visibility: hidden;
    filter: progid:DXImageTransform.Microsoft.Shadow(color=#CACACA, direction=135, strength=4);
	margin-top: -1px;
	border: 1px solid #93E1EB;
	border-top: 0px solid #3CA2DC;
	background-color: #FFF;
	background:url(../images/mmenubg.gif);
	padding-top:6px;
	padding-bottom:6px;
}

.dropMenu li {
	margin-top:2px;
	margin-bottom:4px;
	padding-left:6px;
}
.dropMenu a {
	width: auto;
	display: block;
	color: black;
	padding: 2px 0 2px 1.2em;
}
* html .dropMenu a {
	width: 100%;
}
.dropMenu a:hover {
	color:red;
	text-decoration: underline;
}
/*------ //搜索框 ---------*/
.search-keyword {
	width:210px;
	height:18px;
	padding-top:2px;
	padding-left:6px;
	border:0px;
	border:#badaa1 solid 1px;
	background: #FFF;
	color:#444;
}
.search-submit {
	cursor:pointer;
	width:68px;
	height:22px;
	font-size:0px;
	color:#fafafa;
	border:0px;
	background:url(../images/search-bt.gif) no-repeat;
}
.search-option {
	margin-left:3px;
	margin-right:3px;
	border:#badaa1 solid 1px;
	height:22px;
}
.w963 .search{
	padding-left:10px;
	line-height:32px;
}
.w963 .form h4 {
	display:none;
}
.w963 .form {
	float:left;
	margin:0 10px 0 0;
	*margin:0 10px 0 0;
	_margin:5px 10px 0 0;
}
.w963 .tags {
	width:500px;
	overflow:hidden;
}
.w963 .tags h4 {
	float:left;
	margin-right: 6px;
	height:26px;
	font-size:12px;
	color:#777;
}
.w963 .tags li {
	float:left;
	margin-right: 6px;
}
.header .nav .end { }
/*-- //End 导航菜单 --*/

/*---------- frame : channel-nav ---------*/
.channel-nav {
	margin-top:8px;
	padding-left:6px;
	height:24px;
	width:950px;
	overflow:hidden;
}
.channel-nav .sonnav {
	width:830px;
	line-height:26px;
	float:left;
	color:#256DB1;
}
.channel-nav .sonnav span {	
	margin-right:10px;
	float:left;
}
.channel-nav .sonnav span a{
	padding:0 4px;
	border:1px solid #BADAA1;
	height:22px;
	line-height:21px;
	background:url(../images/channel_bg.png) repeat-x;
	display:inline-block;
	}
.channel-nav .sonnav span a.thisclass{
	border:1px solid #3aa21b;
	}
.channel-nav .sonnav a {
	color:#428C5B;
	text-decoration:none;
}
.channel-nav .sonnav a:hover{
	 color:#287212;
	}
.channel-nav .back{
	display:block;
	height:22px;
	line-height:21px;
	padding-top:6px;
	padding-right:10px;
	padding-left:20px;
	letter-spacing:2px;
	float:right;
	background:url(../images/ico-home.gif) 4px 10px no-repeat;
} 
.channel-nav .back a{
	color:#397CBE;
}
.channel-nav .back a:hover{
	text-decoration:none;
	color:#777;
}
/*pic scroll
----------------------------------*/
.infiniteCarousel {
  width: 700px;
  position: relative;
  margin-left:auto;
  margin-right:auto;
}

.infiniteCarousel .wrapper {
  width: 640px; 
  overflow: auto;
  height: 170px;
  margin: 0 30px;
  top: 0;
}
.infiniteCarousel ul a img {
  border:1px solid #E3E3E3;
  padding:2px;
  width:143px;
  height:106px;
  display:block;
}
.infiniteCarousel .wrapper ul {
  width: 625px; 
  list-style-image:none;
  list-style-position:outside;
  list-style-type:none;
  margin:0;
  padding:0;
  top: 0;
}
.infiniteCarousel ul li {
  display:block;
  color:#6C6D61;
  float:left;
  padding: 10px 6px;
  height: 147px;
  width: 147px;
  text-align:center;
}
.infiniteCarousel ul li a,
.infiniteCarousel ul li a:visited{
	color:#6C6D61;
	}
.infiniteCarousel .wrapper ul li a:hover{
	text-decoration:underline;
	}
.infiniteCarousel ul li a:hover img {
  border-color: #aaa;
}
.infiniteCarousel ul li a span{
   display:block;
   line-height:17px;
   padding-top:6px;
}
.infiniteCarousel .arrow {
  display: block;
  height: 26px;
  width: 26px;
  text-indent: -999px;
  position: absolute;
  top: 70px;
  cursor: pointer;
  outline: 0;
}
.infiniteCarousel .forward {
  background:url(../images/green_skin.png) 0 -256px no-repeat;
  right: 0;
}
.infiniteCarousel .back {
  background:url(../images/green_skin.png) 0 -222px no-repeat;
  left: 0;
}
/*----------dedeinfolink  ---------*/
#dedeinfolink {
	margin-bottom:6px;
	}
#dedeinfolink tr td div {
	padding:0 5px;
	background:url(../images/white_bg.gif) repeat-x;
	margin-right:8px;
	}
#dedeinfolink tr td {
	line-height:18px;
	}
#dedeinfolink tr td.spline {
	font-size:1px;
	height:1px;
	line-height:1px;
	border-bottom:1px dashed #dedede;
	}
#dedeinfolink tr td.iftitle {
	font-weight:bold;
	color:#428C5B;
	line-height:24px;
	border-bottom:1px dashed #dedede;
}
/*---------- frame : footer ---------*/
.footer{
	margin:auto;
	color:#999;
	text-align:center;
	margin-top:8px;
	padding-bottom:10px;
	border-top:1px solid #E5EFD6;
	padding-top:10px;
}
.footer .link{
	text-align:center;
	padding:5px 0px;
}
.footer .link a{
	margin:0px 5px;
	color:#666666;
}
.footer .powered{
	font-size:10px;
	line-height:25px;
}
.footer .powered strong{
	color:#690;
}
.footer .powered strong span{
	color:#F93;
}
.footer .copyright{
	color:#666666;
	line-height:23px;
}

/*new search result page
----------------------------------------*/
.search_header{
    overflow:hidden;
	zoom:1;
}
.search_header  h1{
    float:left;
    display:inline;
    margin:5px 20px 5px 10px;
    width:216px;
}
.search_header .search_box{
    float:left;
    padding-top:25px;
}
.search_header .search_box input{
    border-width:1px;
	border-style:solid;
	border-color:#707070 #CECECE #CECECE #707070;
	padding:2px 4px;
	height:18px;
	line-height:18px;
    width:200px;
	margin-right:5px;
}
.search_header .search_box select{
   font-size:14px;
   height:22px;
   margin-right:5px;
}
.search_header .search_box button{
    margin-right:3px;
}
.resultbar{
	height:32px;
	line-height:32px;
	background:transparent url(../images/search-top-bg.gif) repeat-x scroll;
	text-indent:12px;
	color:#428C5B;
	border-bottom: 1px solid #E4E4E4;
	border-top: 1px solid #E4E4E4;
 }
 .result_content{
    overflow:hidden;
	zoom:1;
 }
.sidebar{
  float:right;
  width:300px;
  padding:20px 0 0 20px;
  margin-right:20px;
  border-left:1px solid #dadada;
  word-wrap:break-word;
}
.sidebar h2{
  font-size:14px;
  line-height:25px;
 }
.sidebar  ul {
  padding:4px 8px;
 }
.sidebar  ul li{
  line-height:24px;
  background:url(../images/ico-3.gif) 4px 9px no-repeat;
 }
.resultlist{
  overflow:hidden;
}
.resultlist ul{
  padding:20px 0 0 20px;
 }
.resultlist ul li{
  padding-top:15px;
 }
.resultlist ul li h3{
  line-height:30px;
  font-size:16px;
  font-weight:normal;
 }
.resultlist ul li h3 a{
  text-decoration:underline;
 }
.resultlist ul li p{
  line-height:22px;
  color:#333;
  font-size:14px;
  width:650px;
 }
.resultlist ul li span small{
  line-height:22px;
  font-size:12px;
  margin-left:5px;
  color:#999;
 }
.resultlist ul li span a{
  color:#008400;
 }
#contentRtPicAD2.stick {
  position:fixed;
  top:0;
  margin:60px 0 0
}