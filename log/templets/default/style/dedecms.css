/***
 *	小仙元码 - 游戏资源分享网站样式
 *	基于 https://wd.xxymw.com 网站样式制作
 *	暗色主题，现代化设计
 **/

/*---------- import ---------*/
@import url("layout.css");
@import url("page.css");

/*---------- 基础样式 ---------*/
* {
	padding: 0px;
	margin: 0px;
	box-sizing: border-box;
}

html {
	background: #0a0a0a;
	scroll-behavior: smooth;
}

body {
	font: 14px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
	background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
	color: #fff;
	line-height: 1.6;
	min-height: 100vh;
}

img {
	border: none;
	max-width: 100%;
	height: auto;
}

a {
	color: #00d4ff;
	text-decoration: none;
	transition: all 0.3s ease;
}

a:hover {
	color: #0099cc;
	text-decoration: none;
}

ul {
	list-style: none;
}

input, select, button {
	font: 14px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
	vertical-align: middle;
	background: #333;
	border: 1px solid #555;
	color: #fff;
	border-radius: 5px;
	padding: 8px 12px;
}

input:focus, select:focus, button:focus {
	outline: none;
	border-color: #00d4ff;
	box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

button {
	background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
	border: none;
	color: #fff;
	cursor: pointer;
	transition: all 0.3s ease;
}

button:hover {
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}
/*---------- 通用样式类 ---------*/
.center {
	margin: 0px auto;
}

.w960 {
	width: 100%;
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.container {
	max-width: 1200px;
	margin: 0 auto;
	padding: 0 20px;
}

.pright .infos_userinfo {
	margin-bottom: 0px;
}

.mt1 {
	margin-top: 15px;
}

.pright .mt1 {
	margin-top: 0px;
}

.mt2 {
	margin-top: 30px;
}

.clear {
	overflow: hidden;
}

.clr {
	clear: both;
}

.fs-12 {
	font-size: 12px;
}

.fc-f60 {
	color: #00d4ff;
}

.fc-f90 {
	color: #ffd700;
}

/* 现代化输入框样式 */
.ipt-txt {
	line-height: 20px;
	padding: 10px 15px;
	border: 1px solid #555;
	background: #333;
	color: #fff;
	border-radius: 8px;
	font-size: 14px;
	margin-right: 10px;
	transition: all 0.3s ease;
}

.ipt-txt:focus {
	border-color: #00d4ff;
	box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
	outline: none;
}

.nb {
	line-height: 20px;
	padding: 8px 12px;
	border: 1px solid #555;
	background: #333;
	color: #fff;
	border-radius: 5px;
	font-size: 14px;
	margin-right: 10px;
}

/* 现代化按钮样式 */
.btn-1 {
	min-width: 80px;
	height: 40px;
	border: none;
	background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
	color: #fff;
	border-radius: 20px;
	line-height: 40px;
	text-align: center;
	cursor: pointer;
	font-weight: bold;
	transition: all 0.3s ease;
	padding: 0 20px;
}

.btn-1:hover {
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.btn-2 {
	min-width: 100px;
	height: 40px;
	border: none;
	background: linear-gradient(135deg, #333 0%, #555 100%);
	color: #fff;
	border-radius: 20px;
	line-height: 40px;
	text-align: center;
	cursor: pointer;
	transition: all 0.3s ease;
	padding: 0 20px;
}

.btn-2:hover {
	background: linear-gradient(135deg, #555 0%, #777 100%);
	transform: translateY(-2px);
}
	color:#444;
	margin-right:2px;
	cursor:pointer;
}
/*---------- frame ---------*/
/*---------- frame : header ---------*/
.header{
	width:100%;
	width:960px;
	margin:auto;
	overflow:hidden;
}
.header_top{
	height:25px!important;
	height:24px;
	line-height:25px;
	border-bottom:1px solid #DBDBDB;
	color:#676767;
	overflow:hidden;
    background:url("../images/green_skin.png") repeat-x scroll 0 -188px transparent;
	}
.header_top .time{
	float:left;
	padding-left:10px;	
	}
.header_top a.rss{
    
	}
.header_top .toplinks{	
	float:right;
	text-align:right;
}
.header_top .toplinks a{
	margin:0 5px;
	}
.header_top .toplinks span{
	margin-left:15px;
	}
.header_top .toplinks span a{
	margin:0 2px;
	}
.header .search {
	overflow:hidden;
}
.header a{
	color:#777;
}
.header a:hover{
	color:#ff3333;
	text-decoration:none;
}
.header .top{
	clear:both;
	overflow:hidden;
	margin-top:10px;
}
.header .title{
	float:left;
}
.header .title h1 a{
	width:216px;
	height:54px;
	display:block;
	overflow:hidden;
}
.header .banner{
	width:468px;
	height:60px;
	float:right;
	margin-left:10px;
	overflow:hidden;
}
.header .banner img{
	width:468px;
	height:60px;
	display:block;
}
.banner2{
	width:950px;
	height:90px;
	margin-top: 10px;
	overflow:hidden;
}
.banner2 img{
	width:950px;
	height:90px;
	display:block;
}

.header .welcome{
	float:right;
	margin-top:20px;
	padding-right:10px;
	color:#999;
}
.header .welcome a{
	margin:0px 3px;	
}

/*----- 新版导航菜单位置的样式 -------*/
.header .nav { }
/*-------- 圆角模型 ---------*/
.module, .module .mid {
	overflow:hidden;
}
.module .top .t_l, .module .bottom .b_l {
	float:left;
	overflow:hidden;
}
.module .top .t_r, .module .bottom .b_r {
	float:right;
	overflow:hidden;
}
.module .top em {
	float:left;
	font-size:13px;
	font-weight:bold;
	font-family:Arial, Helvetica, sans-serif;
	margin-left: 5px;
}
.module .top em a:link, .module .top em a:visited {
	font-size:13px;
	font-weight:bold;
}
.module .top span {
	
}
.module .top strong {
	cursor:pointer;
	float:right;
	font-weight:normal;
	margin-right:4px;
}
.module .mid .m_l, .module .mid .m_r {
	overflow:hidden;
}
.module .mid .content {
	overflow:hidden;
	height:100%;
	clear: both;
	margin-right: 8px;
	margin-left: 8px;
	padding-top: 8px;/*padding-bottom: 10px;*/
}
.module .top, .module .top .t_l, .module .top .t_r, .module .bottom, .module .bottom .b_l, .module .bottom .b_r {
	background-image: url("../images/green_skin.png");
}
/*------ 主色 -------*/
.blue .top {
	background-position: 0 -72px;
	background-repeat: repeat-x;
	height: 70px;
}
.blue .top .t_l {
	background-position: 0 0;
	background-repeat: no-repeat;
	height: 70px;
	width: 5px;
}
.blue .top .t_r {
	background-position: -6px 0;
	background-repeat: no-repeat;
	height: 70px;
	width: 5px;
}
/* --------- 导航 ----------------*/
.w963 {
	width:960px;
}
#navMenu {
	width:915px;
	overflow:hidden;
	height: 28px;
	padding:8px 0 0 15px;
}
#navMenu ul {
	float:left;
	height: 22px;
}
#navMenu ul li {
    font:14px/1.5 '思源黑体',"宋体";
	float:left;	
	height: 22px;
	margin-right: 10px;
	margin-left: -3px;
	padding-left: 10px;
}
#navMenu ul li a {
	color: #FFF;
	height: 22px;
	text-decoration:none;
	display: inline-block;
	position: relative;
}
#navMenu ul li a.hover {
	color:#DEFF01;
	height: 22px;
	text-decoration:none;
	display: inline-block;
	position: relative;
}
    
#navMenu ul li span {
	cursor:pointer;
	display:inline-block;
	height:22px;
	line-height:20px;
	margin:0 0 0 5px;
	padding:0 5px 0 0;
	text-align:center;
	vertical-align:middle;
	font-weight:bold;
	color:#ebf5e9;
}
#navMenu ul li.hover {
	padding-top:0;
}
#navMenu ul li.hover a {
	display: inline-block;
	position: relative;
}
#navMenu ul li.hover span {
	cursor:pointer;
	display:inline-block;
	height:22px;
	line-height:20px;
	margin:0 0 0 5px;
	padding:0 5px 0 0;
	text-align:center;
	vertical-align:middle;
}
#navMenu ul li a.hover, #navMenu ul li a:hover {
	text-decoration:none;
    color:#DEFF01;
	display: inline-block;
	position: relative;
}
#navMenu ul li a.hover span, #navMenu ul li a:hover span {
	cursor:pointer;
	display:inline-block;
	height:22px;
    color:#DEFF01;
	line-height:20px;
	margin:0 0 0 5px;
	padding:0 5px 0 0;
	text-align:center;
	vertical-align:middle;
}
/*-------- 下拉菜单 --------------*/
.dropMenu {
	position:absolute;
	top: 0;
	z-index:100;
	width: 120px;
	visibility: hidden;
    filter: progid:DXImageTransform.Microsoft.Shadow(color=#CACACA, direction=135, strength=4);
	margin-top: -1px;
	border: 1px solid #93E1EB;
	border-top: 0px solid #3CA2DC;
	background-color: #FFF;
	background:url(../images/mmenubg.gif);
	padding-top:6px;
	padding-bottom:6px;
}

.dropMenu li {
	margin-top:2px;
	margin-bottom:4px;
	padding-left:6px;
}
.dropMenu a {
	width: auto;
	display: block;
	color: black;
	padding: 2px 0 2px 1.2em;
}
* html .dropMenu a {
	width: 100%;
}
.dropMenu a:hover {
	color:red;
	text-decoration: underline;
}
/*------ //搜索框 ---------*/
.search-keyword {
	width:210px;
	height:18px;
	padding-top:2px;
	padding-left:6px;
	border:0px;
	border:#badaa1 solid 1px;
	background: #FFF;
	color:#444;
}
.search-submit {
	cursor:pointer;
	width:68px;
	height:22px;
	font-size:0px;
	color:#fafafa;
	border:0px;
	background:url(../images/search-bt.gif) no-repeat;
}
.search-option {
	margin-left:3px;
	margin-right:3px;
	border:#badaa1 solid 1px;
	height:22px;
}
.w963 .search{
	padding-left:10px;
	line-height:32px;
}
.w963 .form h4 {
	display:none;
}
.w963 .form {
	float:left;
	margin:0 10px 0 0;
	*margin:0 10px 0 0;
	_margin:5px 10px 0 0;
}
.w963 .tags {
	width:500px;
	overflow:hidden;
}
.w963 .tags h4 {
	float:left;
	margin-right: 6px;
	height:26px;
	font-size:12px;
	color:#777;
}
.w963 .tags li {
	float:left;
	margin-right: 6px;
}
.header .nav .end { }
/*-- //End 导航菜单 --*/

/*---------- frame : channel-nav ---------*/
.channel-nav {
	margin-top:8px;
	padding-left:6px;
	height:24px;
	width:950px;
	overflow:hidden;
}
.channel-nav .sonnav {
	width:830px;
	line-height:26px;
	float:left;
	color:#256DB1;
}
.channel-nav .sonnav span {	
	margin-right:10px;
	float:left;
}
.channel-nav .sonnav span a{
	padding:0 4px;
	border:1px solid #BADAA1;
	height:22px;
	line-height:21px;
	background:url(../images/channel_bg.png) repeat-x;
	display:inline-block;
	}
.channel-nav .sonnav span a.thisclass{
	border:1px solid #3aa21b;
	}
.channel-nav .sonnav a {
	color:#428C5B;
	text-decoration:none;
}
.channel-nav .sonnav a:hover{
	 color:#287212;
	}
.channel-nav .back{
	display:block;
	height:22px;
	line-height:21px;
	padding-top:6px;
	padding-right:10px;
	padding-left:20px;
	letter-spacing:2px;
	float:right;
	background:url(../images/ico-home.gif) 4px 10px no-repeat;
} 
.channel-nav .back a{
	color:#397CBE;
}
.channel-nav .back a:hover{
	text-decoration:none;
	color:#777;
}
/*pic scroll
----------------------------------*/
.infiniteCarousel {
  width: 700px;
  position: relative;
  margin-left:auto;
  margin-right:auto;
}

.infiniteCarousel .wrapper {
  width: 640px; 
  overflow: auto;
  height: 170px;
  margin: 0 30px;
  top: 0;
}
.infiniteCarousel ul a img {
  border:1px solid #E3E3E3;
  padding:2px;
  width:143px;
  height:106px;
  display:block;
}
.infiniteCarousel .wrapper ul {
  width: 625px; 
  list-style-image:none;
  list-style-position:outside;
  list-style-type:none;
  margin:0;
  padding:0;
  top: 0;
}
.infiniteCarousel ul li {
  display:block;
  color:#6C6D61;
  float:left;
  padding: 10px 6px;
  height: 147px;
  width: 147px;
  text-align:center;
}
.infiniteCarousel ul li a,
.infiniteCarousel ul li a:visited{
	color:#6C6D61;
	}
.infiniteCarousel .wrapper ul li a:hover{
	text-decoration:underline;
	}
.infiniteCarousel ul li a:hover img {
  border-color: #aaa;
}
.infiniteCarousel ul li a span{
   display:block;
   line-height:17px;
   padding-top:6px;
}
.infiniteCarousel .arrow {
  display: block;
  height: 26px;
  width: 26px;
  text-indent: -999px;
  position: absolute;
  top: 70px;
  cursor: pointer;
  outline: 0;
}
.infiniteCarousel .forward {
  background:url(../images/green_skin.png) 0 -256px no-repeat;
  right: 0;
}
.infiniteCarousel .back {
  background:url(../images/green_skin.png) 0 -222px no-repeat;
  left: 0;
}
/*----------dedeinfolink  ---------*/
#dedeinfolink {
	margin-bottom:6px;
	}
#dedeinfolink tr td div {
	padding:0 5px;
	background:url(../images/white_bg.gif) repeat-x;
	margin-right:8px;
	}
#dedeinfolink tr td {
	line-height:18px;
	}
#dedeinfolink tr td.spline {
	font-size:1px;
	height:1px;
	line-height:1px;
	border-bottom:1px dashed #dedede;
	}
#dedeinfolink tr td.iftitle {
	font-weight:bold;
	color:#428C5B;
	line-height:24px;
	border-bottom:1px dashed #dedede;
}
/*---------- frame : footer ---------*/
.footer{
	margin:auto;
	color:#999;
	text-align:center;
	margin-top:8px;
	padding-bottom:10px;
	border-top:1px solid #E5EFD6;
	padding-top:10px;
}
.footer .link{
	text-align:center;
	padding:5px 0px;
}
.footer .link a{
	margin:0px 5px;
	color:#666666;
}
.footer .powered{
	font-size:10px;
	line-height:25px;
}
.footer .powered strong{
	color:#690;
}
.footer .powered strong span{
	color:#F93;
}
.footer .copyright{
	color:#666666;
	line-height:23px;
}

/*new search result page
----------------------------------------*/
.search_header{
    overflow:hidden;
	zoom:1;
}
.search_header  h1{
    float:left;
    display:inline;
    margin:5px 20px 5px 10px;
    width:216px;
}
.search_header .search_box{
    float:left;
    padding-top:25px;
}
.search_header .search_box input{
    border-width:1px;
	border-style:solid;
	border-color:#707070 #CECECE #CECECE #707070;
	padding:2px 4px;
	height:18px;
	line-height:18px;
    width:200px;
	margin-right:5px;
}
.search_header .search_box select{
   font-size:14px;
   height:22px;
   margin-right:5px;
}
.search_header .search_box button{
    margin-right:3px;
}
.resultbar{
	height:32px;
	line-height:32px;
	background:transparent url(../images/search-top-bg.gif) repeat-x scroll;
	text-indent:12px;
	color:#428C5B;
	border-bottom: 1px solid #E4E4E4;
	border-top: 1px solid #E4E4E4;
 }
 .result_content{
    overflow:hidden;
	zoom:1;
 }
.sidebar{
  float:right;
  width:300px;
  padding:20px 0 0 20px;
  margin-right:20px;
  border-left:1px solid #dadada;
  word-wrap:break-word;
}
.sidebar h2{
  font-size:14px;
  line-height:25px;
 }
.sidebar  ul {
  padding:4px 8px;
 }
.sidebar  ul li{
  line-height:24px;
  background:url(../images/ico-3.gif) 4px 9px no-repeat;
 }
.resultlist{
  overflow:hidden;
}
.resultlist ul{
  padding:20px 0 0 20px;
 }
.resultlist ul li{
  padding-top:15px;
 }
.resultlist ul li h3{
  line-height:30px;
  font-size:16px;
  font-weight:normal;
 }
.resultlist ul li h3 a{
  text-decoration:underline;
 }
.resultlist ul li p{
  line-height:22px;
  color:#333;
  font-size:14px;
  width:650px;
 }
.resultlist ul li span small{
  line-height:22px;
  font-size:12px;
  margin-left:5px;
  color:#999;
 }
.resultlist ul li span a{
  color:#008400;
 }
#contentRtPicAD2.stick {
  position: fixed;
  top: 0;
  margin: 60px 0 0;
}

/*---------- 现代化组件样式 ---------*/

/* 卡片样式 */
.card {
	background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
	border-radius: 15px;
	padding: 25px;
	margin-bottom: 25px;
	border: 1px solid #333;
	transition: all 0.3s ease;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.card:hover {
	transform: translateY(-5px);
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 文章卡片 */
.article-card {
	background: #2d2d2d;
	border-radius: 12px;
	overflow: hidden;
	transition: all 0.3s ease;
	border: 1px solid #404040;
	margin-bottom: 20px;
}

.article-card:hover {
	transform: translateY(-3px);
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
	border-color: #00d4ff;
}

.article-card .card-image {
	height: 200px;
	overflow: hidden;
	position: relative;
}

.article-card .card-image img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	transition: transform 0.3s ease;
}

.article-card:hover .card-image img {
	transform: scale(1.05);
}

.article-card .card-content {
	padding: 20px;
}

.article-card .card-title {
	color: #fff;
	font-size: 1.2rem;
	margin-bottom: 10px;
	line-height: 1.4;
}

.article-card .card-title a {
	color: #fff;
	text-decoration: none;
	transition: color 0.3s ease;
}

.article-card .card-title a:hover {
	color: #00d4ff;
}

.article-card .card-desc {
	color: #ccc;
	line-height: 1.6;
	margin-bottom: 15px;
}

.article-card .card-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: #666;
	font-size: 0.9rem;
}

/* 标签样式 */
.tag {
	background: #333;
	color: #00d4ff;
	padding: 5px 12px;
	border-radius: 15px;
	font-size: 0.85rem;
	text-decoration: none;
	display: inline-block;
	margin: 2px 5px 2px 0;
	transition: all 0.3s ease;
}

.tag:hover {
	background: #00d4ff;
	color: #fff;
	transform: translateY(-2px);
}

/* 按钮组 */
.btn-group {
	display: flex;
	gap: 10px;
	margin: 15px 0;
}

.btn-primary {
	background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
	color: #fff;
	padding: 12px 24px;
	border: none;
	border-radius: 25px;
	text-decoration: none;
	cursor: pointer;
	transition: all 0.3s ease;
	font-weight: bold;
	display: inline-flex;
	align-items: center;
	gap: 8px;
}

.btn-primary:hover {
	transform: translateY(-2px);
	box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
	color: #fff;
}

.btn-secondary {
	background: transparent;
	color: #ccc;
	border: 1px solid #333;
	padding: 12px 24px;
	border-radius: 25px;
	text-decoration: none;
	cursor: pointer;
	transition: all 0.3s ease;
	display: inline-flex;
	align-items: center;
	gap: 8px;
}

.btn-secondary:hover {
	background: #333;
	color: #00d4ff;
	border-color: #00d4ff;
}

/* 分页样式 */
.pagination {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 5px;
	margin: 30px 0;
}

.pagination a,
.pagination span {
	padding: 10px 15px;
	border: 1px solid #333;
	color: #ccc;
	text-decoration: none;
	border-radius: 8px;
	transition: all 0.3s ease;
	background: #2d2d2d;
}

.pagination a:hover {
	background: #00d4ff;
	color: #fff;
	border-color: #00d4ff;
	transform: translateY(-2px);
}

.pagination .current {
	background: #00d4ff;
	color: #fff;
	border-color: #00d4ff;
}

/* 侧边栏样式 */
.sidebar {
	float: right;
	width: 300px;
	padding: 0;
	margin: 0;
	border: none;
}

.sidebar-box {
	background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
	border-radius: 15px;
	padding: 25px;
	margin-bottom: 25px;
	border: 1px solid #333;
}

.sidebar-box h2,
.sidebar-box h3 {
	color: #00d4ff;
	margin-bottom: 20px;
	font-size: 1.3rem;
	border-bottom: 2px solid #00d4ff;
	padding-bottom: 10px;
}

.sidebar-box ul {
	padding: 0;
}

.sidebar-box ul li {
	line-height: 2;
	border-bottom: 1px solid #333;
	padding: 8px 0;
	background: none;
}

.sidebar-box ul li:last-child {
	border-bottom: none;
}

.sidebar-box ul li a {
	color: #ccc;
	transition: color 0.3s ease;
}

.sidebar-box ul li a:hover {
	color: #00d4ff;
}

/* 搜索框样式 */
.search-box {
	background: #333;
	border-radius: 25px;
	padding: 5px;
	display: flex;
	align-items: center;
	margin: 20px 0;
	border: 1px solid #555;
	transition: all 0.3s ease;
}

.search-box:focus-within {
	border-color: #00d4ff;
	box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.search-box input {
	flex: 1;
	background: transparent;
	border: none;
	padding: 12px 20px;
	color: #fff;
	font-size: 1rem;
}

.search-box input:focus {
	outline: none;
}

.search-box button {
	background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
	border: none;
	color: #fff;
	padding: 12px 20px;
	border-radius: 20px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.search-box button:hover {
	transform: scale(1.05);
}

/* 面包屑导航 */
.breadcrumb {
	background: #1a1a1a;
	padding: 15px 0;
	border-bottom: 1px solid #333;
	margin-bottom: 20px;
}

.breadcrumb a {
	color: #00d4ff;
	text-decoration: none;
	transition: color 0.3s ease;
}

.breadcrumb a:hover {
	color: #fff;
}

.breadcrumb .separator {
	color: #666;
	margin: 0 10px;
}

/* 文章内容样式 */
.article-content {
	background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
	border-radius: 15px;
	padding: 30px;
	margin-bottom: 30px;
	border: 1px solid #333;
	line-height: 1.8;
	color: #ccc;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
	color: #fff;
	margin: 30px 0 20px 0;
}

.article-content h1 {
	font-size: 2.5rem;
	border-bottom: 2px solid #00d4ff;
	padding-bottom: 15px;
}

.article-content h2 {
	font-size: 2rem;
	color: #00d4ff;
}

.article-content p {
	margin-bottom: 20px;
}

.article-content img {
	max-width: 100%;
	border-radius: 8px;
	margin: 20px 0;
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.article-content blockquote {
	background: #333;
	border-left: 4px solid #00d4ff;
	padding: 20px;
	margin: 20px 0;
	border-radius: 0 8px 8px 0;
	font-style: italic;
}

.article-content code {
	background: #333;
	color: #00d4ff;
	padding: 2px 6px;
	border-radius: 4px;
	font-family: 'Courier New', monospace;
}

.article-content pre {
	background: #333;
	padding: 20px;
	border-radius: 8px;
	overflow-x: auto;
	margin: 20px 0;
	border: 1px solid #555;
}

.article-content pre code {
	background: none;
	padding: 0;
}

/* 表格样式 */
.article-content table {
	width: 100%;
	border-collapse: collapse;
	margin: 20px 0;
	background: #333;
	border-radius: 8px;
	overflow: hidden;
}

.article-content th,
.article-content td {
	padding: 12px 15px;
	text-align: left;
	border-bottom: 1px solid #555;
}

.article-content th {
	background: #404040;
	color: #00d4ff;
	font-weight: bold;
}

.article-content tr:hover {
	background: #404040;
}

/* 列表样式 */
.article-content ul,
.article-content ol {
	margin: 20px 0;
	padding-left: 30px;
}

.article-content li {
	margin-bottom: 8px;
	line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.w960,
	.container {
		padding: 0 15px;
	}

	.sidebar {
		float: none;
		width: 100%;
		margin-top: 30px;
	}

	.article-card .card-content {
		padding: 15px;
	}

	.article-content {
		padding: 20px;
	}

	.article-content h1 {
		font-size: 1.8rem;
	}

	.article-content h2 {
		font-size: 1.5rem;
	}

	.btn-group {
		flex-direction: column;
	}

	.pagination {
		flex-wrap: wrap;
	}

	.search-box {
		flex-direction: column;
		gap: 10px;
	}

	.search-box input,
	.search-box button {
		width: 100%;
	}
}

/* 滚动条样式 */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
	background: #333;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #555;
}

/* 动画效果 */
@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.fade-in {
	animation: fadeIn 0.6s ease-out;
}

/* 加载动画 */
@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading {
	border: 3px solid #333;
	border-top: 3px solid #00d4ff;
	border-radius: 50%;
	width: 30px;
	height: 30px;
	animation: spin 1s linear infinite;
	margin: 20px auto;
}