# 小仙元码 - 游戏资源分享网站模版

这是一个基于 https://wd.xxymw.com 网站样式制作的DedeCMS模版，专为游戏资源分享网站设计。

## 模版特点

### 🎨 现代化设计
- 深色主题，符合游戏资源网站风格
- 渐变色彩搭配，视觉效果出色
- 响应式设计，完美适配各种设备

### 🚀 功能丰富
- 完整的首页、列表页、详情页模版
- 多种资源展示方式（网格视图/列表视图）
- 高级筛选和排序功能
- 用户评论和评分系统
- 会员VIP系统界面
- 客服咨询弹窗
- 分享功能集成

### 📱 移动端优化
- 响应式布局设计
- 移动端专用导航栏
- 触摸友好的交互设计
- 优化的加载性能

## 文件结构

```
default/
├── index_new.htm          # 新版首页模版
├── list_new.htm           # 新版列表页模版
├── article_new.htm        # 新版文章详情页模版
├── head_new.htm           # 新版头部包含文件
├── footer_new.htm         # 新版底部包含文件
├── style/
│   └── main.css          # 主要样式文件
├── js/
│   └── main.js           # 主要JavaScript文件
├── images/
│   ├── logo.svg          # 网站Logo
│   ├── banner.svg        # 横幅图片
│   └── placeholder.svg   # 占位图片
└── README.md             # 说明文档
```

## 安装使用

### 1. 备份原模版
在使用新模版前，请先备份您的原始模版文件：
```bash
cp -r /templets/default /templets/default_backup
```

### 2. 上传模版文件
将新模版文件上传到您的DedeCMS模版目录：
- 上传所有 `*_new.htm` 文件到 `/templets/default/` 目录
- 上传 `style/main.css` 到 `/templets/default/style/` 目录
- 上传 `js/main.js` 到 `/templets/default/js/` 目录
- 上传 `images/` 目录下的所有文件

### 3. 替换模版文件（可选）
如果您想直接替换原模版，可以重命名文件：
```bash
mv index_new.htm index.htm
mv list_new.htm list_default.htm
mv article_new.htm article_article.htm
mv head_new.htm head.htm
mv footer_new.htm footer.htm
```

### 4. 更新网站配置
在DedeCMS后台进行以下配置：
- 系统 → 系统基本参数 → 模版默认风格：选择 `default`
- 生成 → 更新主页HTML
- 生成 → 更新栏目HTML
- 生成 → 更新文档HTML

## 自定义配置

### 修改网站信息
编辑模版文件中的以下内容：
- 网站名称：`{dede:global.cfg_webname/}`
- 网站描述：`{dede:global.cfg_description/}`
- 联系方式：在 `footer_new.htm` 中修改QQ、微信等联系信息

### 修改颜色主题
在 `style/main.css` 中修改以下CSS变量：
```css
/* 主色调 */
--primary-color: #00d4ff;
--primary-dark: #0099cc;

/* 背景色 */
--bg-dark: #0a0a0a;
--bg-card: #1a1a1a;
--bg-card-light: #2d2d2d;

/* 文字颜色 */
--text-primary: #fff;
--text-secondary: #ccc;
--text-muted: #666;
```

### 添加自定义功能
1. **会员系统集成**：在相应位置添加会员登录、注册、充值等功能
2. **支付系统**：集成支付宝、微信支付等支付方式
3. **下载统计**：添加下载次数统计和限制功能
4. **积分系统**：实现积分获取、消费、兑换等功能

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ⚠️ IE 11（部分功能可能不支持）

## 性能优化建议

### 1. 图片优化
- 使用WebP格式图片
- 启用图片懒加载
- 压缩图片文件大小

### 2. CSS/JS优化
- 启用Gzip压缩
- 合并和压缩CSS/JS文件
- 使用CDN加速

### 3. 缓存设置
- 启用浏览器缓存
- 配置服务器缓存
- 使用Redis缓存数据库查询

## 常见问题

### Q: 模版显示不正常怎么办？
A: 请检查以下几点：
1. 确认所有文件都已正确上传
2. 检查文件权限是否正确
3. 清除浏览器缓存
4. 检查DedeCMS版本兼容性

### Q: 如何修改会员价格？
A: 在相应的模版文件中找到价格显示部分，修改对应的数值。

### Q: 如何添加新的分类？
A: 在DedeCMS后台的栏目管理中添加新分类，模版会自动显示。

### Q: 移动端显示有问题？
A: 请检查CSS媒体查询是否正确加载，确保响应式样式生效。

## 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 邮箱：<EMAIL>
- 💬 QQ群：123456789
- 📱 微信：扫描二维码添加客服

## 更新日志

### v1.0.0 (2024-01-25)
- 🎉 初始版本发布
- ✨ 完整的首页、列表页、详情页模版
- 🎨 现代化深色主题设计
- 📱 响应式移动端适配
- 🚀 丰富的交互功能

## 许可证

本模版基于 MIT 许可证发布，您可以自由使用、修改和分发。

## 致谢

感谢 https://wd.xxymw.com 提供的设计灵感和参考。

---

**注意**：本模版仅供学习和参考使用，请勿用于商业用途。使用前请确保遵守相关法律法规。
