{dede:include filename="head_new.htm"/}

    <!-- 面包屑导航 -->
    <section class="breadcrumb-section">
        <div class="container">
            <div class="breadcrumb">
                <a href="{dede:global.cfg_basehost/}">首页</a>
                <span class="separator">/</span>
                {dede:field name='position'/}
            </div>
        </div>
    </section>

    <!-- 文章详情页面 -->
    <main class="main-content">
        <div class="container">
            <div class="content-wrapper">
                <!-- 左侧内容 -->
                <div class="main-column">
                    <!-- 文章头部信息 -->
                    <article class="article-detail">
                        <header class="article-header">
                            <div class="article-category">
                                <a href="{dede:field.typeurl/}">{dede:field.typename/}</a>
                            </div>
                            <h1 class="article-title">{dede:field.title/}</h1>
                            <div class="article-meta">
                                <div class="meta-left">
                                    <span class="author">
                                        <i class="fas fa-user"></i>
                                        作者：{dede:field.writer/}
                                    </span>
                                    <span class="publish-time">
                                        <i class="fas fa-clock"></i>
                                        {dede:field.pubdate function='strftime("%Y年%m月%d日 %H:%M",@me)'/}
                                    </span>
                                    <span class="views">
                                        <i class="fas fa-eye"></i>
                                        浏览：{dede:field.click/}
                                    </span>
                                </div>
                                <div class="meta-right">
                                    <div class="article-actions">
                                        <button class="action-btn" onclick="toggleFavorite({dede:field.id/})">
                                            <i class="fas fa-heart"></i>
                                            收藏
                                        </button>
                                        <button class="action-btn" onclick="shareArticle()">
                                            <i class="fas fa-share"></i>
                                            分享
                                        </button>
                                        <div class="rating">
                                            <span>评分：</span>
                                            <div class="stars" data-rating="{dede:field.scores/}">
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                                <i class="fas fa-star"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </header>

                        <!-- 文章缩略图 -->
                        {dede:field.litpic}
                        <div class="article-thumbnail">
                            <img src="[field:litpic/]" alt="{dede:field.title/}">
                        </div>
                        {/dede:field.litpic}

                        <!-- 文章摘要 -->
                        <div class="article-summary">
                            <h3>资源简介</h3>
                            <p>{dede:field.description/}</p>
                        </div>

                        <!-- 资源信息卡片 -->
                        <div class="resource-info-card">
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">资源类型：</span>
                                    <span class="info-value">{dede:field.typename/}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">文件大小：</span>
                                    <span class="info-value">未知</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">下载次数：</span>
                                    <span class="info-value">{dede:field.click/}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">更新时间：</span>
                                    <span class="info-value">{dede:field.pubdate function='strftime("%Y-%m-%d",@me)'/}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">所需积分：</span>
                                    <span class="info-value price">300 积分</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">资源标签：</span>
                                    <div class="info-tags">
                                        {dede:field.keywords runphp='yes'}
                                        <?php
                                        $keywords = @me;
                                        $tags = explode(',', $keywords);
                                        foreach($tags as $tag) {
                                            $tag = trim($tag);
                                            if($tag) {
                                                echo '<a href="/tags.php?tag='.urlencode($tag).'" class="tag-item">'.$tag.'</a>';
                                            }
                                        }
                                        ?>
                                        {/dede:field.keywords}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 下载按钮区域 -->
                        <div class="download-section">
                            <div class="download-card">
                                <div class="download-info">
                                    <h3>下载此资源</h3>
                                    <p>本资源需要 <strong>300 积分</strong> 才能下载</p>
                                    <div class="download-tips">
                                        <p><i class="fas fa-info-circle"></i> 会员每日可免费下载5个资源</p>
                                        <p><i class="fas fa-shield-alt"></i> 所有资源均经过安全检测</p>
                                    </div>
                                </div>
                                <div class="download-actions">
                                    <button class="btn-download" onclick="downloadResource({dede:field.id/})">
                                        <i class="fas fa-download"></i>
                                        立即下载
                                    </button>
                                    <a href="/vip/" class="btn-vip">
                                        <i class="fas fa-crown"></i>
                                        开通VIP免费下载
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- 文章正文 -->
                        <div class="article-content">
                            {dede:field.body/}
                        </div>

                        <!-- 文章底部信息 -->
                        <footer class="article-footer">
                            <div class="article-tags">
                                <span class="tags-label">标签：</span>
                                {dede:field.keywords runphp='yes'}
                                <?php
                                $keywords = @me;
                                $tags = explode(',', $keywords);
                                foreach($tags as $tag) {
                                    $tag = trim($tag);
                                    if($tag) {
                                        echo '<a href="/tags.php?tag='.urlencode($tag).'" class="tag-item">'.$tag.'</a>';
                                    }
                                }
                                ?>
                                {/dede:field.keywords}
                            </div>
                            <div class="article-share">
                                <span class="share-label">分享到：</span>
                                <div class="share-buttons">
                                    <a href="#" class="share-btn qq" onclick="shareToQQ()">
                                        <i class="fab fa-qq"></i>
                                        QQ
                                    </a>
                                    <a href="#" class="share-btn wechat" onclick="shareToWechat()">
                                        <i class="fab fa-weixin"></i>
                                        微信
                                    </a>
                                    <a href="#" class="share-btn weibo" onclick="shareToWeibo()">
                                        <i class="fab fa-weibo"></i>
                                        微博
                                    </a>
                                    <a href="#" class="share-btn copy" onclick="copyLink()">
                                        <i class="fas fa-link"></i>
                                        复制链接
                                    </a>
                                </div>
                            </div>
                        </footer>
                    </article>

                    <!-- 相关推荐 -->
                    <section class="related-articles">
                        <h3>相关推荐</h3>
                        <div class="related-grid">
                            {dede:likearticle row='6' titlelen='40'}
                            <div class="related-item">
                                <div class="related-image">
                                    <img src="[field:litpic/]" alt="[field:title/]">
                                </div>
                                <div class="related-content">
                                    <h4><a href="[field:arcurl/]">[field:title/]</a></h4>
                                    <div class="related-meta">
                                        <span class="time">[field:pubdate function='strftime("%m-%d",@me)'/]</span>
                                        <span class="views">[field:click/] 浏览</span>
                                    </div>
                                </div>
                            </div>
                            {/dede:likearticle}
                        </div>
                    </section>

                    <!-- 评论区域 -->
                    <section class="comments-section">
                        <h3>用户评论</h3>
                        <div class="comment-form">
                            <form action="/plus/feedback_ajax.php" method="post" id="commentForm">
                                <input type="hidden" name="action" value="send">
                                <input type="hidden" name="aid" value="{dede:field.id/}">
                                <div class="form-group">
                                    <textarea name="msg" placeholder="请输入您的评论..." rows="4" required></textarea>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn-submit">
                                        <i class="fas fa-paper-plane"></i>
                                        发表评论
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="comments-list">
                            {dede:feedback}
                            <div class="comment-item">
                                <div class="comment-avatar">
                                    <img src="{dede:global.cfg_templets_skin/}/images/avatar.png" alt="用户头像">
                                </div>
                                <div class="comment-content">
                                    <div class="comment-header">
                                        <span class="comment-author">[field:username function="(@me=='guest' ? '游客' : @me)"/]</span>
                                        <span class="comment-time">[field:dtime function='strftime("%Y-%m-%d %H:%M",@me)'/]</span>
                                    </div>
                                    <div class="comment-text">
                                        [field:msg/]
                                    </div>
                                </div>
                            </div>
                            {/dede:feedback}
                        </div>
                    </section>
                </div>

                <!-- 右侧边栏 -->
                <aside class="sidebar">
                    <!-- 作者信息 -->
                    <section class="sidebar-section author-info">
                        <h3>作者信息</h3>
                        <div class="author-card">
                            <div class="author-avatar">
                                <img src="{dede:global.cfg_templets_skin/}/images/avatar.png" alt="作者头像">
                            </div>
                            <div class="author-details">
                                <h4>{dede:field.writer/}</h4>
                                <p>资深游戏开发者，专注于游戏源码分享</p>
                                <div class="author-stats">
                                    <span>文章：128</span>
                                    <span>粉丝：1.2K</span>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 同类热门 -->
                    <section class="sidebar-section">
                        <h3>同类热门</h3>
                        <div class="sidebar-list-simple">
                            {dede:arclist row='8' titlelen='30' orderby='click' typeid='[field:typeid/]'}
                            <div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="[field:arcurl/]">[field:title/]</a>
                                <span class="item-count">[field:click/]</span>
                            </div>
                            {/dede:arclist}
                        </div>
                    </section>

                    <!-- 最新评论 -->
                    <section class="sidebar-section">
                        <h3>最新评论</h3>
                        <div class="recent-comments">
                            {dede:feedback row='5' titlelen='20'}
                            <div class="comment-preview">
                                <div class="comment-user">[field:username function="(@me=='guest' ? '游客' : @me)"/]</div>
                                <div class="comment-content">[field:msg function='cn_substr(@me,50)'/]</div>
                                <div class="comment-time">[field:dtime function='strftime("%m-%d %H:%M",@me)'/]</div>
                            </div>
                            {/dede:feedback}
                        </div>
                    </section>
                </aside>
            </div>
        </div>
    </main>

{dede:include filename="footer_new.htm"/}

<script>
// 文章详情页功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化评分显示
    initRatingDisplay();
    
    // 初始化评论表单
    initCommentForm();
    
    // 初始化分享功能
    initShareFunctions();
});

// 评分显示
function initRatingDisplay() {
    const ratingElement = document.querySelector('.stars');
    if (ratingElement) {
        const rating = parseFloat(ratingElement.dataset.rating) || 0;
        const stars = ratingElement.querySelectorAll('i');
        
        stars.forEach((star, index) => {
            if (index < Math.floor(rating)) {
                star.classList.add('active');
            } else if (index < rating) {
                star.classList.add('half');
            }
        });
    }
}

// 评论表单
function initCommentForm() {
    const commentForm = document.getElementById('commentForm');
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('评论发表成功！', 'success');
                    this.reset();
                    // 重新加载评论列表
                    location.reload();
                } else {
                    showNotification(data.message || '评论发表失败', 'error');
                }
            })
            .catch(error => {
                showNotification('网络错误，请稍后重试', 'error');
            });
        });
    }
}

// 分享功能
function initShareFunctions() {
    // 分享功能已在各个分享函数中实现
}

// 收藏功能
function toggleFavorite(articleId) {
    // 这里可以添加AJAX请求来处理收藏功能
    console.log('切换收藏状态:', articleId);
    showNotification('已添加到收藏夹', 'success');
}

// 下载资源
function downloadResource(articleId) {
    // 检查用户登录状态和积分
    // 这里可以添加实际的下载逻辑
    console.log('下载资源:', articleId);
    
    // 模拟下载过程
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 准备下载...';
    btn.disabled = true;
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        showNotification('下载链接已发送到您的邮箱', 'success');
    }, 2000);
}

// 分享到QQ
function shareToQQ() {
    const title = document.title;
    const url = window.location.href;
    const shareUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

// 分享到微博
function shareToWeibo() {
    const title = document.title;
    const url = window.location.href;
    const shareUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

// 分享到微信（显示二维码）
function shareToWechat() {
    // 这里可以生成二维码或显示分享提示
    showNotification('请复制链接到微信分享', 'info');
}

// 复制链接
function copyLink() {
    const url = window.location.href;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showNotification('链接已复制到剪贴板', 'success');
        });
    } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('链接已复制到剪贴板', 'success');
    }
}

// 分享文章
function shareArticle() {
    if (navigator.share) {
        navigator.share({
            title: document.title,
            url: window.location.href
        });
    } else {
        // 显示分享选项
        document.querySelector('.article-share').scrollIntoView({ behavior: 'smooth' });
    }
}
</script>
