<svg width="1200" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bannerGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2d2d2d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a1a1a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="accentGrad" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:0.3" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="100%" height="100%" fill="url(#bannerGrad)"/>
  
  <!-- 装饰性几何图形 -->
  <circle cx="100" cy="100" r="50" fill="url(#accentGrad)" opacity="0.5"/>
  <circle cx="1100" cy="200" r="80" fill="url(#accentGrad)" opacity="0.3"/>
  <polygon points="200,50 250,100 200,150 150,100" fill="url(#accentGrad)" opacity="0.4"/>
  <polygon points="1000,50 1050,100 1000,150 950,100" fill="url(#accentGrad)" opacity="0.4"/>
  
  <!-- 网格背景 -->
  <defs>
    <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#333" stroke-width="1" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)"/>
  
  <!-- 中央内容区域 -->
  <rect x="300" y="100" width="600" height="100" fill="rgba(0,0,0,0.3)" rx="10"/>
  
  <!-- 装饰线条 -->
  <line x1="0" y1="150" x2="1200" y2="150" stroke="url(#accentGrad)" stroke-width="2" opacity="0.5"/>
  
  <!-- 代码风格装饰 -->
  <text x="50" y="250" font-family="monospace" font-size="12" fill="#00d4ff" opacity="0.6">&lt;/&gt; 代码创造未来</text>
  <text x="1000" y="50" font-family="monospace" font-size="12" fill="#00d4ff" opacity="0.6">{ 资源共享 }</text>
</svg>
