<style>
#dedeinfolink { margin-bottom:6px; }
#dedeinfolink tr td div { padding:2px 5px 2px 5px;background:#c5e09d; margin-right:8px; }
#dedeinfolink tr td { line-height:20px; }
#dedeinfolink tr td.spline { font-size:1px; height:1px; line-height:1px; border-bottom:1px dashed #dedede; }
#dedeinfolink tr td.iftitle {
	font-weight:bold; color:#688730; line-height:24px; border-bottom:1px dashed #dedede;
	padding-left:20px;
	background:url(/images/dd2.gif) 5px 5px no-repeat;
}
</style>
<table id='dedeinfolink'>
<tr>
	<td colspan='3' class='iftitle'>
		信息附加条件：
	</td>
</tr>
<tr>
	<td width='50'>&nbsp;地 区：</td>
	<td align='center' nowrap='yes'>
		<div>[field:linkallplace/]</div>
	</td>
	<td>
  	[field:nativeplace /]&nbsp;
	</td>
</tr>
<tr><td colspan='3' class='spline'>&nbsp;</td></tr>
<tr>
	<td>&nbsp;类 型：</td>
  <td align='center' nowrap='yes'>
  	<div>[field:linkalltype/]</div>
  </td>
  <td>
  	[field:infotype /]&nbsp;
  </td>
</tr>
<tr><td colspan='3' class='spline'>&nbsp;</td></tr>
</table>