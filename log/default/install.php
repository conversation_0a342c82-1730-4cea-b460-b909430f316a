<?php
/**
 * 小仙元码模版安装脚本
 * 基于 https://wd.xxymw.com 网站样式制作
 * 
 * 使用方法：
 * 1. 将此文件上传到网站根目录
 * 2. 访问 http://yoursite.com/install.php
 * 3. 按照提示完成安装
 */

// 防止直接访问
if (!defined('INSTALL_CHECK')) {
    define('INSTALL_CHECK', true);
}

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 模版信息
$template_info = [
    'name' => '小仙元码游戏资源模版',
    'version' => '1.0.0',
    'author' => 'AI Assistant',
    'description' => '基于 https://wd.xxymw.com 网站样式制作的DedeCMS模版',
    'compatible' => 'DedeCMS V5.7+',
    'release_date' => '2024-01-25'
];

// 需要安装的文件列表
$template_files = [
    'index_new.htm' => 'index.htm',
    'list_new.htm' => 'list_default.htm', 
    'article_new.htm' => 'article_article.htm',
    'head_new.htm' => 'head.htm',
    'footer_new.htm' => 'footer.htm',
    'style/main.css' => 'style/main.css',
    'js/main.js' => 'js/main.js',
    'images/logo.svg' => 'images/logo.png',
    'images/banner.svg' => 'images/banner.jpg',
    'images/placeholder.svg' => 'images/placeholder.jpg'
];

// 检查环境
function checkEnvironment() {
    $checks = [];
    
    // 检查PHP版本
    $checks['php_version'] = [
        'name' => 'PHP版本',
        'status' => version_compare(PHP_VERSION, '5.6.0', '>='),
        'message' => 'PHP ' . PHP_VERSION . (version_compare(PHP_VERSION, '5.6.0', '>=') ? ' (符合要求)' : ' (需要5.6.0+)')
    ];
    
    // 检查DedeCMS
    $checks['dedecms'] = [
        'name' => 'DedeCMS',
        'status' => file_exists('./include/common.inc.php'),
        'message' => file_exists('./include/common.inc.php') ? '已检测到DedeCMS' : '未检测到DedeCMS'
    ];
    
    // 检查模版目录
    $template_dir = './templets/default/';
    $checks['template_dir'] = [
        'name' => '模版目录',
        'status' => is_dir($template_dir) && is_writable($template_dir),
        'message' => is_dir($template_dir) ? 
            (is_writable($template_dir) ? '目录存在且可写' : '目录存在但不可写') : 
            '目录不存在'
    ];
    
    return $checks;
}

// 备份原文件
function backupFiles() {
    $backup_dir = './templets/default_backup_' . date('YmdHis') . '/';
    
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $original_files = [
        'index.htm',
        'list_default.htm',
        'article_article.htm',
        'head.htm',
        'footer.htm'
    ];
    
    $backed_up = [];
    foreach ($original_files as $file) {
        $source = './templets/default/' . $file;
        $target = $backup_dir . $file;
        
        if (file_exists($source)) {
            if (copy($source, $target)) {
                $backed_up[] = $file;
            }
        }
    }
    
    return ['dir' => $backup_dir, 'files' => $backed_up];
}

// 安装模版文件
function installTemplate() {
    global $template_files;
    
    $installed = [];
    $errors = [];
    
    foreach ($template_files as $source => $target) {
        $source_path = './default/' . $source;
        $target_path = './templets/default/' . $target;
        
        // 创建目标目录
        $target_dir = dirname($target_path);
        if (!is_dir($target_dir)) {
            mkdir($target_dir, 0755, true);
        }
        
        if (file_exists($source_path)) {
            if (copy($source_path, $target_path)) {
                $installed[] = $target;
            } else {
                $errors[] = "无法复制 $source 到 $target";
            }
        } else {
            $errors[] = "源文件不存在: $source";
        }
    }
    
    return ['installed' => $installed, 'errors' => $errors];
}

// 处理安装请求
if ($_POST['action'] == 'install') {
    $result = ['success' => false, 'message' => '', 'data' => []];
    
    try {
        // 检查环境
        $env_checks = checkEnvironment();
        $env_ok = true;
        foreach ($env_checks as $check) {
            if (!$check['status']) {
                $env_ok = false;
                break;
            }
        }
        
        if (!$env_ok) {
            throw new Exception('环境检查未通过，请检查系统要求');
        }
        
        // 备份原文件
        if ($_POST['backup'] == '1') {
            $backup_result = backupFiles();
            $result['data']['backup'] = $backup_result;
        }
        
        // 安装模版
        $install_result = installTemplate();
        $result['data']['install'] = $install_result;
        
        if (empty($install_result['errors'])) {
            $result['success'] = true;
            $result['message'] = '模版安装成功！';
        } else {
            $result['message'] = '安装过程中出现错误：' . implode(', ', $install_result['errors']);
        }
        
    } catch (Exception $e) {
        $result['message'] = $e->getMessage();
    }
    
    header('Content-Type: application/json');
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    exit;
}

// 获取环境信息
if ($_GET['action'] == 'check') {
    header('Content-Type: application/json');
    echo json_encode(checkEnvironment(), JSON_UNESCAPED_UNICODE);
    exit;
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小仙元码模版安装程序</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container { 
            max-width: 600px; 
            width: 90%; 
            background: rgba(45, 45, 45, 0.9);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px;
            border-bottom: 2px solid #00d4ff;
            padding-bottom: 20px;
        }
        .header h1 { 
            color: #00d4ff; 
            margin-bottom: 10px;
            font-size: 2rem;
        }
        .info-grid { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 10px; 
            margin-bottom: 30px;
            background: #333;
            padding: 20px;
            border-radius: 10px;
        }
        .info-item { 
            display: flex; 
            justify-content: space-between;
            padding: 5px 0;
        }
        .info-label { 
            color: #ccc; 
            font-weight: bold;
        }
        .info-value { 
            color: #00d4ff; 
        }
        .check-list { 
            margin-bottom: 30px;
        }
        .check-item { 
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: #333;
            border-radius: 8px;
            border-left: 4px solid #666;
        }
        .check-item.success { 
            border-left-color: #4CAF50; 
        }
        .check-item.error { 
            border-left-color: #f44336; 
        }
        .status { 
            padding: 5px 12px; 
            border-radius: 15px; 
            font-size: 0.9rem;
            font-weight: bold;
        }
        .status.success { 
            background: #4CAF50; 
            color: #fff;
        }
        .status.error { 
            background: #f44336; 
            color: #fff;
        }
        .form-group { 
            margin-bottom: 20px;
        }
        .form-group label { 
            display: block; 
            margin-bottom: 8px; 
            color: #ccc;
            font-weight: bold;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #333;
            border-radius: 8px;
        }
        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #00d4ff;
        }
        .btn { 
            width: 100%; 
            padding: 15px; 
            border: none; 
            border-radius: 25px; 
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer; 
            transition: all 0.3s ease;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #fff;
        }
        .btn-primary:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
        }
        .btn:disabled { 
            opacity: 0.6; 
            cursor: not-allowed;
            transform: none !important;
        }
        .result { 
            margin-top: 20px; 
            padding: 20px; 
            border-radius: 8px; 
            display: none;
        }
        .result.success { 
            background: rgba(76, 175, 80, 0.2); 
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        .result.error { 
            background: rgba(244, 67, 54, 0.2); 
            border: 1px solid #f44336;
            color: #f44336;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 3px solid #333;
            border-top: 3px solid #00d4ff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo $template_info['name']; ?></h1>
            <p><?php echo $template_info['description']; ?></p>
        </div>
        
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">版本：</span>
                <span class="info-value"><?php echo $template_info['version']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">作者：</span>
                <span class="info-value"><?php echo $template_info['author']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">兼容性：</span>
                <span class="info-value"><?php echo $template_info['compatible']; ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">发布日期：</span>
                <span class="info-value"><?php echo $template_info['release_date']; ?></span>
            </div>
        </div>
        
        <div class="check-list" id="checkList">
            <h3 style="margin-bottom: 15px; color: #ccc;">环境检查</h3>
            <!-- 环境检查结果将在这里显示 -->
        </div>
        
        <form id="installForm">
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="backup" name="backup" value="1" checked>
                    <label for="backup">安装前备份原文件（推荐）</label>
                </div>
            </div>
            
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="agree" name="agree" value="1" required>
                    <label for="agree">我已阅读并同意安装条款，了解安装风险</label>
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary" id="installBtn" disabled>
                开始安装模版
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在安装模版，请稍候...</p>
        </div>
        
        <div class="result" id="result"></div>
    </div>

    <script>
        // 检查环境
        function checkEnvironment() {
            fetch('?action=check')
                .then(response => response.json())
                .then(data => {
                    const checkList = document.getElementById('checkList');
                    let allPassed = true;
                    
                    let html = '<h3 style="margin-bottom: 15px; color: #ccc;">环境检查</h3>';
                    
                    for (const [key, check] of Object.entries(data)) {
                        const statusClass = check.status ? 'success' : 'error';
                        const statusText = check.status ? '通过' : '失败';
                        
                        if (!check.status) allPassed = false;
                        
                        html += `
                            <div class="check-item ${statusClass}">
                                <div>
                                    <strong>${check.name}</strong>
                                    <div style="color: #999; font-size: 0.9rem;">${check.message}</div>
                                </div>
                                <span class="status ${statusClass}">${statusText}</span>
                            </div>
                        `;
                    }
                    
                    checkList.innerHTML = html;
                    
                    // 更新安装按钮状态
                    updateInstallButton(allPassed);
                })
                .catch(error => {
                    console.error('环境检查失败:', error);
                });
        }
        
        // 更新安装按钮状态
        function updateInstallButton(envPassed) {
            const installBtn = document.getElementById('installBtn');
            const agreeCheckbox = document.getElementById('agree');
            
            installBtn.disabled = !(envPassed && agreeCheckbox.checked);
        }
        
        // 安装模版
        function installTemplate(formData) {
            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            const installBtn = document.getElementById('installBtn');
            
            loading.style.display = 'block';
            result.style.display = 'none';
            installBtn.disabled = true;
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                loading.style.display = 'none';
                result.style.display = 'block';
                
                if (data.success) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <h4>✅ ${data.message}</h4>
                        <p>模版已成功安装到 /templets/default/ 目录</p>
                        <p>请到DedeCMS后台更新网站HTML文件</p>
                    `;
                } else {
                    result.className = 'result error';
                    result.innerHTML = `
                        <h4>❌ 安装失败</h4>
                        <p>${data.message}</p>
                    `;
                    installBtn.disabled = false;
                }
            })
            .catch(error => {
                loading.style.display = 'none';
                result.style.display = 'block';
                result.className = 'result error';
                result.innerHTML = `
                    <h4>❌ 网络错误</h4>
                    <p>请检查网络连接后重试</p>
                `;
                installBtn.disabled = false;
            });
        }
        
        // 页面加载完成后检查环境
        document.addEventListener('DOMContentLoaded', function() {
            checkEnvironment();
            
            // 监听同意条款复选框
            document.getElementById('agree').addEventListener('change', function() {
                updateInstallButton(true); // 假设环境检查已通过
            });
            
            // 监听表单提交
            document.getElementById('installForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                formData.append('action', 'install');
                
                installTemplate(formData);
            });
        });
    </script>
</body>
</html>
