{dede:include filename="head.htm"/}

<!-- 面包屑导航 -->
<div class="breadcrumb">
    <a href="{dede:global.cfg_basehost/}">首页</a> &gt; 
    {dede:field name='position'/}
</div>

<!-- 分类页面 -->
<div class="list-container">
    <div class="list-main">
        <!-- 分类标题 -->
        <div class="category-header">
            <h1>{dede:field.typename/}</h1>
            <p>{dede:field.description/}</p>
            <div class="category-stats">
                <span>共 {dede:list.total/} 篇文章</span>
                <span>浏览 {dede:field.click/} 次</span>
            </div>
        </div>
        
        <!-- 文章列表 -->
        <div class="article-list">
            {dede:list pagesize='20'}
            <div class="article-item">
                <div class="article-thumb">
                    <a href="[field:arcurl/]">
                        <img src="[field:litpic/]" alt="[field:title/]" />
                    </a>
                </div>
                <div class="article-content">
                    <h3><a href="[field:arcurl/]">[field:title function='cn_substr(@me,60)'/]</a></h3>
                    <p class="article-desc">[field:description function='cn_substr(@me,150)'/]</p>
                    <div class="article-meta">
                        <span class="time">[field:pubdate function='strftime("%Y-%m-%d",@me)'/]</span>
                        <span class="views">浏览：[field:click/]</span>
                        <span class="author">作者：[field:writer/]</span>
                    </div>
                </div>
            </div>
            {/dede:list}
        </div>
        
        <!-- 分页 -->
        <div class="pagination">
            {dede:pagelist listsize='5'/}
        </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="list-sidebar">
        <!-- 本分类热门 -->
        <div class="sidebar-box">
            <h3>本分类热门</h3>
            <ul>
                {dede:arclist row='8' titlelen='30' orderby='click'}
                <li><a href="[field:arcurl/]">[field:title/]</a> <span>([field:click/])</span></li>
                {/dede:arclist}
            </ul>
        </div>
        
        <!-- 最新更新 -->
        <div class="sidebar-box">
            <h3>最新更新</h3>
            <ul>
                {dede:arclist row='8' titlelen='30' orderby='pubdate'}
                <li><a href="[field:arcurl/]">[field:title/]</a></li>
                {/dede:arclist}
            </ul>
        </div>
        
        <!-- 相关分类 -->
        <div class="sidebar-box">
            <h3>相关分类</h3>
            <ul>
                {dede:channel type='brother' currentstyle='<li class="current"><a href="~typelink~">~typename~</a></li>'}
                <li><a href="[field:typeurl/]">[field:typename/]</a></li>
                {/dede:channel}
            </ul>
        </div>
        
        <!-- 标签云 -->
        <div class="sidebar-box">
            <h3>热门标签</h3>
            <div class="tag-cloud">
                {dede:tag sort='hot' getnum='20'}
                <a href="[field:link/]">[field:tag/]</a>
                {/dede:tag}
            </div>
        </div>
    </div>
</div>

<style>
/* 基础样式 */
body {
    font-family: Arial, sans-serif;
    background: #0a0a0a;
    color: #fff;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.breadcrumb {
    background: #1a1a1a;
    padding: 10px 20px;
    border-bottom: 1px solid #333;
}

.breadcrumb a {
    color: #00d4ff;
    text-decoration: none;
}

.list-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    gap: 30px;
}

.list-main {
    flex: 2;
}

.category-header {
    background: #1a1a1a;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.category-header h1 {
    color: #00d4ff;
    font-size: 2rem;
    margin-bottom: 10px;
}

.category-header p {
    color: #ccc;
    margin-bottom: 15px;
}

.category-stats span {
    color: #666;
    margin-right: 20px;
}

.article-list {
    background: #1a1a1a;
    border-radius: 10px;
    padding: 20px;
}

.article-item {
    display: flex;
    padding: 20px 0;
    border-bottom: 1px solid #333;
}

.article-item:last-child {
    border-bottom: none;
}

.article-thumb {
    width: 150px;
    height: 100px;
    margin-right: 20px;
    flex-shrink: 0;
}

.article-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
}

.article-content {
    flex: 1;
}

.article-content h3 {
    margin-bottom: 10px;
}

.article-content h3 a {
    color: #fff;
    text-decoration: none;
    transition: color 0.3s;
}

.article-content h3 a:hover {
    color: #00d4ff;
}

.article-desc {
    color: #ccc;
    margin-bottom: 10px;
    line-height: 1.5;
}

.article-meta {
    color: #666;
    font-size: 0.9rem;
}

.article-meta span {
    margin-right: 15px;
}

.pagination {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    background: #333;
    color: #ccc;
    text-decoration: none;
    border-radius: 3px;
}

.pagination a:hover {
    background: #00d4ff;
    color: #fff;
}

.pagination .current {
    background: #00d4ff;
    color: #fff;
}

.list-sidebar {
    flex: 1;
}

.sidebar-box {
    background: #1a1a1a;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.sidebar-box h3 {
    color: #00d4ff;
    margin-bottom: 15px;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 5px;
}

.sidebar-box ul {
    list-style: none;
    padding: 0;
}

.sidebar-box li {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #333;
}

.sidebar-box li:last-child {
    border-bottom: none;
}

.sidebar-box li.current {
    background: #333;
    padding: 5px 10px;
    border-radius: 3px;
}

.sidebar-box a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.sidebar-box a:hover {
    color: #00d4ff;
}

.sidebar-box span {
    color: #666;
    font-size: 0.9rem;
}

.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-cloud a {
    background: #333;
    color: #ccc;
    padding: 5px 10px;
    border-radius: 15px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s;
}

.tag-cloud a:hover {
    background: #00d4ff;
    color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .list-container {
        flex-direction: column;
        padding: 10px;
    }
    
    .article-item {
        flex-direction: column;
    }
    
    .article-thumb {
        width: 100%;
        height: 200px;
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .category-header {
        padding: 20px;
    }
    
    .category-header h1 {
        font-size: 1.5rem;
    }
}
</style>

{dede:include filename="footer.htm"/}
