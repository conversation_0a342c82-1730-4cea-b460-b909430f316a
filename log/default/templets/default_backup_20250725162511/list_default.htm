{dede:include filename="head_new.htm"/}

    <!-- 面包屑导航 -->
    <section class="breadcrumb-section">
        <div class="container">
            <div class="breadcrumb">
                <a href="{dede:global.cfg_basehost/}">首页</a>
                <span class="separator">/</span>
                {dede:field name='position'/}
            </div>
        </div>
    </section>

    <!-- 分类页面主要内容 -->
    <main class="main-content">
        <div class="container">
            <div class="content-wrapper">
                <!-- 左侧内容 -->
                <div class="main-column">
                    <!-- 分类标题和描述 -->
                    <section class="category-header">
                        <div class="category-info">
                            <h1>{dede:field.typename/}</h1>
                            <p class="category-desc">{dede:field.description/}</p>
                            <div class="category-stats">
                                <span class="stat-item">
                                    <i class="fas fa-file-alt"></i>
                                    共 {dede:list.total/} 篇文章
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-eye"></i>
                                    总浏览量 {dede:field.click/}
                                </span>
                            </div>
                        </div>
                    </section>

                    <!-- 筛选和排序 -->
                    <section class="filter-section">
                        <div class="filter-controls">
                            <div class="filter-tabs">
                                <a href="?orderby=default" class="filter-tab active">默认排序</a>
                                <a href="?orderby=pubdate" class="filter-tab">最新发布</a>
                                <a href="?orderby=click" class="filter-tab">最多浏览</a>
                                <a href="?orderby=scores" class="filter-tab">最高评分</a>
                            </div>
                            <div class="view-mode">
                                <button class="view-btn active" data-view="grid">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="view-btn" data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                    </section>

                    <!-- 资源列表 -->
                    <section class="resources-list">
                        <div class="resource-grid" id="resourceGrid">
                            {dede:list pagesize='20'}
                            <div class="resource-card">
                                <div class="card-image">
                                    <img src="[field:litpic/]" alt="[field:title/]" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    [field:flag]
                                    {/dede:field.flag}
                                </div>
                                <div class="card-content">
                                    <h3><a href="[field:arcurl/]">[field:title function='cn_substr(@me,60)'/]</a></h3>
                                    <p class="card-desc">[field:description function='cn_substr(@me,100)'/]</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            [field:pubdate function='strftime("%Y-%m-%d",@me)'/]
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            [field:click/]
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            [field:click/]
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="[field:arcurl/]" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites([field:id/])">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {/dede:list}
                        </div>

                        <!-- 列表视图 -->
                        <div class="resource-list-view" id="resourceList" style="display: none;">
                            {dede:list pagesize='20'}
                            <div class="list-item">
                                <div class="item-image">
                                    <img src="[field:litpic/]" alt="[field:title/]" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="[field:arcurl/]">[field:title/]</a></h3>
                                    <p class="item-desc">[field:description function='cn_substr(@me,200)'/]</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">[field:pubdate function='strftime("%Y-%m-%d",@me)'/]</span>
                                        <span class="views">[field:click/] 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="[field:arcurl/]" class="btn-primary">立即下载</a>
                                </div>
                            </div>
                            {/dede:list}
                        </div>

                        <!-- 分页 -->
                        <div class="pagination-wrapper">
                            {dede:pagelist listsize='5'/}
                        </div>
                    </section>
                </div>

                <!-- 右侧边栏 -->
                <aside class="sidebar">
                    <!-- 热门资源 -->
                    <section class="sidebar-section">
                        <h3>本分类热门</h3>
                        <div class="sidebar-list-simple">
                            {dede:arclist typeid='{dede:field.typeid/}' row='8' titlelen='30' orderby='click'}
                            <div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="[field:arcurl/]">[field:title/]</a>
                                <span class="item-count">[field:click/]</span>
                            </div>
                            {/dede:arclist}
                        </div>
                    </section>

                    <!-- 最新更新 -->
                    <section class="sidebar-section">
                        <h3>最新更新</h3>
                        <div class="sidebar-list">
                            {dede:arclist row='6' titlelen='40' orderby='pubdate'}
                            <div class="sidebar-item">
                                <div class="item-image">
                                    <img src="[field:litpic/]" alt="[field:title/]">
                                </div>
                                <div class="item-content">
                                    <h4><a href="[field:arcurl/]">[field:title/]</a></h4>
                                    <div class="item-meta">
                                        <span class="time">[field:pubdate function='strftime("%m-%d",@me)'/]</span>
                                        <span class="views">[field:click/]</span>
                                    </div>
                                </div>
                            </div>
                            {/dede:arclist}
                        </div>
                    </section>

                    <!-- 相关分类 -->
                    <section class="sidebar-section">
                        <h3>相关分类</h3>
                        <div class="category-links">
                            {dede:channel type='brother' currentstyle='<a href="~typelink~" class="category-link active">~typename~</a>'}
                            <a href="[field:typeurl/]" class="category-link">[field:typename/]</a>
                            {/dede:channel}
                        </div>
                    </section>

                    <!-- 标签云 -->
                    <section class="sidebar-section">
                        <h3>热门标签</h3>
                        <div class="tag-cloud">
                            {dede:tag sort='hot' getnum='20'}
                            <a href="[field:link/]" class="tag-item">[field:tag/]</a>
                            {/dede:tag}
                        </div>
                    </section>

                    <!-- 广告位 -->
                    <section class="sidebar-section ad-section">
                        <div class="ad-banner">
                            <img src="{dede:global.cfg_templets_skin/}/images/ad-banner.jpg" alt="广告">
                        </div>
                    </section>
                </aside>
            </div>
        </div>
    </main>

{dede:include filename="footer_new.htm"/}

<script>
// 视图切换功能
document.addEventListener('DOMContentLoaded', function() {
    const viewBtns = document.querySelectorAll('.view-btn');
    const gridView = document.getElementById('resourceGrid');
    const listView = document.getElementById('resourceList');
    
    viewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // 移除所有active类
            viewBtns.forEach(b => b.classList.remove('active'));
            
            // 添加active类到当前按钮
            this.classList.add('active');
            
            // 切换视图
            const viewMode = this.dataset.view;
            if (viewMode === 'grid') {
                gridView.style.display = 'grid';
                listView.style.display = 'none';
            } else {
                gridView.style.display = 'none';
                listView.style.display = 'block';
            }
            
            // 保存用户偏好
            localStorage.setItem('viewMode', viewMode);
        });
    });
    
    // 加载用户偏好的视图模式
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode) {
        const targetBtn = document.querySelector(`[data-view="${savedViewMode}"]`);
        if (targetBtn) {
            targetBtn.click();
        }
    }
});

// 添加到收藏夹
function addToFavorites(articleId) {
    // 这里可以添加AJAX请求来处理收藏功能
    console.log('添加到收藏夹:', articleId);
    
    // 显示提示信息
    showNotification('已添加到收藏夹', 'success');
}

// 筛选功能
document.querySelectorAll('.filter-tab').forEach(tab => {
    tab.addEventListener('click', function(e) {
        e.preventDefault();
        
        // 移除所有active类
        document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
        
        // 添加active类到当前标签
        this.classList.add('active');
        
        // 获取排序参数
        const url = new URL(this.href);
        const orderby = url.searchParams.get('orderby');
        
        // 重新加载页面或使用AJAX加载内容
        window.location.href = this.href;
    });
});
</script>
