/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #0a0a0a;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 顶部导航 */
.header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-bottom: 1px solid #333;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.logo img {
    height: 40px;
    width: auto;
}

.main-nav ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-nav li {
    position: relative;
    margin: 0 20px;
}

.main-nav a {
    color: #fff;
    text-decoration: none;
    padding: 10px 0;
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
}

.main-nav a:hover {
    color: #00d4ff;
}

.main-nav .dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: #2d2d2d;
    min-width: 180px;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    padding: 12px 20px;
    border-bottom: 1px solid #404040;
    display: block;
}

.dropdown-menu a:hover {
    background: #404040;
}

.login-btn {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #fff !important;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

/* 搜索区域 */
.search-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: 40px 0;
}

.search-banner {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    height: 300px;
}

.search-banner img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.search-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: #fff;
}

.search-overlay h1 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-box {
    display: flex;
    margin-bottom: 20px;
    max-width: 600px;
    width: 100%;
}

.search-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px 0 0 25px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    outline: none;
}

.search-btn {
    padding: 15px 25px;
    border: none;
    border-radius: 0 25px 25px 0;
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: linear-gradient(135deg, #0099cc 0%, #007399 100%);
}

.search-tags {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.search-tags span {
    color: #ccc;
}

.search-tags a {
    color: #00d4ff;
    text-decoration: none;
    padding: 5px 12px;
    border: 1px solid #00d4ff;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.search-tags a:hover {
    background: #00d4ff;
    color: #fff;
}

/* 主要内容区域 */
.main-content {
    background: #0a0a0a;
    padding: 40px 0;
}

.content-wrapper {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 40px;
}

/* 网站动态 */
.site-activity {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.site-activity h3 {
    color: #fff;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #333;
}

.activity-item:last-child {
    border-bottom: none;
}

.user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
}

.activity-content {
    flex: 1;
}

.user-name {
    color: #00d4ff;
    font-weight: bold;
    margin-bottom: 5px;
}

.activity-text a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.activity-text a:hover {
    color: #00d4ff;
}

.activity-time {
    color: #666;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* 功能模块 */
.feature-modules {
    margin-bottom: 40px;
}

.module-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.module-item {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    border: 1px solid #333;
    transition: all 0.3s ease;
}

.module-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.1);
}

.module-icon {
    font-size: 3rem;
    color: #00d4ff;
    margin-bottom: 20px;
}

.module-item h4 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.module-item p {
    color: #ccc;
    line-height: 1.6;
}

/* 最新推荐 */
.latest-resources {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid #333;
}

.section-header {
    margin-bottom: 30px;
}

.section-header h2 {
    color: #fff;
    font-size: 2rem;
    margin-bottom: 10px;
}

.section-header p {
    color: #ccc;
    margin-bottom: 20px;
}

.filter-tabs {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-tabs a {
    padding: 8px 16px;
    border: 1px solid #333;
    border-radius: 20px;
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
}

.filter-tabs a.active,
.filter-tabs a:hover {
    background: #00d4ff;
    color: #fff;
    border-color: #00d4ff;
}

.resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.resource-card {
    background: #2d2d2d;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #404040;
    transition: all 0.3s ease;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.card-image {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.resource-card:hover .card-image img {
    transform: scale(1.05);
}

.card-category {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #00d4ff;
    color: #fff;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
}

.card-content {
    padding: 20px;
}

.card-content h3 {
    margin-bottom: 15px;
}

.card-content h3 a {
    color: #fff;
    text-decoration: none;
    font-size: 1.1rem;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.card-content h3 a:hover {
    color: #00d4ff;
}

.card-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #666;
    font-size: 0.9rem;
    flex-wrap: wrap;
}

.card-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.card-meta .price {
    color: #00d4ff;
    font-weight: bold;
    margin-left: auto;
}

/* 加载更多 */
.load-more {
    text-align: center;
}

.load-more-btn {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #fff;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.pagination-info {
    color: #666;
    margin-top: 15px;
}

/* 侧边栏 */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.sidebar-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #333;
}

.sidebar-section h3 {
    color: #fff;
    margin-bottom: 20px;
    font-size: 1.3rem;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 10px;
}

.sidebar-item {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #333;
}

.sidebar-item:last-child {
    border-bottom: none;
}

.item-image img {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
}

.item-content {
    flex: 1;
}

.item-content h4 a {
    color: #fff;
    text-decoration: none;
    font-size: 0.95rem;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.item-content h4 a:hover {
    color: #00d4ff;
}

.item-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    color: #666;
    font-size: 0.85rem;
}

.item-meta .price {
    color: #00d4ff;
    font-weight: bold;
}

.simple-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #333;
}

.simple-item:last-child {
    border-bottom: none;
}

.item-number {
    background: #00d4ff;
    color: #fff;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    margin-right: 10px;
}

.simple-item a {
    flex: 1;
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.simple-item a:hover {
    color: #00d4ff;
}

.item-count {
    color: #666;
    font-size: 0.85rem;
}

/* VIP会员区域 */
.vip-section {
    background: linear-gradient(135deg, #2d1810 0%, #4a2c1a 100%);
    border: 1px solid #8b4513;
}

.vip-section h3 {
    border-bottom-color: #ffd700;
    color: #ffd700;
}

.vip-plan {
    text-align: center;
    margin-top: 20px;
}

.vip-plan h4 {
    color: #ffd700;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.vip-price {
    color: #fff;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.vip-duration {
    color: #ccc;
    margin-bottom: 20px;
}

.vip-features {
    list-style: none;
    margin-bottom: 25px;
}

.vip-features li {
    color: #ccc;
    padding: 5px 0;
    position: relative;
    padding-left: 20px;
}

.vip-features li:before {
    content: "✓";
    color: #ffd700;
    position: absolute;
    left: 0;
}

.vip-btn {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    color: #000;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.vip-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

/* 统计区域 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #333;
    border-radius: 10px;
}

.stat-number {
    color: #00d4ff;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    color: #ccc;
    font-size: 0.9rem;
}

/* 底部 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-top: 1px solid #333;
    padding: 40px 0 20px;
    margin-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
    margin-bottom: 30px;
}

.footer-logo img {
    height: 40px;
    margin-bottom: 15px;
}

.footer-logo p {
    color: #ccc;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.link-group h4 {
    color: #fff;
    margin-bottom: 15px;
}

.link-group ul {
    list-style: none;
}

.link-group li {
    margin-bottom: 8px;
}

.link-group a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.link-group a:hover {
    color: #00d4ff;
}

.contact-qr {
    display: flex;
    gap: 10px;
}

.contact-qr img {
    width: 60px;
    height: 60px;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #333;
    color: #666;
}

.footer-links-inline {
    display: flex;
    gap: 20px;
}

.footer-links-inline a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links-inline a:hover {
    color: #00d4ff;
}

/* 移动端底部导航 */
.mobile-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #1a1a1a;
    border-top: 1px solid #333;
    z-index: 1000;
}

.mobile-nav {
    display: flex;
    justify-content: space-around;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-item.active,
.nav-item:hover {
    color: #00d4ff;
}

.nav-item i {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.nav-item span {
    font-size: 0.8rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content {
        flex-direction: column;
        height: auto;
        padding: 15px 0;
    }
    
    .main-nav {
        display: none;
    }
    
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .search-overlay h1 {
        font-size: 1.8rem;
    }
    
    .search-box {
        flex-direction: column;
        gap: 10px;
    }
    
    .search-input,
    .search-btn {
        border-radius: 25px;
    }
    
    .resource-grid {
        grid-template-columns: 1fr;
    }
    
    .module-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .mobile-nav {
        display: flex;
    }
    
    body {
        padding-bottom: 70px;
    }
}

/* 返回顶部按钮 */
.scroll-to-top {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.scroll-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

/* 客服按钮 */
.customer-service {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.service-btn {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: #fff;
    padding: 15px 20px;
    border-radius: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.service-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.service-btn i {
    font-size: 1.2rem;
}

/* 模态框样式 */
.modal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    margin: 10% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    border: 1px solid #333;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #fff;
    margin: 0;
}

.close {
    color: #666;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #00d4ff;
}

.modal-body {
    padding: 25px;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #333;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: #404040;
}

.contact-item i {
    font-size: 2rem;
    color: #00d4ff;
    width: 40px;
    text-align: center;
}

.contact-item h4 {
    color: #fff;
    margin: 0 0 5px 0;
}

.contact-item p {
    color: #ccc;
    margin: 0 0 10px 0;
}

.contact-btn {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #fff;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 212, 255, 0.3);
}

.qr-code {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    margin-top: 10px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 面包屑导航 */
.breadcrumb-section {
    background: #1a1a1a;
    padding: 15px 0;
    border-bottom: 1px solid #333;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #ccc;
}

.breadcrumb a {
    color: #00d4ff;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: #fff;
}

.separator {
    color: #666;
}

/* 分类页面样式 */
.category-header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.category-info h1 {
    color: #fff;
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.category-desc {
    color: #ccc;
    font-size: 1.1rem;
    margin-bottom: 20px;
    line-height: 1.6;
}

.category-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #00d4ff;
    font-weight: bold;
}

.stat-item i {
    color: #666;
}

/* 筛选控制 */
.filter-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-tabs {
    display: flex;
    gap: 10px;
}

.filter-tab {
    padding: 10px 20px;
    border: 1px solid #333;
    border-radius: 25px;
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
}

.filter-tab.active,
.filter-tab:hover {
    background: #00d4ff;
    color: #fff;
    border-color: #00d4ff;
}

.view-mode {
    display: flex;
    gap: 5px;
}

.view-btn {
    width: 40px;
    height: 40px;
    border: 1px solid #333;
    background: transparent;
    color: #ccc;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn.active,
.view-btn:hover {
    background: #00d4ff;
    color: #fff;
    border-color: #00d4ff;
}

/* 列表视图样式 */
.resource-list-view {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.list-item {
    display: flex;
    background: #2d2d2d;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #404040;
    transition: all 0.3s ease;
}

.list-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.list-item .item-image {
    width: 200px;
    height: 150px;
    flex-shrink: 0;
}

.list-item .item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.list-item .item-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.list-item h3 {
    margin-bottom: 10px;
}

.list-item h3 a {
    color: #fff;
    text-decoration: none;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.list-item h3 a:hover {
    color: #00d4ff;
}

.list-item .item-desc {
    color: #ccc;
    line-height: 1.6;
    margin-bottom: 15px;
}

.list-item .item-meta {
    display: flex;
    gap: 20px;
    color: #666;
    font-size: 0.9rem;
}

.list-item .item-meta .category {
    background: #00d4ff;
    color: #fff;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
}

.list-item .item-meta .price {
    color: #00d4ff;
    font-weight: bold;
}

.list-item .item-actions {
    padding: 20px;
    display: flex;
    align-items: center;
}

.btn-primary {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #fff;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #ccc;
    border: 1px solid #333;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.btn-secondary:hover {
    background: #333;
    color: #00d4ff;
}

/* 分页样式 */
.pagination-wrapper {
    margin-top: 40px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    gap: 5px;
    align-items: center;
}

.pagination a,
.pagination span {
    padding: 10px 15px;
    border: 1px solid #333;
    color: #ccc;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.pagination a:hover {
    background: #00d4ff;
    color: #fff;
    border-color: #00d4ff;
}

.pagination .current {
    background: #00d4ff;
    color: #fff;
    border-color: #00d4ff;
}

/* 相关分类链接 */
.category-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.category-link {
    padding: 8px 16px;
    border: 1px solid #333;
    border-radius: 20px;
    color: #ccc;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.category-link.active,
.category-link:hover {
    background: #00d4ff;
    color: #fff;
    border-color: #00d4ff;
}

/* 标签云 */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-item {
    padding: 5px 12px;
    background: #333;
    color: #ccc;
    text-decoration: none;
    border-radius: 15px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.tag-item:hover {
    background: #00d4ff;
    color: #fff;
}

/* 广告区域 */
.ad-section {
    text-align: center;
}

.ad-banner img {
    width: 100%;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.ad-banner img:hover {
    transform: scale(1.02);
}

/* 文章详情页样式 */
.article-detail {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.article-header {
    margin-bottom: 30px;
}

.article-category a {
    background: #00d4ff;
    color: #fff;
    padding: 6px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-block;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.article-category a:hover {
    background: #0099cc;
}

.article-title {
    color: #fff;
    font-size: 2.5rem;
    line-height: 1.3;
    margin-bottom: 20px;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #333;
    border-bottom: 1px solid #333;
}

.meta-left {
    display: flex;
    gap: 30px;
    color: #ccc;
}

.meta-left span {
    display: flex;
    align-items: center;
    gap: 8px;
}

.meta-left i {
    color: #666;
}

.meta-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.article-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.action-btn {
    background: transparent;
    border: 1px solid #333;
    color: #ccc;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.action-btn:hover {
    background: #333;
    color: #00d4ff;
}

.rating {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ccc;
}

.stars {
    display: flex;
    gap: 2px;
}

.stars i {
    color: #333;
    transition: color 0.3s ease;
}

.stars i.active {
    color: #ffd700;
}

.stars i.half {
    color: #ffd700;
    opacity: 0.5;
}

/* 文章缩略图 */
.article-thumbnail {
    margin: 30px 0;
    text-align: center;
}

.article-thumbnail img {
    max-width: 100%;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 文章摘要 */
.article-summary {
    background: #333;
    padding: 25px;
    border-radius: 12px;
    margin: 30px 0;
    border-left: 4px solid #00d4ff;
}

.article-summary h3 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.article-summary p {
    color: #ccc;
    line-height: 1.6;
    font-size: 1.1rem;
}

/* 资源信息卡片 */
.resource-info-card {
    background: #333;
    border-radius: 12px;
    padding: 25px;
    margin: 30px 0;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-label {
    color: #666;
    font-weight: bold;
    min-width: 80px;
}

.info-value {
    color: #ccc;
    flex: 1;
}

.info-value.price {
    color: #00d4ff;
    font-weight: bold;
    font-size: 1.1rem;
}

.info-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

/* 下载区域 */
.download-section {
    margin: 40px 0;
}

.download-card {
    background: linear-gradient(135deg, #2d1810 0%, #4a2c1a 100%);
    border: 2px solid #8b4513;
    border-radius: 15px;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.download-info h3 {
    color: #ffd700;
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.download-info p {
    color: #ccc;
    margin-bottom: 15px;
}

.download-tips {
    color: #999;
    font-size: 0.9rem;
}

.download-tips p {
    margin: 5px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.download-tips i {
    color: #ffd700;
}

.download-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.btn-download {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #fff;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-download:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.btn-vip {
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    color: #000;
    text-decoration: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-vip:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

/* 文章正文 */
.article-content {
    color: #ccc;
    line-height: 1.8;
    font-size: 1.1rem;
    margin: 40px 0;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
    color: #fff;
    margin: 30px 0 20px 0;
}

.article-content p {
    margin-bottom: 20px;
}

.article-content img {
    max-width: 100%;
    border-radius: 8px;
    margin: 20px 0;
}

.article-content blockquote {
    background: #333;
    border-left: 4px solid #00d4ff;
    padding: 20px;
    margin: 20px 0;
    border-radius: 0 8px 8px 0;
}

.article-content code {
    background: #333;
    color: #00d4ff;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.article-content pre {
    background: #333;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 20px 0;
}

.article-content pre code {
    background: none;
    padding: 0;
}

/* 文章底部 */
.article-footer {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #333;
}

.article-tags {
    margin-bottom: 25px;
}

.tags-label {
    color: #666;
    margin-right: 15px;
}

.article-share {
    display: flex;
    align-items: center;
    gap: 15px;
}

.share-label {
    color: #666;
}

.share-buttons {
    display: flex;
    gap: 10px;
}

.share-btn {
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    color: #fff;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.share-btn.qq {
    background: #12b7f5;
}

.share-btn.wechat {
    background: #1aad19;
}

.share-btn.weibo {
    background: #e6162d;
}

.share-btn.copy {
    background: #666;
}

.share-btn:hover {
    transform: translateY(-2px);
    opacity: 0.9;
}

/* 相关推荐 */
.related-articles {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid #333;
}

.related-articles h3 {
    color: #fff;
    margin-bottom: 25px;
    font-size: 1.5rem;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 10px;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.related-item {
    display: flex;
    background: #333;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.related-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.related-image {
    width: 100px;
    height: 80px;
    flex-shrink: 0;
}

.related-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-content {
    padding: 15px;
    flex: 1;
}

.related-content h4 {
    margin-bottom: 10px;
}

.related-content h4 a {
    color: #fff;
    text-decoration: none;
    font-size: 0.95rem;
    line-height: 1.4;
    transition: color 0.3s ease;
}

.related-content h4 a:hover {
    color: #00d4ff;
}

.related-meta {
    display: flex;
    gap: 15px;
    color: #666;
    font-size: 0.85rem;
}

/* 评论区域 */
.comments-section {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid #333;
}

.comments-section h3 {
    color: #fff;
    margin-bottom: 25px;
    font-size: 1.5rem;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 10px;
}

.comment-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group textarea {
    width: 100%;
    background: #333;
    border: 1px solid #555;
    border-radius: 8px;
    padding: 15px;
    color: #ccc;
    resize: vertical;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
}

.form-group textarea:focus {
    outline: none;
    border-color: #00d4ff;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2);
}

.form-group textarea::placeholder {
    color: #666;
}

.form-actions {
    text-align: right;
}

.btn-submit {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #fff;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.comment-item {
    display: flex;
    gap: 15px;
    padding: 20px;
    background: #333;
    border-radius: 10px;
}

.comment-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.comment-author {
    color: #00d4ff;
    font-weight: bold;
}

.comment-time {
    color: #666;
    font-size: 0.9rem;
}

.comment-text {
    color: #ccc;
    line-height: 1.6;
}

/* 作者信息 */
.author-info {
    background: linear-gradient(135deg, #2d1810 0%, #4a2c1a 100%);
    border: 1px solid #8b4513;
}

.author-info h3 {
    border-bottom-color: #ffd700;
    color: #ffd700;
}

.author-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.author-avatar {
    margin-bottom: 15px;
}

.author-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 3px solid #ffd700;
}

.author-details h4 {
    color: #ffd700;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.author-details p {
    color: #ccc;
    margin-bottom: 15px;
    line-height: 1.5;
}

.author-stats {
    display: flex;
    gap: 20px;
    color: #999;
    font-size: 0.9rem;
}

/* 最新评论预览 */
.recent-comments {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.comment-preview {
    padding: 15px;
    background: #333;
    border-radius: 8px;
    border-left: 3px solid #00d4ff;
}

.comment-user {
    color: #00d4ff;
    font-weight: bold;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.comment-content {
    color: #ccc;
    margin-bottom: 8px;
    line-height: 1.4;
    font-size: 0.9rem;
}

.comment-time {
    color: #666;
    font-size: 0.8rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .article-detail {
        padding: 20px;
    }

    .article-title {
        font-size: 1.8rem;
    }

    .article-meta {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .meta-left {
        flex-direction: column;
        gap: 10px;
    }

    .download-card {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .download-actions {
        width: 100%;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .related-grid {
        grid-template-columns: 1fr;
    }

    .related-item {
        flex-direction: column;
    }

    .related-image {
        width: 100%;
        height: 150px;
    }

    .share-buttons {
        flex-wrap: wrap;
    }

    .article-share {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
