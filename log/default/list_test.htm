<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{dede:field.typename/} - {dede:global.cfg_webname/}</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #0a0a0a; 
            color: #fff; 
            margin: 0; 
            padding: 20px; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
        }
        .header { 
            background: #1a1a1a; 
            padding: 20px; 
            border-radius: 10px; 
            margin-bottom: 20px; 
        }
        .content { 
            background: #1a1a1a; 
            padding: 20px; 
            border-radius: 10px; 
        }
        .article-item { 
            background: #333; 
            padding: 15px; 
            margin-bottom: 15px; 
            border-radius: 5px; 
        }
        .article-item h3 { 
            margin-bottom: 10px; 
        }
        .article-item a { 
            color: #00d4ff; 
            text-decoration: none; 
        }
        .article-item a:hover { 
            color: #0099cc; 
        }
        .meta { 
            color: #666; 
            font-size: 0.9rem; 
        }
        .pagination { 
            text-align: center; 
            margin-top: 20px; 
        }
        .pagination a, .pagination span { 
            display: inline-block; 
            padding: 8px 12px; 
            margin: 0 2px; 
            background: #333; 
            color: #ccc; 
            text-decoration: none; 
            border-radius: 3px; 
        }
        .pagination a:hover { 
            background: #00d4ff; 
            color: #fff; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{dede:field.typename/}</h1>
            <p>{dede:field.description/}</p>
            <div>共 {dede:list.total/} 篇文章</div>
        </div>
        
        <div class="content">
            {dede:list pagesize='20'}
            <div class="article-item">
                <h3><a href="[field:arcurl/]">[field:title/]</a></h3>
                <p>[field:description/]</p>
                <div class="meta">
                    发布时间：[field:pubdate function='strftime("%Y-%m-%d",@me)'/] | 
                    浏览次数：[field:click/] | 
                    作者：[field:writer/]
                </div>
            </div>
            {/dede:list}
            
            <div class="pagination">
                {dede:pagelist listsize='5'/}
            </div>
        </div>
    </div>
</body>
</html>
