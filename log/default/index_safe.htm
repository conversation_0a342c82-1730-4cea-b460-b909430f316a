{dede:include filename="head.htm"/}

<!-- 首页内容 -->
<div class="homepage">
    <!-- 网站横幅 -->
    <div class="site-banner">
        <div class="banner-content">
            <h1>{dede:global.cfg_webname/}</h1>
            <p>{dede:global.cfg_description/}</p>
            <div class="search-box">
                <form action="/plus/search.php" method="get">
                    <input type="text" name="keyword" placeholder="请输入关键词搜索" />
                    <button type="submit">搜索</button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-container">
        <div class="content-wrapper">
            <!-- 左侧内容 -->
            <div class="main-content">
                <!-- 最新文章 -->
                <section class="content-section">
                    <h2>最新文章</h2>
                    <div class="article-grid">
                        {dede:arclist row='12' titlelen='60' infolen='100' orderby='pubdate'}
                        <div class="article-card">
                            <div class="card-image">
                                <a href="[field:arcurl/]">
                                    <img src="[field:litpic/]" alt="[field:title/]" />
                                </a>
                            </div>
                            <div class="card-content">
                                <h3><a href="[field:arcurl/]">[field:title/]</a></h3>
                                <p>[field:description/]</p>
                                <div class="card-meta">
                                    <span>[field:pubdate function='strftime("%m-%d",@me)'/]</span>
                                    <span>[field:click/] 浏览</span>
                                </div>
                            </div>
                        </div>
                        {/dede:arclist}
                    </div>
                </section>
                
                <!-- 热门文章 -->
                <section class="content-section">
                    <h2>热门文章</h2>
                    <div class="article-list">
                        {dede:arclist row='10' titlelen='60' orderby='click'}
                        <div class="list-item">
                            <div class="item-thumb">
                                <a href="[field:arcurl/]">
                                    <img src="[field:litpic/]" alt="[field:title/]" />
                                </a>
                            </div>
                            <div class="item-content">
                                <h4><a href="[field:arcurl/]">[field:title/]</a></h4>
                                <div class="item-meta">
                                    <span>[field:pubdate function='strftime("%Y-%m-%d",@me)'/]</span>
                                    <span>[field:click/] 浏览</span>
                                </div>
                            </div>
                        </div>
                        {/dede:arclist}
                    </div>
                </section>
            </div>
            
            <!-- 右侧边栏 -->
            <div class="sidebar">
                <!-- 网站统计 -->
                <div class="sidebar-box">
                    <h3>网站统计</h3>
                    <div class="stats">
                        <div class="stat-item">
                            <span class="stat-number">1000+</span>
                            <span class="stat-label">文章总数</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">5000+</span>
                            <span class="stat-label">用户总数</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">10000+</span>
                            <span class="stat-label">总浏览量</span>
                        </div>
                    </div>
                </div>
                
                <!-- 栏目导航 -->
                <div class="sidebar-box">
                    <h3>栏目导航</h3>
                    <ul class="category-list">
                        {dede:channel type='top' row='10'}
                        <li><a href="[field:typeurl/]">[field:typename/]</a> <span>([field:count/])</span></li>
                        {/dede:channel}
                    </ul>
                </div>
                
                <!-- 推荐文章 -->
                <div class="sidebar-box">
                    <h3>推荐文章</h3>
                    <ul class="recommend-list">
                        {dede:arclist row='8' titlelen='40' flag='h'}
                        <li><a href="[field:arcurl/]">[field:title/]</a></li>
                        {/dede:arclist}
                    </ul>
                </div>
                
                <!-- 标签云 -->
                <div class="sidebar-box">
                    <h3>热门标签</h3>
                    <div class="tag-cloud">
                        {dede:tag sort='hot' getnum='15'}
                        <a href="[field:link/]">[field:tag/]</a>
                        {/dede:tag}
                    </div>
                </div>
                
                <!-- 友情链接 -->
                <div class="sidebar-box">
                    <h3>友情链接</h3>
                    <div class="friend-links">
                        {dede:flink row='10'}
                        <a href="[field:url/]" target="_blank">[field:webname/]</a>
                        {/dede:flink}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 基础样式 */
body {
    font-family: Arial, sans-serif;
    background: #0a0a0a;
    color: #fff;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.homepage {
    min-height: 100vh;
}

/* 网站横幅 */
.site-banner {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    padding: 60px 0;
    text-align: center;
    border-bottom: 1px solid #333;
}

.banner-content h1 {
    color: #00d4ff;
    font-size: 3rem;
    margin-bottom: 15px;
}

.banner-content p {
    color: #ccc;
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.search-box {
    max-width: 500px;
    margin: 0 auto;
    display: flex;
    gap: 10px;
}

.search-box input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    background: #333;
    color: #fff;
    font-size: 1rem;
}

.search-box button {
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    background: #00d4ff;
    color: #fff;
    cursor: pointer;
    font-size: 1rem;
}

.search-box button:hover {
    background: #0099cc;
}

/* 主要内容 */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
}

.content-wrapper {
    display: flex;
    gap: 30px;
}

.main-content {
    flex: 2;
}

.content-section {
    background: #1a1a1a;
    border-radius: 10px;
    padding: 30px;
    margin-bottom: 30px;
}

.content-section h2 {
    color: #00d4ff;
    margin-bottom: 25px;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 10px;
}

/* 文章网格 */
.article-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.article-card {
    background: #333;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s;
}

.article-card:hover {
    transform: translateY(-5px);
}

.card-image {
    height: 150px;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.card-content {
    padding: 15px;
}

.card-content h3 {
    margin-bottom: 10px;
    font-size: 1rem;
}

.card-content h3 a {
    color: #fff;
    text-decoration: none;
}

.card-content h3 a:hover {
    color: #00d4ff;
}

.card-content p {
    color: #ccc;
    font-size: 0.9rem;
    margin-bottom: 10px;
    line-height: 1.4;
}

.card-meta {
    color: #666;
    font-size: 0.8rem;
}

.card-meta span {
    margin-right: 15px;
}

/* 文章列表 */
.article-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.list-item {
    display: flex;
    background: #333;
    border-radius: 8px;
    overflow: hidden;
    padding: 15px;
}

.item-thumb {
    width: 80px;
    height: 60px;
    margin-right: 15px;
    flex-shrink: 0;
}

.item-thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
}

.item-content {
    flex: 1;
}

.item-content h4 {
    margin-bottom: 5px;
    font-size: 0.95rem;
}

.item-content h4 a {
    color: #fff;
    text-decoration: none;
}

.item-content h4 a:hover {
    color: #00d4ff;
}

.item-meta {
    color: #666;
    font-size: 0.8rem;
}

.item-meta span {
    margin-right: 15px;
}

/* 侧边栏 */
.sidebar {
    flex: 1;
}

.sidebar-box {
    background: #1a1a1a;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.sidebar-box h3 {
    color: #00d4ff;
    margin-bottom: 15px;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 5px;
}

/* 统计信息 */
.stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
}

.stat-number {
    color: #00d4ff;
    font-size: 1.5rem;
    font-weight: bold;
}

.stat-label {
    color: #ccc;
    font-size: 0.9rem;
}

/* 列表样式 */
.category-list, .recommend-list {
    list-style: none;
    padding: 0;
}

.category-list li, .recommend-list li {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #333;
}

.category-list li:last-child, .recommend-list li:last-child {
    border-bottom: none;
}

.category-list a, .recommend-list a {
    color: #ccc;
    text-decoration: none;
}

.category-list a:hover, .recommend-list a:hover {
    color: #00d4ff;
}

.category-list span {
    color: #666;
    font-size: 0.9rem;
}

/* 标签云 */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-cloud a {
    background: #333;
    color: #ccc;
    padding: 5px 10px;
    border-radius: 15px;
    text-decoration: none;
    font-size: 0.9rem;
}

.tag-cloud a:hover {
    background: #00d4ff;
    color: #fff;
}

/* 友情链接 */
.friend-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.friend-links a {
    background: #333;
    color: #ccc;
    padding: 5px 12px;
    border-radius: 15px;
    text-decoration: none;
    font-size: 0.9rem;
}

.friend-links a:hover {
    background: #00d4ff;
    color: #fff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .content-wrapper {
        flex-direction: column;
    }
    
    .banner-content h1 {
        font-size: 2rem;
    }
    
    .search-box {
        flex-direction: column;
    }
    
    .article-grid {
        grid-template-columns: 1fr;
    }
    
    .stats {
        grid-template-columns: 1fr;
    }
    
    .main-container {
        padding: 20px 10px;
    }
}
</style>

{dede:include filename="footer.htm"/}
