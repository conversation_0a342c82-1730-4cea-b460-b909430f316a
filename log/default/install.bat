@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   小仙元码模版 - Windows 安装脚本
echo ========================================
echo.

:: 检查是否在正确的目录
if not exist "templets" (
    echo ❌ 错误：未找到 templets 目录
    echo 请将此脚本放在 DedeCMS 根目录下运行
    echo.
    pause
    exit /b 1
)

:: 创建备份目录
set backup_dir=templets\default_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_dir=%backup_dir: =0%
echo 📁 创建备份目录: %backup_dir%
if not exist "%backup_dir%" mkdir "%backup_dir%"

:: 备份原文件
echo.
echo 💾 备份原模版文件...
if exist "templets\default\index.htm" (
    copy "templets\default\index.htm" "%backup_dir%\index.htm" >nul
    echo ✅ 备份 index.htm
)
if exist "templets\default\list_default.htm" (
    copy "templets\default\list_default.htm" "%backup_dir%\list_default.htm" >nul
    echo ✅ 备份 list_default.htm
)
if exist "templets\default\article_article.htm" (
    copy "templets\default\article_article.htm" "%backup_dir%\article_article.htm" >nul
    echo ✅ 备份 article_article.htm
)
if exist "templets\default\head.htm" (
    copy "templets\default\head.htm" "%backup_dir%\head.htm" >nul
    echo ✅ 备份 head.htm
)
if exist "templets\default\footer.htm" (
    copy "templets\default\footer.htm" "%backup_dir%\footer.htm" >nul
    echo ✅ 备份 footer.htm
)

:: 创建必要的目录
echo.
echo 📁 创建必要目录...
if not exist "templets\default\style" mkdir "templets\default\style"
if not exist "templets\default\js" mkdir "templets\default\js"
if not exist "templets\default\images" mkdir "templets\default\images"

:: 复制新模版文件
echo.
echo 📄 安装新模版文件...

:: 检查源文件是否存在
set source_dir=default
if not exist "%source_dir%\index_new.htm" (
    set source_dir=.
)
if not exist "%source_dir%\index_new.htm" (
    echo ❌ 错误：找不到模版源文件
    echo 请确保模版文件在当前目录或 default 子目录中
    echo.
    pause
    exit /b 1
)

:: 复制主要模版文件
if exist "%source_dir%\index_new.htm" (
    copy "%source_dir%\index_new.htm" "templets\default\index.htm" >nul
    echo ✅ 安装 index.htm
) else (
    echo ❌ 缺少 index_new.htm
)

if exist "%source_dir%\list_new.htm" (
    copy "%source_dir%\list_new.htm" "templets\default\list_default.htm" >nul
    echo ✅ 安装 list_default.htm
) else (
    echo ❌ 缺少 list_new.htm
)

if exist "%source_dir%\article_new.htm" (
    copy "%source_dir%\article_new.htm" "templets\default\article_article.htm" >nul
    echo ✅ 安装 article_article.htm
) else (
    echo ❌ 缺少 article_new.htm
)

if exist "%source_dir%\head_new.htm" (
    copy "%source_dir%\head_new.htm" "templets\default\head.htm" >nul
    echo ✅ 安装 head.htm
) else (
    echo ❌ 缺少 head_new.htm
)

if exist "%source_dir%\footer_new.htm" (
    copy "%source_dir%\footer_new.htm" "templets\default\footer.htm" >nul
    echo ✅ 安装 footer.htm
) else (
    echo ❌ 缺少 footer_new.htm
)

:: 复制样式文件
echo.
echo 🎨 安装样式文件...
if exist "%source_dir%\style\main.css" (
    copy "%source_dir%\style\main.css" "templets\default\style\main.css" >nul
    echo ✅ 安装 main.css
) else (
    echo ❌ 缺少 style\main.css
)

:: 复制脚本文件
echo.
echo ⚡ 安装脚本文件...
if exist "%source_dir%\js\main.js" (
    copy "%source_dir%\js\main.js" "templets\default\js\main.js" >nul
    echo ✅ 安装 main.js
) else (
    echo ❌ 缺少 js\main.js
)

:: 复制图片文件
echo.
echo 🖼️ 安装图片文件...
if exist "%source_dir%\images\logo.svg" (
    copy "%source_dir%\images\logo.svg" "templets\default\images\logo.png" >nul
    echo ✅ 安装 logo.png
)
if exist "%source_dir%\images\banner.svg" (
    copy "%source_dir%\images\banner.svg" "templets\default\images\banner.jpg" >nul
    echo ✅ 安装 banner.jpg
)
if exist "%source_dir%\images\placeholder.svg" (
    copy "%source_dir%\images\placeholder.svg" "templets\default\images\placeholder.jpg" >nul
    echo ✅ 安装 placeholder.jpg
)

:: 安装完成
echo.
echo ========================================
echo   🎉 模版安装完成！
echo ========================================
echo.
echo 📝 后续步骤：
echo 1. 登录 DedeCMS 后台
echo 2. 进入"生成" → "更新主页HTML"
echo 3. 进入"生成" → "更新栏目HTML"  
echo 4. 进入"生成" → "更新文档HTML"
echo 5. 清除浏览器缓存，查看效果
echo.
echo 💾 原文件已备份到: %backup_dir%
echo.
echo 如有问题，请查看 README.md 文档
echo.
pause
