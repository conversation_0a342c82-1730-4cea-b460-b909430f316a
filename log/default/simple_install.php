<?php
/**
 * 小仙元码模版简化安装脚本
 * 如果主安装脚本有问题，请使用此脚本
 */

// 防止直接访问
if (!isset($_GET['install']) && !isset($_POST['install'])) {
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>小仙元码模版 - 简化安装</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                background: #1a1a1a; 
                color: #fff; 
                padding: 20px; 
                line-height: 1.6;
            }
            .container { 
                max-width: 800px; 
                margin: 0 auto; 
                background: #2d2d2d; 
                padding: 30px; 
                border-radius: 10px; 
            }
            .btn { 
                background: #00d4ff; 
                color: #fff; 
                padding: 12px 24px; 
                border: none; 
                border-radius: 5px; 
                cursor: pointer; 
                text-decoration: none; 
                display: inline-block; 
                margin: 10px 5px;
            }
            .btn:hover { background: #0099cc; }
            .success { color: #4CAF50; }
            .error { color: #f44336; }
            .warning { color: #ff9800; }
            pre { background: #333; padding: 15px; border-radius: 5px; overflow-x: auto; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎮 小仙元码模版 - 简化安装程序</h1>
            <p>这是一个简化版的安装程序，如果主安装程序遇到问题，可以使用此脚本。</p>
            
            <h2>📋 安装前准备</h2>
            <ol>
                <li>确保您有DedeCMS网站的管理权限</li>
                <li>确保 <code>/templets/default/</code> 目录存在且可写</li>
                <li>建议先备份原有模版文件</li>
            </ol>
            
            <h2>🚀 开始安装</h2>
            <p>点击下面的按钮开始安装模版：</p>
            <a href="?install=1" class="btn">开始安装模版</a>
            
            <h2>📖 手动安装说明</h2>
            <p>如果自动安装失败，您可以手动安装：</p>
            <pre>
1. 将以下文件复制到 /templets/default/ 目录：
   - index_new.htm → index.htm
   - list_new.htm → list_default.htm  
   - article_new.htm → article_article.htm
   - head_new.htm → head.htm
   - footer_new.htm → footer.htm

2. 将 style/main.css 复制到 /templets/default/style/ 目录

3. 将 js/main.js 复制到 /templets/default/js/ 目录

4. 将 images/ 目录下的文件复制到 /templets/default/images/ 目录

5. 在DedeCMS后台更新网站HTML
            </pre>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 执行安装
if (isset($_GET['install']) || isset($_POST['install'])) {
    echo '<!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>安装进度</title>
        <style>
            body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; padding: 20px; }
            .container { max-width: 800px; margin: 0 auto; background: #2d2d2d; padding: 30px; border-radius: 10px; }
            .success { color: #4CAF50; }
            .error { color: #f44336; }
            .warning { color: #ff9800; }
            .step { margin: 10px 0; padding: 10px; background: #333; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔧 模版安装进度</h1>';
    
    $success_count = 0;
    $error_count = 0;
    
    // 定义文件映射
    $files = [
        'index_new.htm' => 'index.htm',
        'list_new.htm' => 'list_default.htm',
        'article_new.htm' => 'article_article.htm',
        'head_new.htm' => 'head.htm',
        'footer_new.htm' => 'footer.htm'
    ];
    
    // 检查并创建目录
    $dirs_to_create = [
        './templets/',
        './templets/default/',
        './templets/default/style/',
        './templets/default/js/',
        './templets/default/images/'
    ];
    
    echo '<div class="step"><h3>📁 检查和创建目录</h3>';
    foreach ($dirs_to_create as $dir) {
        if (!is_dir($dir)) {
            if (@mkdir($dir, 0755, true)) {
                echo "<p class='success'>✅ 创建目录: $dir</p>";
            } else {
                echo "<p class='error'>❌ 无法创建目录: $dir</p>";
                $error_count++;
            }
        } else {
            echo "<p class='success'>✅ 目录已存在: $dir</p>";
        }
    }
    echo '</div>';
    
    // 复制主要模版文件
    echo '<div class="step"><h3>📄 复制模版文件</h3>';
    foreach ($files as $source => $target) {
        $source_path = "./$source";
        $target_path = "./templets/default/$target";
        
        if (file_exists($source_path)) {
            if (copy($source_path, $target_path)) {
                echo "<p class='success'>✅ 复制成功: $source → $target</p>";
                $success_count++;
            } else {
                echo "<p class='error'>❌ 复制失败: $source → $target</p>";
                $error_count++;
            }
        } else {
            echo "<p class='warning'>⚠️ 源文件不存在: $source</p>";
            $error_count++;
        }
    }
    echo '</div>';
    
    // 复制CSS文件
    echo '<div class="step"><h3>🎨 复制样式文件</h3>';
    if (file_exists('./style/main.css')) {
        if (copy('./style/main.css', './templets/default/style/main.css')) {
            echo "<p class='success'>✅ 复制CSS文件成功</p>";
            $success_count++;
        } else {
            echo "<p class='error'>❌ 复制CSS文件失败</p>";
            $error_count++;
        }
    } else {
        echo "<p class='warning'>⚠️ CSS文件不存在: ./style/main.css</p>";
    }
    echo '</div>';
    
    // 复制JS文件
    echo '<div class="step"><h3>⚡ 复制脚本文件</h3>';
    if (file_exists('./js/main.js')) {
        if (copy('./js/main.js', './templets/default/js/main.js')) {
            echo "<p class='success'>✅ 复制JS文件成功</p>";
            $success_count++;
        } else {
            echo "<p class='error'>❌ 复制JS文件失败</p>";
            $error_count++;
        }
    } else {
        echo "<p class='warning'>⚠️ JS文件不存在: ./js/main.js</p>";
    }
    echo '</div>';
    
    // 复制图片文件
    echo '<div class="step"><h3>🖼️ 复制图片文件</h3>';
    $image_files = ['logo.svg', 'banner.svg', 'placeholder.svg'];
    foreach ($image_files as $img) {
        $source_path = "./images/$img";
        $target_path = "./templets/default/images/$img";
        
        if (file_exists($source_path)) {
            if (copy($source_path, $target_path)) {
                echo "<p class='success'>✅ 复制图片: $img</p>";
                $success_count++;
            } else {
                echo "<p class='error'>❌ 复制图片失败: $img</p>";
                $error_count++;
            }
        } else {
            // 创建占位符文件
            $placeholder_content = '<!-- 占位符文件 -->';
            if (file_put_contents($target_path, $placeholder_content)) {
                echo "<p class='warning'>⚠️ 创建占位符: $img</p>";
            }
        }
    }
    echo '</div>';
    
    // 显示安装结果
    echo '<div class="step"><h3>📊 安装结果</h3>';
    if ($error_count == 0) {
        echo '<p class="success">🎉 模版安装完成！所有文件都已成功复制。</p>';
        echo '<h4>📝 后续步骤：</h4>';
        echo '<ol>';
        echo '<li>登录DedeCMS后台</li>';
        echo '<li>进入"生成" → "更新主页HTML"</li>';
        echo '<li>进入"生成" → "更新栏目HTML"</li>';
        echo '<li>进入"生成" → "更新文档HTML"</li>';
        echo '<li>清除浏览器缓存，查看效果</li>';
        echo '</ol>';
    } else {
        echo "<p class='warning'>⚠️ 安装完成，但有 $error_count 个错误。</p>";
        echo '<p>请检查文件权限，或尝试手动复制文件。</p>';
    }
    
    echo "<p>成功: $success_count 个文件，错误: $error_count 个文件</p>";
    echo '</div>';
    
    echo '</div></body></html>';
}
?>
