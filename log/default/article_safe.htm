{dede:include filename="head.htm"/}

<!-- 面包屑导航 -->
<div class="breadcrumb">
    <a href="{dede:global.cfg_basehost/}">首页</a> &gt; 
    {dede:field name='position'/}
</div>

<!-- 文章内容 -->
<div class="article-container">
    <div class="article-main">
        <!-- 文章标题 -->
        <h1 class="article-title">{dede:field.title/}</h1>
        
        <!-- 文章信息 -->
        <div class="article-info">
            <span>发布时间：{dede:field.pubdate function='strftime("%Y-%m-%d %H:%M",@me)'/}</span>
            <span>浏览次数：{dede:field.click/}</span>
            <span>作者：{dede:field.writer/}</span>
        </div>
        
        <!-- 文章缩略图 -->
        {dede:field.litpic}
        <div class="article-pic">
            <img src="[field:litpic/]" alt="{dede:field.title/}" />
        </div>
        {/dede:field.litpic}
        
        <!-- 文章摘要 -->
        <div class="article-description">
            {dede:field.description/}
        </div>
        
        <!-- 文章正文 -->
        <div class="article-body">
            {dede:field.body/}
        </div>
        
        <!-- 文章标签 -->
        <div class="article-tags">
            <strong>标签：</strong>
            {dede:field.keywords runphp='yes'}
            <?php
            $keywords = @me;
            if($keywords) {
                $tags = explode(',', $keywords);
                foreach($tags as $tag) {
                    $tag = trim($tag);
                    if($tag) {
                        echo '<a href="/tags.php?tag='.urlencode($tag).'">'.$tag.'</a> ';
                    }
                }
            }
            ?>
            {/dede:field.keywords}
        </div>
    </div>
    
    <!-- 侧边栏 -->
    <div class="article-sidebar">
        <!-- 相关文章 -->
        <div class="sidebar-box">
            <h3>相关文章</h3>
            <ul>
                {dede:likearticle row='8' titlelen='40'}
                <li><a href="[field:arcurl/]">[field:title/]</a></li>
                {/dede:likearticle}
            </ul>
        </div>
        
        <!-- 最新文章 -->
        <div class="sidebar-box">
            <h3>最新文章</h3>
            <ul>
                {dede:arclist row='8' titlelen='40' orderby='pubdate'}
                <li><a href="[field:arcurl/]">[field:title/]</a></li>
                {/dede:arclist}
            </ul>
        </div>
        
        <!-- 热门文章 -->
        <div class="sidebar-box">
            <h3>热门文章</h3>
            <ul>
                {dede:arclist row='8' titlelen='40' orderby='click'}
                <li><a href="[field:arcurl/]">[field:title/]</a></li>
                {/dede:arclist}
            </ul>
        </div>
    </div>
</div>

<!-- 评论区域 -->
<div class="comments-section">
    <h3>用户评论</h3>
    {dede:feedback}
    <div class="comment-item">
        <strong>[field:username/]</strong>
        <span class="comment-time">[field:dtime function='strftime("%Y-%m-%d %H:%M",@me)'/]</span>
        <div class="comment-content">[field:msg/]</div>
    </div>
    {/dede:feedback}
    
    <!-- 评论表单 -->
    <form action="/plus/feedback_ajax.php" method="post">
        <input type="hidden" name="action" value="send">
        <input type="hidden" name="aid" value="{dede:field.id/}">
        <div>
            <label>姓名：</label>
            <input type="text" name="username" required>
        </div>
        <div>
            <label>评论内容：</label>
            <textarea name="msg" rows="4" required></textarea>
        </div>
        <div>
            <button type="submit">发表评论</button>
        </div>
    </form>
</div>

<style>
/* 基础样式 */
body {
    font-family: Arial, sans-serif;
    background: #0a0a0a;
    color: #fff;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.breadcrumb {
    background: #1a1a1a;
    padding: 10px 20px;
    border-bottom: 1px solid #333;
}

.breadcrumb a {
    color: #00d4ff;
    text-decoration: none;
}

.article-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    gap: 30px;
}

.article-main {
    flex: 2;
    background: #1a1a1a;
    padding: 30px;
    border-radius: 10px;
}

.article-title {
    color: #fff;
    font-size: 2rem;
    margin-bottom: 20px;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 10px;
}

.article-info {
    color: #ccc;
    margin-bottom: 20px;
    padding: 10px 0;
    border-bottom: 1px solid #333;
}

.article-info span {
    margin-right: 20px;
}

.article-pic {
    text-align: center;
    margin: 20px 0;
}

.article-pic img {
    max-width: 100%;
    border-radius: 8px;
}

.article-description {
    background: #333;
    padding: 15px;
    border-left: 4px solid #00d4ff;
    margin: 20px 0;
    border-radius: 5px;
}

.article-body {
    color: #ccc;
    line-height: 1.8;
    margin: 30px 0;
}

.article-body img {
    max-width: 100%;
    height: auto;
    border-radius: 5px;
    margin: 10px 0;
}

.article-tags {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #333;
}

.article-tags a {
    background: #333;
    color: #00d4ff;
    padding: 5px 10px;
    border-radius: 15px;
    text-decoration: none;
    margin-right: 10px;
    font-size: 0.9rem;
}

.article-tags a:hover {
    background: #00d4ff;
    color: #fff;
}

.article-sidebar {
    flex: 1;
}

.sidebar-box {
    background: #1a1a1a;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.sidebar-box h3 {
    color: #00d4ff;
    margin-bottom: 15px;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 5px;
}

.sidebar-box ul {
    list-style: none;
    padding: 0;
}

.sidebar-box li {
    margin-bottom: 8px;
    padding: 5px 0;
    border-bottom: 1px solid #333;
}

.sidebar-box li:last-child {
    border-bottom: none;
}

.sidebar-box a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.sidebar-box a:hover {
    color: #00d4ff;
}

.comments-section {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
    background: #1a1a1a;
    border-radius: 10px;
    padding: 30px;
}

.comments-section h3 {
    color: #00d4ff;
    margin-bottom: 20px;
    border-bottom: 2px solid #00d4ff;
    padding-bottom: 10px;
}

.comment-item {
    background: #333;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.comment-time {
    color: #666;
    font-size: 0.9rem;
    float: right;
}

.comment-content {
    margin-top: 10px;
    color: #ccc;
}

form {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #333;
}

form div {
    margin-bottom: 15px;
}

form label {
    display: block;
    margin-bottom: 5px;
    color: #ccc;
}

form input, form textarea {
    width: 100%;
    padding: 10px;
    background: #333;
    border: 1px solid #555;
    border-radius: 5px;
    color: #fff;
}

form button {
    background: #00d4ff;
    color: #fff;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

form button:hover {
    background: #0099cc;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .article-container {
        flex-direction: column;
        padding: 10px;
    }
    
    .article-main {
        padding: 20px;
    }
    
    .article-title {
        font-size: 1.5rem;
    }
}
</style>

{dede:include filename="footer.htm"/}
