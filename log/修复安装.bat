@echo off
chcp 65001 >nul
title 修复DedeCMS模版SQL错误
color 0C

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔧 修复DedeCMS模版SQL错误                  ║
echo ║                   解决 MariaDB 语法错误问题                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 当前目录: %CD%
echo.

:: 检查DedeCMS
if not exist "include\common.inc.php" (
    echo ❌ 错误：未检测到DedeCMS
    pause
    exit /b 1
)
echo ✅ 检测到DedeCMS环境

:: 检查源文件
echo.
echo 🔍 检查修复后的源文件...
if not exist "default\article_new.htm" (
    echo ❌ 缺少修复后的文件: default\article_new.htm
    pause
    exit /b 1
)
echo ✅ 找到修复后的文件

echo.
echo 🚀 开始修复安装...
echo.

:: 创建必要的目录
echo 📁 创建目录结构...
if not exist "templets\default" mkdir "templets\default" 2>nul
if not exist "templets\default\style" mkdir "templets\default\style" 2>nul
if not exist "templets\default\js" mkdir "templets\default\js" 2>nul
if not exist "templets\default\images" mkdir "templets\default\images" 2>nul
echo ✅ 目录创建完成

:: 复制修复后的模版文件
echo.
echo 📄 安装修复后的模版文件...

copy "default\index_new.htm" "templets\default\index.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 index.htm 失败
) else (
    echo ✅ 安装 index.htm
)

copy "default\list_new.htm" "templets\default\list_default.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 list_default.htm 失败
) else (
    echo ✅ 安装 list_default.htm
)

copy "default\article_new.htm" "templets\default\article_article.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 article_article.htm 失败
) else (
    echo ✅ 安装 article_article.htm (已修复SQL错误)
)

copy "default\head_new.htm" "templets\default\head.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 head.htm 失败
) else (
    echo ✅ 安装 head.htm
)

copy "default\footer_new.htm" "templets\default\footer.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 footer.htm 失败
) else (
    echo ✅ 安装 footer.htm
)

:: 复制样式和脚本
echo.
echo 🎨 安装样式和脚本文件...
copy "default\style\main.css" "templets\default\style\main.css" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 main.css 失败
) else (
    echo ✅ 安装 main.css
)

copy "default\js\main.js" "templets\default\js\main.js" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 main.js 失败
) else (
    echo ✅ 安装 main.js
)

:: 创建图片占位符
echo.
echo 🖼️  创建图片占位符...
echo ^<!-- Logo占位符 --^> > "templets\default\images\logo.png"
echo ✅ 创建 logo.png 占位符

echo ^<!-- Banner占位符 --^> > "templets\default\images\banner.jpg"
echo ✅ 创建 banner.jpg 占位符

echo ^<!-- 占位符图片 --^> > "templets\default\images\placeholder.jpg"
echo ✅ 创建 placeholder.jpg 占位符

echo ^<!-- 头像占位符 --^> > "templets\default\images\avatar.png"
echo ✅ 创建 avatar.png 占位符

echo ^<!-- QQ二维码占位符 --^> > "templets\default\images\qr-qq.png"
echo ✅ 创建 qr-qq.png 占位符

echo ^<!-- 微信二维码占位符 --^> > "templets\default\images\qr-wechat.png"
echo ✅ 创建 qr-wechat.png 占位符

echo ^<!-- 广告横幅占位符 --^> > "templets\default\images\ad-banner.jpg"
echo ✅ 创建 ad-banner.jpg 占位符

:: 修复完成
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🎉 修复完成！                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔧 已修复的问题：
echo    ✅ 修复了 typeid='{dede:field.typeid/}' 的SQL语法错误
echo    ✅ 改为使用 typeid='[field:typeid/]' 语法
echo    ✅ 创建了必要的图片占位符文件
echo.
echo 📝 后续步骤：
echo    1. 登录 DedeCMS 后台 (通常是 /dede/)
echo    2. 进入"生成" → "更新主页HTML"
echo    3. 进入"生成" → "更新栏目HTML"  
echo    4. 进入"生成" → "更新文档HTML"
echo    5. 清除浏览器缓存，查看效果
echo.
echo 🌐 现在可以正常生成HTML文件了！
echo.
pause
