@echo off
chcp 65001 >nul
title 小仙元码模版 - 快速安装
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    小仙元码模版 - 快速安装                    ║
echo ║                  基于 https://wd.xxymw.com 样式制作           ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 检查当前目录
echo 📍 当前目录: %CD%
echo.

:: 检查DedeCMS
if not exist "include\common.inc.php" (
    echo ❌ 错误：未检测到DedeCMS
    echo    请确保在DedeCMS根目录下运行此脚本
    echo.
    pause
    exit /b 1
)
echo ✅ 检测到DedeCMS环境

:: 检查源文件
echo.
echo 🔍 检查源文件...
set missing_files=0

if not exist "default\index_new.htm" (
    echo ❌ 缺少: default\index_new.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\index_new.htm
)

if not exist "default\list_new.htm" (
    echo ❌ 缺少: default\list_new.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\list_new.htm
)

if not exist "default\article_new.htm" (
    echo ❌ 缺少: default\article_new.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\article_new.htm
)

if not exist "default\head_new.htm" (
    echo ❌ 缺少: default\head_new.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\head_new.htm
)

if not exist "default\footer_new.htm" (
    echo ❌ 缺少: default\footer_new.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\footer_new.htm
)

if not exist "default\style\main.css" (
    echo ❌ 缺少: default\style\main.css
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\style\main.css
)

if not exist "default\js\main.js" (
    echo ❌ 缺少: default\js\main.js
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\js\main.js
)

if %missing_files% gtr 0 (
    echo.
    echo ❌ 发现 %missing_files% 个文件缺失，无法继续安装
    echo    请检查文件是否完整上传
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 所有源文件检查完毕，可以开始安装
echo.

:: 询问是否继续
set /p confirm="是否继续安装？(Y/N): "
if /i not "%confirm%"=="Y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始安装模版...
echo.

:: 创建备份目录
set backup_dir=templets\default_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_dir=%backup_dir: =0%
echo 📁 创建备份目录: %backup_dir%
if not exist "%backup_dir%" mkdir "%backup_dir%" 2>nul

:: 备份原文件
echo.
echo 💾 备份原模版文件...
if exist "templets\default\index.htm" (
    copy "templets\default\index.htm" "%backup_dir%\index.htm" >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  备份 index.htm 失败
    ) else (
        echo ✅ 备份 index.htm
    )
)

if exist "templets\default\list_default.htm" (
    copy "templets\default\list_default.htm" "%backup_dir%\list_default.htm" >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  备份 list_default.htm 失败
    ) else (
        echo ✅ 备份 list_default.htm
    )
)

if exist "templets\default\article_article.htm" (
    copy "templets\default\article_article.htm" "%backup_dir%\article_article.htm" >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  备份 article_article.htm 失败
    ) else (
        echo ✅ 备份 article_article.htm
    )
)

if exist "templets\default\head.htm" (
    copy "templets\default\head.htm" "%backup_dir%\head.htm" >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  备份 head.htm 失败
    ) else (
        echo ✅ 备份 head.htm
    )
)

if exist "templets\default\footer.htm" (
    copy "templets\default\footer.htm" "%backup_dir%\footer.htm" >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  备份 footer.htm 失败
    ) else (
        echo ✅ 备份 footer.htm
    )
)

:: 创建必要的目录
echo.
echo 📁 创建必要目录...
if not exist "templets\default" mkdir "templets\default" 2>nul
if not exist "templets\default\style" mkdir "templets\default\style" 2>nul
if not exist "templets\default\js" mkdir "templets\default\js" 2>nul
if not exist "templets\default\images" mkdir "templets\default\images" 2>nul
echo ✅ 目录创建完成

:: 复制新模版文件
echo.
echo 📄 安装新模版文件...

copy "default\index_new.htm" "templets\default\index.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 index.htm 失败
) else (
    echo ✅ 安装 index.htm
)

copy "default\list_new.htm" "templets\default\list_default.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 list_default.htm 失败
) else (
    echo ✅ 安装 list_default.htm
)

copy "default\article_new.htm" "templets\default\article_article.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 article_article.htm 失败
) else (
    echo ✅ 安装 article_article.htm
)

copy "default\head_new.htm" "templets\default\head.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 head.htm 失败
) else (
    echo ✅ 安装 head.htm
)

copy "default\footer_new.htm" "templets\default\footer.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 footer.htm 失败
) else (
    echo ✅ 安装 footer.htm
)

:: 复制样式文件
echo.
echo 🎨 安装样式文件...
copy "default\style\main.css" "templets\default\style\main.css" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 main.css 失败
) else (
    echo ✅ 安装 main.css
)

:: 复制脚本文件
echo.
echo ⚡ 安装脚本文件...
copy "default\js\main.js" "templets\default\js\main.js" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 main.js 失败
) else (
    echo ✅ 安装 main.js
)

:: 复制图片文件（如果存在）
echo.
echo 🖼️  安装图片文件...
if exist "default\images\logo.svg" (
    copy "default\images\logo.svg" "templets\default\images\logo.png" >nul 2>&1
    if errorlevel 1 (
        echo ❌ 安装 logo.png 失败
    ) else (
        echo ✅ 安装 logo.png
    )
) else (
    echo "<!-- Logo占位符 -->" > "templets\default\images\logo.png"
    echo ⚠️  创建 logo.png 占位符
)

if exist "default\images\banner.svg" (
    copy "default\images\banner.svg" "templets\default\images\banner.jpg" >nul 2>&1
    if errorlevel 1 (
        echo ❌ 安装 banner.jpg 失败
    ) else (
        echo ✅ 安装 banner.jpg
    )
) else (
    echo "<!-- Banner占位符 -->" > "templets\default\images\banner.jpg"
    echo ⚠️  创建 banner.jpg 占位符
)

if exist "default\images\placeholder.svg" (
    copy "default\images\placeholder.svg" "templets\default\images\placeholder.jpg" >nul 2>&1
    if errorlevel 1 (
        echo ❌ 安装 placeholder.jpg 失败
    ) else (
        echo ✅ 安装 placeholder.jpg
    )
) else (
    echo "<!-- 占位符 -->" > "templets\default\images\placeholder.jpg"
    echo ⚠️  创建 placeholder.jpg 占位符
)

:: 安装完成
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🎉 安装完成！                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 📝 后续步骤：
echo    1. 登录 DedeCMS 后台 (通常是 /dede/)
echo    2. 进入"生成" → "更新主页HTML"
echo    3. 进入"生成" → "更新栏目HTML"  
echo    4. 进入"生成" → "更新文档HTML"
echo    5. 清除浏览器缓存，查看效果
echo.
echo 💾 原文件已备份到: %backup_dir%
echo.
echo 🌐 如需访问网站，请在浏览器中打开您的网站地址
echo.
echo 如有问题，请查看 default\README.md 文档
echo.
pause
