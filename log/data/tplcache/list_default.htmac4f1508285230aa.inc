<?php
$z[0]=Array("include","",0,39);
$z[0][4]=array();
$z[0][4]['filename']="head_new.htm";
$z[1]=Array("global","",205,232);
$z[1][4]=array();
$z[1][4]['name']="cfg_basehost";
$z[2]=Array("field","",310,339);
$z[2][4]=array();
$z[2][4]['name']="position";
$z[3]=Array("field","",803,825);
$z[3][4]=array();
$z[3][4]['name']="typename";
$z[4]=Array("field","",884,909);
$z[4][4]=array();
$z[4][4]['name']="description";
$z[5]=Array("list","",1136,1154);
$z[5][4]=array();
$z[5][4]['name']="total";
$z[6]=Array("field","",1374,1393);
$z[6][4]=array();
$z[6][4]['name']="click";
$z[7]=Array("list","\n                            <div class=\"resource-card\">\n                                <div class=\"card-image\">\n                                    <img src=\"[field:litpic/]\" alt=\"[field:title/]\" loading=\"lazy\">\n                                    <div class=\"card-category\">{dede:field.typename/}</div>\n                                    {dede:field.flag function=\"GetFlag(@me,'h')\" runphp=\"yes\"}\n                                    [field:flag]\n                                    {/dede:field.flag}\n                                </div>\n                                <div class=\"card-content\">\n                                    <h3><a href=\"[field:arcurl/]\">[field:title function='cn_substr(@me,60)'/]</a></h3>\n                                    <p class=\"card-desc\">[field:description function='cn_substr(@me,100)'/]</p>\n                                    <div class=\"card-meta\">\n                                        <span class=\"time\">\n                                            <i class=\"fas fa-clock\"></i>\n                                            [field:pubdate function='strftime(\"%Y-%m-%d\",@me)'/]\n                                        </span>\n                                        <span class=\"views\">\n                                            <i class=\"fas fa-eye\"></i>\n                                            [field:click/]\n                                        </span>\n                                        <span class=\"comments\">\n                                            <i class=\"fas fa-comment\"></i>\n                                            0\n                                        </span>\n                                        <span class=\"downloads\">\n                                            <i class=\"fas fa-download\"></i>\n                                            [field:click/]\n                                        </span>\n                                        <span class=\"price\">300</span>\n                                    </div>\n                                    <div class=\"card-actions\">\n                                        <a href=\"[field:arcurl/]\" class=\"btn-primary\">查看详情</a>\n                                        <button class=\"btn-secondary\" onclick=\"addToFavorites([field:id/])\">\n                                            <i class=\"fas fa-heart\"></i>\n                                        </button>\n                                    </div>\n                                </div>\n                            </div>\n                            ",2867,5453);
$z[7][4]=array();
$z[7][4]['pagesize']="20";
$z[8]=Array("list","\n                            <div class=\"list-item\">\n                                <div class=\"item-image\">\n                                    <img src=\"[field:litpic/]\" alt=\"[field:title/]\" loading=\"lazy\">\n                                </div>\n                                <div class=\"item-content\">\n                                    <h3><a href=\"[field:arcurl/]\">[field:title/]</a></h3>\n                                    <p class=\"item-desc\">[field:description function='cn_substr(@me,200)'/]</p>\n                                    <div class=\"item-meta\">\n                                        <span class=\"category\">{dede:field.typename/}</span>\n                                        <span class=\"time\">[field:pubdate function='strftime(\"%Y-%m-%d\",@me)'/]</span>\n                                        <span class=\"views\">[field:click/] 浏览</span>\n                                        <span class=\"price\">300 积分</span>\n                                    </div>\n                                </div>\n                                <div class=\"item-actions\">\n                                    <a href=\"[field:arcurl/]\" class=\"btn-primary\">立即下载</a>\n                                </div>\n                            </div>\n                            ",5658,6986);
$z[8][4]=array();
$z[8][4]['pagesize']="20";
$z[9]=Array("pagelist","",7144,7173);
$z[9][4]=array();
$z[9][4]['listsize']="5";
$z[10]=Array("arclist","\n                            <div class=\"simple-item\">\n                                <span class=\"item-number\">{dede:global.autoindex/}</span>\n                                <a href=\"[field:arcurl/]\">[field:title/]</a>\n                                <span class=\"item-count\">[field:click/]</span>\n                            </div>\n                            ",7569,8000);
$z[10][4]=array();
$z[10][4]['row']="8";
$z[10][4]['titlelen']="30";
$z[10][4]['orderby']="click";
$z[11]=Array("arclist","\n                            <div class=\"sidebar-item\">\n                                <div class=\"item-image\">\n                                    <img src=\"[field:litpic/]\" alt=\"[field:title/]\">\n                                </div>\n                                <div class=\"item-content\">\n                                    <h4><a href=\"[field:arcurl/]\">[field:title/]</a></h4>\n                                    <div class=\"item-meta\">\n                                        <span class=\"time\">[field:pubdate function='strftime(\"%m-%d\",@me)'/]</span>\n                                        <span class=\"views\">[field:click/]</span>\n                                    </div>\n                                </div>\n                            </div>\n                            ",8285,9143);
$z[11][4]=array();
$z[11][4]['row']="6";
$z[11][4]['titlelen']="40";
$z[11][4]['orderby']="pubdate";
$z[12]=Array("channel","\n                            <a href=\"[field:typeurl/]\" class=\"category-link\">[field:typename/]</a>\n                            ",9430,9682);
$z[12][4]=array();
$z[12][4]['type']="brother";
$z[12][4]['currentstyle']="<a href=\"~typelink~\" class=\"category-link active\">~typename~</a>";
$z[13]=Array("tag","\n                            <a href=\"[field:link/]\" class=\"tag-item\">[field:tag/]</a>\n                            ",9961,10120);
$z[13][4]=array();
$z[13][4]['sort']="hot";
$z[13][4]['getnum']="20";
$z[14]=Array("global","",10374,10406);
$z[14][4]=array();
$z[14][4]['name']="cfg_templets_skin";
$z[15]=Array("include","",10577,10618);
$z[15][4]=array();
$z[15][4]['filename']="footer_new.htm";

?>