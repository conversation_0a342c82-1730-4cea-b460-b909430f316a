<?php
$z[0]=Array("include","",0,39);
$z[0][4]=array();
$z[0][4]['filename']="head_new.htm";
$z[1]=Array("global","",205,232);
$z[1][4]=array();
$z[1][4]['name']="cfg_basehost";
$z[2]=Array("field","",310,339);
$z[2][4]=array();
$z[2][4]['name']="position";
$z[3]=Array("field","",865,886);
$z[3][4]=array();
$z[3][4]['name']="typeurl";
$z[4]=Array("field","",888,910);
$z[4][4]=array();
$z[4][4]['name']="typename";
$z[5]=Array("field","",1004,1023);
$z[5][4]=array();
$z[5][4]['name']="title";
$z[6]=Array("field","",1315,1335);
$z[6][4]=array();
$z[6][4]['name']="writer";
$z[7]=Array("field","",1553,1623);
$z[7][4]=array();
$z[7][4]['name']="pubdate";
$z[7][4]['function']="strftime(\"%Y年%m月%d日 %H:%M\",@me)";
$z[8]=Array("field","",1841,1860);
$z[8][4]=array();
$z[8][4]['name']="click";
$z[9]=Array("field","",2158,2174);
$z[9][4]=array();
$z[9][4]['name']="id";
$z[10]=Array("field","",2823,2843);
$z[10][4]=array();
$z[10][4]['name']="scores";
$z[11]=Array("field","\n                        <div class=\"article-thumbnail\">\n                            <img src=\"[field:litpic/]\" alt=\"{dede:field.title/}\">\n                        </div>\n                        ",3549,3782);
$z[11][4]=array();
$z[11][4]['name']="litpic";
$z[12]=Array("field","",3965,3990);
$z[12][4]=array();
$z[12][4]['name']="description";
$z[13]=Array("field","",4389,4411);
$z[13][4]=array();
$z[13][4]['name']="typename";
$z[14]=Array("field","",4913,4932);
$z[14][4]=array();
$z[14][4]['name']="click";
$z[15]=Array("field","",5180,5237);
$z[15][4]=array();
$z[15][4]['name']="pubdate";
$z[15][4]['function']="strftime(\"%Y-%m-%d\",@me)";
$z[16]=Array("field","\n                                        <?php\n                                        \$keywords = @me;\n                                        \$tags = explode(',', \$keywords);\n                                        foreach(\$tags as \$tag) {\n                                            \$tag = trim(\$tag);\n                                            if(\$tag) {\n                                                echo '<a href=\"/tags.php?tag='.urlencode(\$tag).'\" class=\"tag-item\">'.\$tag.'</a>';\n                                            }\n                                        }\n                                        ?>\n                                        ",5788,6505);
$z[16][4]=array();
$z[16][4]['name']="keywords";
$z[16][4]['runphp']="yes";
$z[17]=Array("field","",7574,7590);
$z[17][4]=array();
$z[17][4]['name']="id";
$z[18]=Array("field","",8240,8258);
$z[18][4]=array();
$z[18][4]['name']="body";
$z[19]=Array("field","\n                                <?php\n                                \$keywords = @me;\n                                \$tags = explode(',', \$keywords);\n                                foreach(\$tags as \$tag) {\n                                    \$tag = trim(\$tag);\n                                    if(\$tag) {\n                                        echo '<a href=\"/tags.php?tag='.urlencode(\$tag).'\" class=\"tag-item\">'.\$tag.'</a>';\n                                    }\n                                }\n                                ?>\n                                ",8560,9189);
$z[19][4]=array();
$z[19][4]['name']="keywords";
$z[19][4]['runphp']="yes";
$z[20]=Array("likearticle","\n                            <div class=\"related-item\">\n                                <div class=\"related-image\">\n                                    <img src=\"[field:litpic/]\" alt=\"[field:title/]\">\n                                </div>\n                                <div class=\"related-content\">\n                                    <h4><a href=\"[field:arcurl/]\">[field:title/]</a></h4>\n                                    <div class=\"related-meta\">\n                                        <span class=\"time\">[field:pubdate function='strftime(\"%m-%d\",@me)'/]</span>\n                                        <span class=\"views\">[field:click/] 浏览</span>\n                                    </div>\n                                </div>\n                            </div>\n                            ",10791,11655);
$z[20][4]=array();
$z[20][4]['row']="6";
$z[20][4]['titlelen']="40";
$z[21]=Array("field","",12164,12180);
$z[21][4]=array();
$z[21][4]['name']="id";
$z[22]=Array("feedback","\n                            <div class=\"comment-item\">\n                                <div class=\"comment-avatar\">\n                                    <img src=\"{dede:global.cfg_templets_skin/}/images/avatar.png\" alt=\"用户头像\">\n                                </div>\n                                <div class=\"comment-content\">\n                                    <div class=\"comment-header\">\n                                        <span class=\"comment-author\">[field:username function=\"(@me=='guest' ? '游客' : @me)\"/]</span>\n                                        <span class=\"comment-time\">[field:dtime function='strftime(\"%Y-%m-%d %H:%M\",@me)'/]</span>\n                                    </div>\n                                    <div class=\"comment-text\">\n                                        [field:msg/]\n                                    </div>\n                                </div>\n                            </div>\n                            ",12902,13906);
$z[22][4]=array();
$z[23]=Array("global","",14373,14405);
$z[23][4]=array();
$z[23][4]['name']="cfg_templets_skin";
$z[24]=Array("field","",14573,14593);
$z[24][4]=array();
$z[24][4]['name']="writer";
$z[25]=Array("likearticle","\n                            <div class=\"simple-item\">\n                                <span class=\"item-number\">{dede:global.autoindex/}</span>\n                                <a href=\"[field:arcurl/]\">[field:title/]</a>\n                                <span class=\"item-count\">[field:click/]</span>\n                            </div>\n                            ",15239,15662);
$z[25][4]=array();
$z[25][4]['row']="8";
$z[25][4]['titlelen']="30";
$z[26]=Array("feedback","\n                            <div class=\"comment-preview\">\n                                <div class=\"comment-user\">[field:username function=\"(@me=='guest' ? '游客' : @me)\"/]</div>\n                                <div class=\"comment-content\">[field:msg function='cn_substr(@me,50)'/]</div>\n                                <div class=\"comment-time\">[field:dtime function='strftime(\"%m-%d %H:%M\",@me)'/]</div>\n                            </div>\n                            ",15950,16477);
$z[26][4]=array();
$z[26][4]['row']="5";
$z[26][4]['titlelen']="20";
$z[27]=Array("include","",16612,16653);
$z[27][4]=array();
$z[27][4]['filename']="footer_new.htm";

?>