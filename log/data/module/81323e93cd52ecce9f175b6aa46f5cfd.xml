<module>
<baseinfo>
name=百度站内搜索模块
team=织梦官方
time=2015-05-20
email=<EMAIL>
url=http://www.dedecms.com
hash=81323e93cd52ecce9f175b6aa46f5cfd
indexname=
indexurl=
ismember=0
autosetup=1
autodel=1
lang=utf-8
moduletype=templets
</baseinfo>
<systemfile>
<menustring>
PG06dG9wIG5hbWU9J+eZvuW6puermeWGheaQnOe0oicgYz0nNiwnIGRpc3BsYXk9J2Jsb2NrJyByYW5rPScnPg0KICA8bTppdGVtIG5hbWU9J+ermeWGheaQnOe0oicgbGluaz0nYmFpZHVzaXRlbWFwX21haW4ucGhwJyByYW5rPScnIHRhcmdldD0nbWFpbicgLz4NCiAgPG06aXRlbSBuYW1lPSfmkJzntKLmoYbnrqHnkIYnIGxpbms9J2JhaWR1c2l0ZW1hcF9tYWluLnBocD9kb3Bvc3Q9c2VhcmNoYm94MicgcmFuaz0nJyB0YXJnZXQ9J21haW4nIC8+DQogIDxtOml0ZW0gbmFtZT0n57uT5p6c6aG1566h55CGJyBsaW5rPSdiYWlkdXNpdGVtYXBfbWFpbi5waHA/ZG9wb3N0PXNlYXJjaHBhZ2UyJyByYW5rPScnIHRhcmdldD0nbWFpbicgLz4NCiAgPG06aXRlbSBuYW1lPSfojrflvpfmlLblhaUnIGxpbms9J2JhaWR1c2l0ZW1hcF9tYWluLnBocD9kb3Bvc3Q9aW5jb21lMicgcmFuaz0nJyB0YXJnZXQ9J21haW4nIC8+ICANCiAgPG06aXRlbSBuYW1lPSfmlbDmja7miqXooagnIGxpbms9J2JhaWR1c2l0ZW1hcF9tYWluLnBocD9kb3Bvc3Q9cmVwb3J0MicgcmFuaz0nJyB0YXJnZXQ9J21haW4nIC8+DQo8L206dG9wPg==
</menustring>
<readme>
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
</readme>
<setupsql40>

</setupsql40>
<delsql>
RFJPUCBUQUJMRSBJRiBFWElTVFMgYCNAX19wbHVzX2JhaWR1c2l0ZW1hcF9saXN0YDsNCkRST1AgVEFCTEUgSUYgRVhJU1RTIGAjQF9fcGx1c19iYWlkdXNpdGVtYXBfc2V0dGluZ2A7
</delsql>
<setup>

</setup>
<uninstall>

</uninstall>
<oldfilelist>
./baidusitemap_main.php
../include/baidusitemap.func.php
../include/baiduxml.class.php
../include/taglib/baidusitemap.lib.php
../plus/baidusitemap.php
../plus/search.php
</oldfilelist>
</systemfile>

<modulefiles>
<file type='file' name='./baidusitemap_main.php'>
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
</file>
<file type='file' name='../include/baidusitemap.func.php'>
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
</file>
<file type='file' name='../include/baiduxml.class.php'>
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
</file>
<file type='file' name='../include/taglib/baidusitemap.lib.php'>
PD9waHAgICBpZighZGVmaW5lZCgnREVERUlOQycpKSBleGl0KCdSZXF1ZXN0IEVycm9yIScpOw0KcmVxdWlyZV9vbmNlKERFREVJTkMuIi9iYWlkdXNpdGVtYXAuZnVuYy5waHAiKTsNCg0KZnVuY3Rpb24gbGliX2JhaWR1c2l0ZW1hcCgmJGN0YWcsJiRyZWZPYmopDQp7DQogICAgZ2xvYmFsICRkc3FsLCAkZW52czsNCiAgICAvL+WxnuaAp+WkhOeQhg0KICAgICRhdHRsaXN0PSJ0eXBlfGNvZGUiOw0KICAgIEZpbGxBdHRzRGVmYXVsdCgkY3RhZy0+Q0F0dHJpYnV0ZS0+SXRlbXMsJGF0dGxpc3QpOw0KICAgIGV4dHJhY3QoJGN0YWctPkNBdHRyaWJ1dGUtPkl0ZW1zLCBFWFRSX1NLSVApOw0KICAgIA0KICAgICRyZXZhbD0iIjsNCiAgICANCiAgICBpZiggISRkc3FsLT5Jc1RhYmxlKCIjQF9fcGx1c19iYWlkdXNpdGVtYXBfc2V0dGluZyIpICkgcmV0dXJuICfmsqHlronnmb7luqbnq5nlhoXmkJzntKLmqKHlnZcnOw0KICAgIA0KICAgICRzaXRlX2lkPWJhaWR1X2dldF9zZXR0aW5nKCdzaXRlX2lkJyk7DQogICAgaWYoZW1wdHkoJHNpdGVfaWQpKSByZXR1cm4gJ+Wwmuacque7keWumuermeeCuUlE77yM6K+355m75b2V57O757uf5ZCO5Y+w57uR5a6aJzsNCiAgICANCiAgICBpZigkdHlwZT09J2NvZGUnKQ0KICAgIHsNCiAgICAgICAgJHJldmFsIC49IDw8PEVPVA0KPHNjcmlwdCB0eXBlPSJ0ZXh0L2phdmFzY3JpcHQiPmRvY3VtZW50LndyaXRlKHVuZXNjYXBlKCclM0NkaXYgaWQ9ImJkY3MiJTNFJTNDL2RpdiUzRSUzQ3NjcmlwdCBjaGFyc2V0PSJ1dGYtOCIgc3JjPSJodHRwOi8vem5zdi5iYWlkdS5jb20vY3VzdG9tZXJfc2VhcmNoL2FwaS9qcz9zaWQ9eyRzaXRlX2lkfScpICsgJyZwbGF0ZV91cmw9JyArIChlbmNvZGVVUklDb21wb25lbnQod2luZG93LmxvY2F0aW9uLmhyZWYpKSArICcmdD0nICsgKE1hdGguY2VpbChuZXcgRGF0ZSgpLzM2MDAwMDApKSArIHVuZXNjYXBlKCciJTNFJTNDL3NjcmlwdCUzRScpKTs8L3NjcmlwdD4NCkVPVDsNCiAgICB9DQogICAgDQogICAgcmV0dXJuICRyZXZhbDsNCn0NCg==
</file>
<file type='file' name='../plus/baidusitemap.php'>
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
</file>
<file type='file' name='../plus/search.php'>
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
</file>
</modulefiles>
</module>
