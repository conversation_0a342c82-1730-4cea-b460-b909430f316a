@echo off
chcp 65001 >nul
title 测试版本DedeCMS模版安装
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🧪 测试版本模版安装                        ║
echo ║                  最基础版本，用于排查问题                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 当前目录: %CD%
echo.

:: 检查DedeCMS
if not exist "include\common.inc.php" (
    echo ❌ 错误：未检测到DedeCMS
    pause
    exit /b 1
)
echo ✅ 检测到DedeCMS环境

echo.
echo 🧪 测试版本说明：
echo    ✅ 只使用最基本的DedeCMS标签
echo    ✅ 没有复杂的嵌套语法
echo    ✅ 没有PHP代码
echo    ✅ 内置简单的CSS样式
echo    ✅ 极简设计，专注于功能测试
echo.

:: 检查测试版本文件
echo 🔍 检查测试版本文件...
set missing_files=0

if not exist "default\index_test.htm" (
    echo ❌ 缺少: default\index_test.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\index_test.htm
)

if not exist "default\list_test.htm" (
    echo ❌ 缺少: default\list_test.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\list_test.htm
)

if not exist "default\article_test.htm" (
    echo ❌ 缺少: default\article_test.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\article_test.htm
)

if %missing_files% gtr 0 (
    echo.
    echo ❌ 发现 %missing_files% 个测试文件缺失
    echo    请确保测试版本文件已生成
    echo.
    pause
    exit /b 1
)

echo.
set /p confirm="是否安装测试版本模版？(Y/N): "
if /i not "%confirm%"=="Y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始安装测试版本...
echo.

:: 创建备份
set backup_dir=templets\default_backup_test_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_dir=%backup_dir: =0%
echo 📁 创建备份目录: %backup_dir%
if not exist "%backup_dir%" mkdir "%backup_dir%" 2>nul

:: 备份原文件
echo.
echo 💾 备份原模版文件...
if exist "templets\default\index.htm" (
    copy "templets\default\index.htm" "%backup_dir%\index.htm" >nul 2>&1
    echo ✅ 备份 index.htm
)
if exist "templets\default\list_default.htm" (
    copy "templets\default\list_default.htm" "%backup_dir%\list_default.htm" >nul 2>&1
    echo ✅ 备份 list_default.htm
)
if exist "templets\default\article_article.htm" (
    copy "templets\default\article_article.htm" "%backup_dir%\article_article.htm" >nul 2>&1
    echo ✅ 备份 article_article.htm
)

:: 创建必要的目录
echo.
echo 📁 确保目录存在...
if not exist "templets\default" mkdir "templets\default" 2>nul
echo ✅ 目录检查完成

:: 安装测试版本模版
echo.
echo 📄 安装测试版本模版...

copy "default\index_test.htm" "templets\default\index.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 index.htm 失败
) else (
    echo ✅ 安装 index.htm (测试版本)
)

copy "default\list_test.htm" "templets\default\list_default.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 list_default.htm 失败
) else (
    echo ✅ 安装 list_default.htm (测试版本)
)

copy "default\article_test.htm" "templets\default\article_article.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 article_article.htm 失败
) else (
    echo ✅ 安装 article_article.htm (测试版本)
)

:: 验证安装结果
echo.
echo 🔍 验证安装结果...
set missing_files=0

if not exist "templets\default\index.htm" (
    echo ❌ 缺失: index.htm
    set /a missing_files+=1
) else (
    for %%F in ("templets\default\index.htm") do echo ✅ 存在: index.htm (大小: %%~zF 字节)
)

if not exist "templets\default\list_default.htm" (
    echo ❌ 缺失: list_default.htm
    set /a missing_files+=1
) else (
    for %%F in ("templets\default\list_default.htm") do echo ✅ 存在: list_default.htm (大小: %%~zF 字节)
)

if not exist "templets\default\article_article.htm" (
    echo ❌ 缺失: article_article.htm
    set /a missing_files+=1
) else (
    for %%F in ("templets\default\article_article.htm") do echo ✅ 存在: article_article.htm (大小: %%~zF 字节)
)

:: 最终结果
echo.
if %missing_files% equ 0 (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                        🎉 安装成功！                          ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo 🧪 测试版本特点：
    echo    • 只使用最基本的DedeCMS标签
    echo    • 没有复杂的嵌套语法
    echo    • 没有PHP代码
    echo    • 内置简单的CSS样式
    echo    • 极简设计，专注于功能测试
) else (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                        ⚠️  部分问题                           ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo 发现 %missing_files% 个文件缺失，请检查
)

echo.
echo 📝 测试步骤：
echo    1. 登录 DedeCMS 后台 (通常是 /dede/)
echo    2. 先测试首页：进入"生成" → "更新主页HTML"
echo    3. 观察过程：看是否还是一闪而过
echo    4. 如果成功：再测试栏目页和文章页
echo    5. 如果失败：检查DedeCMS错误日志
echo.
echo 🔍 如果仍然一闪而过：
echo    • 检查PHP错误日志：查看服务器错误日志
echo    • 检查DedeCMS日志：查看 /data/log/ 目录
echo    • 检查数据库：确保数据库连接正常
echo    • 检查权限：确保文件权限正确
echo    • 检查内存：可能是PHP内存不足
echo.
echo 💾 原文件已备份到: %backup_dir%
echo.
echo 🧪 现在请测试最基础的模版生成！
echo.
pause
