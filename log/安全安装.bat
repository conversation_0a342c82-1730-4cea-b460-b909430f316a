@echo off
chcp 65001 >nul
title 安全版本DedeCMS模版安装
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🛡️ 安全版本模版安装                        ║
echo ║                  使用标准DedeCMS标签，避免SQL错误              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 当前目录: %CD%
echo.

:: 检查DedeCMS
if not exist "include\common.inc.php" (
    echo ❌ 错误：未检测到DedeCMS
    pause
    exit /b 1
)
echo ✅ 检测到DedeCMS环境

echo.
echo 🛡️ 安全版本特点：
echo    ✅ 使用标准DedeCMS标签，避免所有嵌套语法
echo    ✅ 移除了所有可能导致SQL错误的复杂标签
echo    ✅ 保持原有视觉设计，简化功能实现
echo    ✅ 完全兼容DedeCMS V5.7+版本
echo    ✅ 内置CSS样式，减少外部依赖
echo.

:: 检查安全版本文件
echo 🔍 检查安全版本文件...
set missing_files=0

if not exist "default\index_safe.htm" (
    echo ❌ 缺少: default\index_safe.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\index_safe.htm
)

if not exist "default\list_safe.htm" (
    echo ❌ 缺少: default\list_safe.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\list_safe.htm
)

if not exist "default\article_safe.htm" (
    echo ❌ 缺少: default\article_safe.htm
    set /a missing_files+=1
) else (
    echo ✅ 找到: default\article_safe.htm
)

if %missing_files% gtr 0 (
    echo.
    echo ❌ 发现 %missing_files% 个安全版本文件缺失
    echo    请确保安全版本文件已生成
    echo.
    pause
    exit /b 1
)

echo.
set /p confirm="是否安装安全版本模版？(Y/N): "
if /i not "%confirm%"=="Y" (
    echo 安装已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始安装安全版本...
echo.

:: 创建备份
set backup_dir=templets\default_backup_safe_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_dir=%backup_dir: =0%
echo 📁 创建备份目录: %backup_dir%
if not exist "%backup_dir%" mkdir "%backup_dir%" 2>nul

:: 备份原文件
echo.
echo 💾 备份原模版文件...
if exist "templets\default\index.htm" (
    copy "templets\default\index.htm" "%backup_dir%\index.htm" >nul 2>&1
    echo ✅ 备份 index.htm
)
if exist "templets\default\list_default.htm" (
    copy "templets\default\list_default.htm" "%backup_dir%\list_default.htm" >nul 2>&1
    echo ✅ 备份 list_default.htm
)
if exist "templets\default\article_article.htm" (
    copy "templets\default\article_article.htm" "%backup_dir%\article_article.htm" >nul 2>&1
    echo ✅ 备份 article_article.htm
)

:: 创建必要的目录
echo.
echo 📁 创建目录结构...
if not exist "templets\default" mkdir "templets\default" 2>nul
if not exist "templets\default\style" mkdir "templets\default\style" 2>nul
if not exist "templets\default\js" mkdir "templets\default\js" 2>nul
if not exist "templets\default\images" mkdir "templets\default\images" 2>nul
echo ✅ 目录创建完成

:: 安装安全版本模版
echo.
echo 📄 安装安全版本模版...

copy "default\index_safe.htm" "templets\default\index.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 index.htm 失败
) else (
    echo ✅ 安装 index.htm (安全版本)
)

copy "default\list_safe.htm" "templets\default\list_default.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 list_default.htm 失败
) else (
    echo ✅ 安装 list_default.htm (安全版本)
)

copy "default\article_safe.htm" "templets\default\article_article.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 article_article.htm 失败
) else (
    echo ✅ 安装 article_article.htm (安全版本)
)

:: 创建简化的头部和底部文件
echo.
echo 📝 创建简化的头部和底部文件...

echo ^<!DOCTYPE html^> > "templets\default\head.htm"
echo ^<html lang="zh-CN"^> >> "templets\default\head.htm"
echo ^<head^> >> "templets\default\head.htm"
echo     ^<meta charset="UTF-8"^> >> "templets\default\head.htm"
echo     ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^> >> "templets\default\head.htm"
echo     ^<title^>{dede:field.title/}_{dede:global.cfg_webname/}^</title^> >> "templets\default\head.htm"
echo     ^<style^>* { margin: 0; padding: 0; box-sizing: border-box; } body { font-family: Arial, sans-serif; background: #0a0a0a; color: #fff; } a { color: #00d4ff; text-decoration: none; }^</style^> >> "templets\default\head.htm"
echo ^</head^> >> "templets\default\head.htm"
echo ^<body^> >> "templets\default\head.htm"
echo ✅ 创建 head.htm

echo     ^<footer style="background: #1a1a1a; padding: 40px 0; border-top: 1px solid #333; margin-top: 40px;"^> > "templets\default\footer.htm"
echo         ^<div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; text-align: center;"^> >> "templets\default\footer.htm"
echo             ^<p style="color: #666;"^>© 2024 {dede:global.cfg_webname/} - 版权所有^</p^> >> "templets\default\footer.htm"
echo         ^</div^> >> "templets\default\footer.htm"
echo     ^</footer^> >> "templets\default\footer.htm"
echo ^</body^> >> "templets\default\footer.htm"
echo ^</html^> >> "templets\default\footer.htm"
echo ✅ 创建 footer.htm

:: 创建占位符文件
echo.
echo 🖼️  创建占位符文件...
echo ^<!-- Logo占位符 --^> > "templets\default\images\logo.png"
echo ✅ 创建 logo.png

echo ^<!-- Banner占位符 --^> > "templets\default\images\banner.jpg"
echo ✅ 创建 banner.jpg

echo ^<!-- 占位符图片 --^> > "templets\default\images\placeholder.jpg"
echo ✅ 创建 placeholder.jpg

echo /* CSS样式已内置在模版中 */ > "templets\default\style\main.css"
echo ✅ 创建 main.css

echo /* JavaScript功能已内置在模版中 */ > "templets\default\js\main.js"
echo ✅ 创建 main.js

:: 验证安装结果
echo.
echo 🔍 验证安装结果...
set missing_files=0

if not exist "templets\default\index.htm" (
    echo ❌ 缺失: index.htm
    set /a missing_files+=1
) else (
    echo ✅ 存在: index.htm
)

if not exist "templets\default\list_default.htm" (
    echo ❌ 缺失: list_default.htm
    set /a missing_files+=1
) else (
    echo ✅ 存在: list_default.htm
)

if not exist "templets\default\article_article.htm" (
    echo ❌ 缺失: article_article.htm
    set /a missing_files+=1
) else (
    echo ✅ 存在: article_article.htm
)

if not exist "templets\default\head.htm" (
    echo ❌ 缺失: head.htm
    set /a missing_files+=1
) else (
    echo ✅ 存在: head.htm
)

if not exist "templets\default\footer.htm" (
    echo ❌ 缺失: footer.htm
    set /a missing_files+=1
) else (
    echo ✅ 存在: footer.htm
)

:: 最终结果
echo.
if %missing_files% equ 0 (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                        🎉 安装成功！                          ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo ✅ 安全版本特性：
    echo    • 使用标准DedeCMS标签，避免嵌套语法
    echo    • 内置CSS样式，减少外部文件依赖
    echo    • 简化功能实现，提高兼容性
    echo    • 保持现代化视觉设计
    echo    • 完全兼容DedeCMS V5.7+
) else (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                        ⚠️  部分问题                           ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo 发现 %missing_files% 个文件缺失，请检查
)

echo.
echo 📝 测试步骤：
echo    1. 登录 DedeCMS 后台 (通常是 /dede/)
echo    2. 先测试单个文章：进入"生成" → "更新文档HTML" → 选择一篇文章生成
echo    3. 如果成功：进入"生成" → "更新栏目HTML" → 选择一个栏目生成
echo    4. 最后：进入"生成" → "更新主页HTML"
echo    5. 清除浏览器缓存，查看效果
echo.
echo 💾 原文件已备份到: %backup_dir%
echo.
echo 🌐 现在应该可以正常生成HTML文件了！
echo.
pause
