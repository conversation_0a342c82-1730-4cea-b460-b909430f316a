<?php
/**
 * 安全版本安装脚本 - 使用完全兼容的DedeCMS标签
 */

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <title>安全版本安装</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #2d2d2d; padding: 30px; border-radius: 10px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .step { margin: 15px 0; padding: 15px; background: #333; border-radius: 8px; }
        .highlight { background: #404040; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🛡️ DedeCMS模版安全版本安装</h1>";

echo "<div class='step'><h3>📋 安全版本特点</h3>";
echo "<div class='highlight'>
✅ 使用标准DedeCMS标签，避免所有嵌套语法<br>
✅ 移除了所有可能导致SQL错误的复杂标签<br>
✅ 保持原有视觉设计，简化功能实现<br>
✅ 完全兼容DedeCMS V5.7+版本<br>
✅ 内置CSS样式，减少外部依赖
</div>";
echo "</div>";

$success_count = 0;
$error_count = 0;

// 确保目录存在
$dirs = ['templets/default', 'templets/default/style', 'templets/default/js', 'templets/default/images'];
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        @mkdir($dir, 0755, true);
    }
}

echo "<div class='step'><h3>📄 安装安全版本模版</h3>";

// 安全版本文件映射
$safe_files = [
    'default/index_safe.htm' => 'templets/default/index.htm',
    'default/list_safe.htm' => 'templets/default/list_default.htm',
    'default/article_safe.htm' => 'templets/default/article_article.htm'
];

foreach ($safe_files as $source => $target) {
    if (file_exists($source)) {
        if (copy($source, $target)) {
            echo "<p class='success'>✅ 安装安全版本: $source → $target</p>";
            $success_count++;
        } else {
            echo "<p class='error'>❌ 复制失败: $source → $target</p>";
            $error_count++;
        }
    } else {
        echo "<p class='error'>❌ 源文件不存在: $source</p>";
        $error_count++;
    }
}

// 创建简化的头部和底部文件
echo "<p class='warning'>📝 创建简化的头部和底部文件...</p>";

$head_content = '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{dede:field.title/}_{dede:global.cfg_webname/}</title>
    <meta name="keywords" content="{dede:field.keywords/}">
    <meta name="description" content="{dede:field.description/}">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #0a0a0a; color: #fff; }
        a { color: #00d4ff; text-decoration: none; }
        a:hover { color: #0099cc; }
    </style>
</head>
<body>
    <header style="background: #1a1a1a; padding: 20px 0; border-bottom: 1px solid #333;">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; display: flex; justify-content: space-between; align-items: center;">
            <div>
                <a href="{dede:global.cfg_basehost/}" style="font-size: 1.5rem; font-weight: bold;">{dede:global.cfg_webname/}</a>
            </div>
            <nav>
                <a href="{dede:global.cfg_basehost/}" style="margin: 0 15px;">首页</a>
                {dede:channel type=\'top\' row=\'5\'}
                <a href="[field:typeurl/]" style="margin: 0 15px;">[field:typename/]</a>
                {/dede:channel}
            </nav>
        </div>
    </header>';

$footer_content = '    <footer style="background: #1a1a1a; padding: 40px 0; border-top: 1px solid #333; margin-top: 40px;">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; text-align: center;">
            <p style="color: #666; margin-bottom: 10px;">© 2024 {dede:global.cfg_webname/} - 版权所有</p>
            <p style="color: #666; font-size: 0.9rem;">{dede:global.cfg_description/}</p>
        </div>
    </footer>
</body>
</html>';

if (file_put_contents('templets/default/head.htm', $head_content)) {
    echo "<p class='success'>✅ 创建 head.htm</p>";
    $success_count++;
} else {
    echo "<p class='error'>❌ 创建 head.htm 失败</p>";
    $error_count++;
}

if (file_put_contents('templets/default/footer.htm', $footer_content)) {
    echo "<p class='success'>✅ 创建 footer.htm</p>";
    $success_count++;
} else {
    echo "<p class='error'>❌ 创建 footer.htm 失败</p>";
    $error_count++;
}

echo "</div>";

// 创建必要的占位符文件
echo "<div class='step'><h3>🖼️ 创建占位符文件</h3>";

$placeholders = [
    'templets/default/images/logo.png' => '<!-- Logo占位符 -->',
    'templets/default/images/banner.jpg' => '<!-- Banner占位符 -->',
    'templets/default/images/placeholder.jpg' => '<!-- 占位符图片 -->',
    'templets/default/style/main.css' => '/* CSS样式已内置在模版中 */',
    'templets/default/js/main.js' => '/* JavaScript功能已内置在模版中 */'
];

foreach ($placeholders as $file => $content) {
    if (file_put_contents($file, $content)) {
        echo "<p class='success'>✅ 创建占位符: $file</p>";
    } else {
        echo "<p class='error'>❌ 创建失败: $file</p>";
    }
}

echo "</div>";

// 验证安装结果
echo "<div class='step'><h3>🔍 验证安装结果</h3>";

$check_files = [
    'templets/default/index.htm' => '首页模版',
    'templets/default/list_default.htm' => '列表页模版',
    'templets/default/article_article.htm' => '文章页模版',
    'templets/default/head.htm' => '头部文件',
    'templets/default/footer.htm' => '底部文件'
];

$all_ok = true;
foreach ($check_files as $file => $desc) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $desc: $file</p>";
    } else {
        echo "<p class='error'>❌ $desc 缺失: $file</p>";
        $all_ok = false;
    }
}

echo "</div>";

echo "<div class='step'><h3>📊 安装结果</h3>";
if ($all_ok && $error_count == 0) {
    echo "<p class='success'>🎉 安全版本安装成功！现在可以正常生成HTML了。</p>";
} else {
    echo "<p class='warning'>⚠️ 安装完成，但可能有部分问题</p>";
}

echo "<h4>✅ 安全版本特性：</h4>
<ul>
    <li>使用标准DedeCMS标签，避免嵌套语法</li>
    <li>内置CSS样式，减少外部文件依赖</li>
    <li>简化功能实现，提高兼容性</li>
    <li>保持现代化视觉设计</li>
    <li>完全兼容DedeCMS V5.7+</li>
</ul>";

echo "<h4>📝 测试步骤：</h4>
<ol>
    <li>登录DedeCMS后台</li>
    <li><strong>先测试单个文章</strong>：进入"生成" → "更新文档HTML" → 选择一篇文章生成</li>
    <li><strong>如果成功</strong>：进入"生成" → "更新栏目HTML" → 选择一个栏目生成</li>
    <li><strong>最后</strong>：进入"生成" → "更新主页HTML"</li>
    <li>清除浏览器缓存，查看效果</li>
</ol>";

echo "<h4>🔧 如果仍有问题：</h4>
<ul>
    <li>检查DedeCMS版本是否为V5.7+</li>
    <li>检查数据库连接是否正常</li>
    <li>检查文件权限是否正确</li>
    <li>查看DedeCMS错误日志</li>
</ul>";

echo "</div></div></body></html>";
?>
