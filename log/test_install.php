<?php
/**
 * 测试版本安装脚本 - 最基础的DedeCMS模版
 */

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <title>测试版本安装</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #2d2d2d; padding: 30px; border-radius: 10px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .step { margin: 15px 0; padding: 15px; background: #333; border-radius: 8px; }
        .highlight { background: #404040; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 DedeCMS模版测试版本安装</h1>";

echo "<div class='step'><h3>📋 测试版本说明</h3>";
echo "<div class='highlight'>
🧪 <strong>这是最基础的测试版本</strong><br>
✅ 只使用最基本的DedeCMS标签<br>
✅ 没有复杂的嵌套语法<br>
✅ 没有PHP代码<br>
✅ 内置简单的CSS样式<br>
✅ 用于排查生成问题的根本原因
</div>";
echo "</div>";

$success_count = 0;
$error_count = 0;

// 确保目录存在
$dirs = ['templets/default'];
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        @mkdir($dir, 0755, true);
    }
}

echo "<div class='step'><h3>📄 安装测试版本模版</h3>";

// 测试版本文件映射
$test_files = [
    'default/index_test.htm' => 'templets/default/index.htm',
    'default/list_test.htm' => 'templets/default/list_default.htm',
    'default/article_test.htm' => 'templets/default/article_article.htm'
];

foreach ($test_files as $source => $target) {
    if (file_exists($source)) {
        if (copy($source, $target)) {
            echo "<p class='success'>✅ 安装测试版本: $source → $target</p>";
            $success_count++;
        } else {
            echo "<p class='error'>❌ 复制失败: $source → $target</p>";
            $error_count++;
        }
    } else {
        echo "<p class='error'>❌ 源文件不存在: $source</p>";
        $error_count++;
    }
}

echo "</div>";

// 验证安装结果
echo "<div class='step'><h3>🔍 验证安装结果</h3>";

$check_files = [
    'templets/default/index.htm' => '首页模版',
    'templets/default/list_default.htm' => '列表页模版',
    'templets/default/article_article.htm' => '文章页模版'
];

$all_ok = true;
foreach ($check_files as $file => $desc) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<p class='success'>✅ $desc: $file (大小: {$size} 字节)</p>";
    } else {
        echo "<p class='error'>❌ $desc 缺失: $file</p>";
        $all_ok = false;
    }
}

echo "</div>";

echo "<div class='step'><h3>📊 安装结果</h3>";
if ($all_ok && $error_count == 0) {
    echo "<p class='success'>🎉 测试版本安装成功！</p>";
} else {
    echo "<p class='warning'>⚠️ 安装完成，但可能有部分问题</p>";
}

echo "<h4>🧪 测试版本特点：</h4>
<ul>
    <li>只使用最基本的DedeCMS标签</li>
    <li>没有复杂的嵌套语法</li>
    <li>没有PHP代码</li>
    <li>内置简单的CSS样式</li>
    <li>极简设计，专注于功能测试</li>
</ul>";

echo "<h4>📝 测试步骤：</h4>
<ol>
    <li>登录DedeCMS后台</li>
    <li><strong>先测试首页</strong>：进入"生成" → "更新主页HTML"</li>
    <li><strong>观察过程</strong>：看是否还是一闪而过</li>
    <li><strong>如果成功</strong>：再测试栏目页和文章页</li>
    <li><strong>如果失败</strong>：检查DedeCMS错误日志</li>
</ol>";

echo "<h4>🔍 如果仍然一闪而过：</h4>
<ul>
    <li><strong>检查PHP错误日志</strong>：查看服务器错误日志</li>
    <li><strong>检查DedeCMS日志</strong>：查看 /data/log/ 目录</li>
    <li><strong>检查数据库</strong>：确保数据库连接正常</li>
    <li><strong>检查权限</strong>：确保文件权限正确</li>
    <li><strong>检查内存</strong>：可能是PHP内存不足</li>
</ul>";

echo "<h4>🛠️ 调试建议：</h4>
<ul>
    <li>在PHP配置中开启错误显示</li>
    <li>检查 php.ini 中的 memory_limit 设置</li>
    <li>查看 Apache/Nginx 错误日志</li>
    <li>尝试手动访问生成的HTML文件</li>
</ul>";

echo "</div></div></body></html>";
?>
