@echo off
chcp 65001 >nul
title 应用小仙元码暗色主题样式
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎨 应用暗色主题样式                        ║
echo ║                  直接修改现有CSS文件                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 当前目录: %CD%
echo.

:: 检查DedeCMS
if not exist "include\common.inc.php" (
    echo ❌ 错误：未检测到DedeCMS
    pause
    exit /b 1
)
echo ✅ 检测到DedeCMS环境

:: 检查模版目录
if not exist "templets\default" (
    echo ❌ 错误：未找到默认模版目录
    pause
    exit /b 1
)
echo ✅ 找到默认模版目录

:: 检查样式文件
echo.
echo 🔍 检查样式文件...
if not exist "templets\default\style\dedecms.css" (
    echo ❌ 缺少: templets\default\style\dedecms.css
    pause
    exit /b 1
) else (
    echo ✅ 找到: templets\default\style\dedecms.css
)

if not exist "templets\default\style\layout.css" (
    echo ❌ 缺少: templets\default\style\layout.css
    pause
    exit /b 1
) else (
    echo ✅ 找到: templets\default\style\layout.css
)

echo.
echo 🎨 样式修改说明：
echo    ✅ 已将主要CSS文件修改为暗色主题
echo    ✅ 使用现代化的渐变和阴影效果
echo    ✅ 采用蓝色系配色方案 (#00d4ff)
echo    ✅ 优化了按钮、输入框、卡片等组件样式
echo    ✅ 添加了响应式设计和动画效果
echo.

set /p confirm="样式文件已修改，是否清除浏览器缓存并查看效果？(Y/N): "
if /i not "%confirm%"=="Y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 🚀 应用样式修改...
echo.

:: 检查修改结果
echo 📊 检查修改结果...
for %%F in ("templets\default\style\dedecms.css") do (
    echo ✅ dedecms.css 文件大小: %%~zF 字节
)

for %%F in ("templets\default\style\layout.css") do (
    echo ✅ layout.css 文件大小: %%~zF 字节
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        🎉 样式应用完成！                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎨 已应用的样式特性：
echo    • 暗色主题背景 (#0a0a0a, #1a1a1a, #2d2d2d)
echo    • 蓝色系主色调 (#00d4ff, #0099cc)
echo    • 现代化渐变效果
echo    • 圆角边框和阴影
echo    • 悬停动画效果
echo    • 响应式设计
echo    • 优化的按钮和表单样式
echo.
echo 📝 查看效果：
echo    1. 清除浏览器缓存 (Ctrl+F5)
echo    2. 访问您的网站首页
echo    3. 查看新的暗色主题效果
echo    4. 测试响应式设计 (调整浏览器窗口大小)
echo.
echo 🔧 如果样式没有生效：
echo    • 强制刷新浏览器 (Ctrl+Shift+R)
echo    • 检查浏览器开发者工具中的CSS加载
echo    • 确保CSS文件路径正确
echo    • 检查是否有缓存插件影响
echo.
echo 💡 进一步自定义：
echo    • 编辑 templets\default\style\dedecms.css 调整主要样式
echo    • 编辑 templets\default\style\layout.css 调整布局样式
echo    • 修改颜色变量来改变主题色彩
echo.
echo 🌐 现在可以查看新的暗色主题效果了！
echo.
pause
