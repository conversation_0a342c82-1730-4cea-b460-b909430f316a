@echo off
chcp 65001 >nul
title 最终修复DedeCMS模版
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🔧 最终修复DedeCMS模版                     ║
echo ║                  彻底解决SQL语法错误问题                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 当前目录: %CD%
echo.

:: 检查DedeCMS
if not exist "include\common.inc.php" (
    echo ❌ 错误：未检测到DedeCMS
    pause
    exit /b 1
)
echo ✅ 检测到DedeCMS环境

echo.
echo 🔧 修复说明：
echo    原问题：typeid='{dede:field.typeid/}' 和 typeid='[field:typeid/]'
echo    解决方案：
echo    1. 文章详情页使用 {dede:likearticle} 标签
echo    2. 列表页移除 typeid 参数
echo    3. 避免所有嵌套标签语法
echo.

set /p confirm="是否继续最终修复？(Y/N): "
if /i not "%confirm%"=="Y" (
    echo 修复已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始最终修复...
echo.

:: 创建必要的目录
echo 📁 创建目录结构...
if not exist "templets\default" mkdir "templets\default" 2>nul
if not exist "templets\default\style" mkdir "templets\default\style" 2>nul
if not exist "templets\default\js" mkdir "templets\default\js" 2>nul
if not exist "templets\default\images" mkdir "templets\default\images" 2>nul
echo ✅ 目录创建完成

:: 复制最终修复版本的模版文件
echo.
echo 📄 安装最终修复版本...

copy "default\index_new.htm" "templets\default\index.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 index.htm 失败
) else (
    echo ✅ 安装 index.htm
)

copy "default\list_new.htm" "templets\default\list_default.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 list_default.htm 失败
) else (
    echo ✅ 安装 list_default.htm (已修复SQL错误)
)

copy "default\article_new.htm" "templets\default\article_article.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 article_article.htm 失败
) else (
    echo ✅ 安装 article_article.htm (已修复SQL错误)
)

copy "default\head_new.htm" "templets\default\head.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 head.htm 失败
) else (
    echo ✅ 安装 head.htm
)

copy "default\footer_new.htm" "templets\default\footer.htm" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 footer.htm 失败
) else (
    echo ✅ 安装 footer.htm
)

:: 复制样式和脚本
echo.
echo 🎨 安装样式和脚本文件...
copy "default\style\main.css" "templets\default\style\main.css" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 main.css 失败
) else (
    echo ✅ 安装 main.css
)

copy "default\js\main.js" "templets\default\js\main.js" >nul 2>&1
if errorlevel 1 (
    echo ❌ 安装 main.js 失败
) else (
    echo ✅ 安装 main.js
)

:: 创建图片占位符
echo.
echo 🖼️  创建图片占位符...
echo ^<!-- Logo占位符 --^> > "templets\default\images\logo.png"
echo ✅ 创建 logo.png

echo ^<!-- Banner占位符 --^> > "templets\default\images\banner.jpg"
echo ✅ 创建 banner.jpg

echo ^<!-- 占位符图片 --^> > "templets\default\images\placeholder.jpg"
echo ✅ 创建 placeholder.jpg

echo ^<!-- 头像占位符 --^> > "templets\default\images\avatar.png"
echo ✅ 创建 avatar.png

echo ^<!-- QQ二维码占位符 --^> > "templets\default\images\qr-qq.png"
echo ✅ 创建 qr-qq.png

echo ^<!-- 微信二维码占位符 --^> > "templets\default\images\qr-wechat.png"
echo ✅ 创建 qr-wechat.png

echo ^<!-- 广告横幅占位符 --^> > "templets\default\images\ad-banner.jpg"
echo ✅ 创建 ad-banner.jpg

:: 验证修复结果
echo.
echo 🔍 验证修复结果...
set missing_files=0

if not exist "templets\default\index.htm" (
    echo ❌ 缺失: index.htm
    set /a missing_files+=1
) else (
    echo ✅ 存在: index.htm
)

if not exist "templets\default\list_default.htm" (
    echo ❌ 缺失: list_default.htm
    set /a missing_files+=1
) else (
    echo ✅ 存在: list_default.htm
)

if not exist "templets\default\article_article.htm" (
    echo ❌ 缺失: article_article.htm
    set /a missing_files+=1
) else (
    echo ✅ 存在: article_article.htm
)

if not exist "templets\default\style\main.css" (
    echo ❌ 缺失: style\main.css
    set /a missing_files+=1
) else (
    echo ✅ 存在: style\main.css
)

:: 最终结果
echo.
if %missing_files% equ 0 (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                        🎉 修复成功！                          ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo ✅ 已修复的问题：
    echo    • 彻底移除了所有嵌套标签语法
    echo    • 文章详情页使用 likearticle 标签显示相关文章
    echo    • 列表页显示全站热门文章
    echo    • 创建了所有必要的图片占位符
) else (
    echo ╔══════════════════════════════════════════════════════════════╗
    echo ║                        ⚠️  部分问题                           ║
    echo ╚══════════════════════════════════════════════════════════════╝
    echo.
    echo 发现 %missing_files% 个文件缺失，请检查
)

echo.
echo 📝 后续步骤：
echo    1. 登录 DedeCMS 后台 (通常是 /dede/)
echo    2. 进入"生成" → "更新文档HTML" (先测试单个文章)
echo    3. 如果成功，再进入"生成" → "更新栏目HTML"
echo    4. 最后进入"生成" → "更新主页HTML"
echo    5. 清除浏览器缓存，查看效果
echo.
echo 🌐 现在应该可以正常生成HTML文件了！
echo.
echo 💡 测试建议：
echo    • 先生成一个文章页面测试
echo    • 如果成功，再生成栏目页面
echo    • 最后生成首页
echo.
pause
