<?php
/**
 * 最终修复脚本 - 彻底解决DedeCMS标签语法问题
 */

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <title>最终修复</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #2d2d2d; padding: 30px; border-radius: 10px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .step { margin: 15px 0; padding: 15px; background: #333; border-radius: 8px; }
        .code { background: #404040; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 DedeCMS模版最终修复</h1>";

$success_count = 0;
$error_count = 0;

// 确保目录存在
$dirs = ['templets/default', 'templets/default/style', 'templets/default/js', 'templets/default/images'];
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        @mkdir($dir, 0755, true);
    }
}

echo "<div class='step'><h3>🔧 修复的问题</h3>";
echo "<div class='code'>
原问题：typeid='{dede:field.typeid/}' 和 typeid='[field:typeid/]'<br>
解决方案：<br>
1. 文章详情页使用 {dede:likearticle} 标签（自动匹配相关文章）<br>
2. 列表页移除 typeid 参数（显示全站热门）<br>
3. 避免所有嵌套标签语法
</div>";
echo "</div>";

echo "<div class='step'><h3>📄 安装最终修复版本</h3>";

// 文件映射
$files = [
    'default/index_new.htm' => 'templets/default/index.htm',
    'default/list_new.htm' => 'templets/default/list_default.htm',
    'default/article_new.htm' => 'templets/default/article_article.htm',
    'default/head_new.htm' => 'templets/default/head.htm',
    'default/footer_new.htm' => 'templets/default/footer.htm',
    'default/style/main.css' => 'templets/default/style/main.css',
    'default/js/main.js' => 'templets/default/js/main.js'
];

foreach ($files as $source => $target) {
    if (file_exists($source)) {
        if (copy($source, $target)) {
            echo "<p class='success'>✅ 复制成功: $source → $target</p>";
            $success_count++;
        } else {
            echo "<p class='error'>❌ 复制失败: $source → $target</p>";
            $error_count++;
        }
    } else {
        echo "<p class='error'>❌ 源文件不存在: $source</p>";
        $error_count++;
    }
}

echo "</div>";

// 创建图片占位符
echo "<div class='step'><h3>🖼️ 创建图片占位符</h3>";

$image_placeholders = [
    'templets/default/images/logo.png' => '<!-- Logo占位符 -->',
    'templets/default/images/banner.jpg' => '<!-- Banner占位符 -->',
    'templets/default/images/placeholder.jpg' => '<!-- 占位符图片 -->',
    'templets/default/images/avatar.png' => '<!-- 头像占位符 -->',
    'templets/default/images/qr-qq.png' => '<!-- QQ二维码占位符 -->',
    'templets/default/images/qr-wechat.png' => '<!-- 微信二维码占位符 -->',
    'templets/default/images/ad-banner.jpg' => '<!-- 广告横幅占位符 -->'
];

foreach ($image_placeholders as $file => $content) {
    if (file_put_contents($file, $content)) {
        echo "<p class='success'>✅ 创建占位符: $file</p>";
    } else {
        echo "<p class='error'>❌ 创建失败: $file</p>";
    }
}

echo "</div>";

// 验证修复
echo "<div class='step'><h3>🔍 验证修复结果</h3>";

$check_files = [
    'templets/default/index.htm',
    'templets/default/list_default.htm',
    'templets/default/article_article.htm',
    'templets/default/head.htm',
    'templets/default/footer.htm',
    'templets/default/style/main.css',
    'templets/default/js/main.js'
];

$all_ok = true;
foreach ($check_files as $file) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ 文件存在: $file</p>";
    } else {
        echo "<p class='error'>❌ 文件缺失: $file</p>";
        $all_ok = false;
    }
}

echo "</div>";

echo "<div class='step'><h3>📊 修复结果</h3>";
if ($all_ok && $error_count == 0) {
    echo "<p class='success'>🎉 所有文件已成功安装并修复！现在可以正常生成HTML了。</p>";
} else {
    echo "<p class='warning'>⚠️ 修复完成，但可能有部分问题</p>";
}

echo "<h4>✅ 已修复的问题：</h4>
<ul>
    <li>彻底移除了所有嵌套标签语法</li>
    <li>文章详情页使用 likearticle 标签显示相关文章</li>
    <li>列表页显示全站热门文章</li>
    <li>创建了所有必要的图片占位符</li>
</ul>";

echo "<h4>📝 后续步骤：</h4>
<ol>
    <li>登录DedeCMS后台</li>
    <li>进入"生成" → "更新主页HTML"</li>
    <li>进入"生成" → "更新栏目HTML"</li>
    <li>进入"生成" → "更新文档HTML"</li>
    <li>清除浏览器缓存</li>
</ol>";

echo "<h4>🌐 测试建议：</h4>
<ul>
    <li>先生成一个文章页面测试</li>
    <li>如果成功，再生成栏目页面</li>
    <li>最后生成首页</li>
    <li>如果还有错误，请检查DedeCMS版本兼容性</li>
</ul>";

echo "</div></div></body></html>";
?>
