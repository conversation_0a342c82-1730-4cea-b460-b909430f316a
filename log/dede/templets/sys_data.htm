<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo $cfg_soft_lang; ?>">
<title>数据库维护</title>
<link href='css/base.css' rel='stylesheet' type='text/css'>
<script language="javascript" src="../include/js/dedeajax2.js"></script>
<script language="javascript">
var myajax;
var newobj;
var posLeft = 200;
var posTop = 150;
function LoadUrl(surl){
  newobj = document.getElementById('_mydatainfo');
  if(!newobj){
  	newobj = document.createElement("DIV");
    newobj.id = '_mydatainfo';
    newobj.style.position='absolute';
    newobj.className = "dlg";
    newobj.style.top = posTop;
    newobj.style.left = posLeft;
    document.body.appendChild(newobj);
  }else{
  	newobj.style.display = "block";
  }
  myajax = new DedeAjax(newobj);
  myajax.SendGet("sys_data.php?"+surl);
}
function HideObj(objname){
   var obj = document.getElementById(objname);
   obj.style.display = "none";
}

//获得选中文件的数据表

function getCheckboxItem(){
	 var myform = document.form1;
	 var allSel="";
	 if(myform.tables.value) return myform.tables.value;
	 for(i=0;i<myform.tables.length;i++)
	 {
		 if(myform.tables[i].checked){
			 if(allSel=="")
				 allSel=myform.tables[i].value;
			 else
				 allSel=allSel+","+myform.tables[i].value;
		 }
	 }
	 return allSel;	
}

//反选
function ReSel(){
	var myform = document.form1;
	for(i=0;i<myform.tables.length;i++){
		if(myform.tables[i].checked) myform.tables[i].checked = false;
		else myform.tables[i].checked = true;
	}
}

//全选
function SelAll(){
	var myform = document.form1;
	for(i=0;i<myform.tables.length;i++){
		myform.tables[i].checked = true;
	}
}

//取消
function NoneSel(){
	var myform = document.form1;
	for(i=0;i<myform.tables.length;i++){
		myform.tables[i].checked = false;
	}
}

function checkSubmit()
{
	var myform = document.form1;
	myform.tablearr.value = getCheckboxItem();
	return true;
}

</script>
</head>
<body background='images/allbg.gif' leftmargin='8' topmargin='8'>
<table width="99%" border="0" cellpadding="3" cellspacing="1" bgcolor="#D6D6D6">
  <tr> 
    <td height="19" colspan="8" background="images/tbg.gif" bgcolor="#E7E7E7">
    	<table width="96%" border="0" cellspacing="1" cellpadding="1">
        <tr> 
          <td width="24%" style="padding-left:10px;"><strong>数据库管理</strong></td>
          <td width="76%" align="right">
          	<b><a href="sys_data_revert.php"><u>数据还原</u></a></b> | 
          	<b><a href="sys_sql_query.php"><u>SQL命令行工具</u></a></b>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <form name="form1" onSubmit="checkSubmit()" action="sys_data_done.php?dopost=bak" method="post" target="stafrm">
  <input type='hidden' name='tablearr' value='' />
  <tr bgcolor="#F7F8ED"> 
    <td height="24" colspan="8"><strong>DedeCMS默认系统表：</strong></td>
  </tr>
  <tr bgcolor="#FBFCE2" align="center"> 
    <td height="24" width="5%">选择</td>
    <td width="20%">表名</td>
    <td width="8%">记录数</td>
    <td width="17%">操作</td>
    <td width="5%">选择</td>
    <td width="20%">表名</td>
    <td width="8%">记录数</td>
    <td width="17%">操作</td>
  </tr>
  <?php  
  for($i=0; isset($dedeSysTables[$i]); $i++)
  { 
    $t = $dedeSysTables[$i];
    echo "<tr align='center'  bgcolor='#FFFFFF' height='24'>\r\n";
  ?> 
    <td>
    	<input type="checkbox" name="tables" value="<?php echo $t; ?>" class="np" checked /> 
    </td>
    <td> 
      <?php echo $t; ?>
    </td>
    <td> 
      <?php echo TjCount($t,$dsql); ?>
    </td>
    <td>
    <a href="#" onClick="LoadUrl('dopost=opimize&tablename=<?php echo $t; ?>');">优化</a> |
    <a href="#" onClick="LoadUrl('dopost=repair&tablename=<?php echo $t; ?>');">修复</a> |
    <a href="#" onClick="LoadUrl('dopost=viewinfo&tablename=<?php echo $t; ?>');">结构</a>
    </td>
  <?php
   $i++;
   if(isset($dedeSysTables[$i])) {
   	$t = $dedeSysTables[$i];
  ?>
    <td>
    	<input type="checkbox" name="tables" value="<?php echo $t; ?>" class="np" checked /> 
    </td>
    <td> 
      <?php echo $t; ?>
    </td>
    <td> 
      <?php echo TjCount($t,$dsql); ?>
    </td>
    <td>
    <a href="#" onClick="LoadUrl('dopost=opimize&tablename=<?php echo $t; ?>');">优化</a> |
    <a href="#" onClick="LoadUrl('dopost=repair&tablename=<?php echo $t; ?>');">修复</a> |
    <a href="#" onClick="LoadUrl('dopost=viewinfo&tablename=<?php echo $t; ?>');">结构</a>
  </td>
  <?php
   }
   else
   {
   	  echo "<td></td><td></td><td></td><td></td>\r\n";
   }
   echo "</tr>\r\n";
  }
  ?>
  <tr bgcolor="#F9FCEF"> 
    <td height="24" colspan="8"><strong>其它数据表：</strong></td>
  </tr>
  <tr bgcolor="#FBFCE2" align="center"> 
    <td height="24" width="5%">选择</td>
    <td width="20%">表名</td>
    <td width="8%">记录数</td>
    <td width="17%">操作</td>
    <td width="5%">选择</td>
    <td width="20%">表名</td>
    <td width="8%">记录数</td>
    <td width="17%">操作</td>
  </tr>
 <?php  
  for($i=0; isset($otherTables[$i]); $i++)
  { 
    $t = $otherTables[$i];
    echo "<tr align='center'  bgcolor='#FFFFFF' height='24'>\r\n";
  ?> 
    <td>
    	<input type="checkbox" name="tables" value="<?php echo $t; ?>" class="np" /> 
    </td>
    <td> 
      <?php echo $t; ?>
    </td>
    <td> 
      <?php echo TjCount($t,$dsql); ?>
    </td>
    <td>
    <a href="#" onClick="LoadUrl('dopost=opimize&tablename=<?php echo $t; ?>');">优化</a> |
    <a href="#" onClick="LoadUrl('dopost=repair&tablename=<?php echo $t; ?>');">修复</a> |
    <a href="#" onClick="LoadUrl('dopost=viewinfo&tablename=<?php echo $t; ?>');">结构</a>
    </td>
  <?php
   $i++;
   if(isset($otherTables[$i])) {
   	$t = $otherTables[$i];
  ?>
   <td>
    	<input type="checkbox" name="tables" value="<?php echo $t; ?>" class="np" /> 
    </td>
    <td> 
      <?php echo $t; ?>
    </td>
    <td> 
      <?php echo TjCount($t,$dsql); ?>
    </td>
    <td>
    <a href="#" onClick="LoadUrl('dopost=opimize&tablename=<?php echo $t; ?>');">优化</a> |
    <a href="#" onClick="LoadUrl('dopost=repair&tablename=<?php echo $t; ?>');">修复</a> |
    <a href="#" onClick="LoadUrl('dopost=viewinfo&tablename=<?php echo $t; ?>');">结构</a>
  </td>
  <?php
   }else{
   	  echo "<td></td><td></td><td></td><td></td>\r\n";
   }
   echo "</tr>\r\n";
  }
  ?>
    <tr bgcolor="#ffffff"> 
      <td height="24" colspan="8">
      	&nbsp; 
        <input name="b1" type="button" id="b1" class="coolbg np" onClick="SelAll()" value="全选" />
        &nbsp; 
        <input name="b2" type="button" id="b2" class="coolbg np" onClick="ReSel()" value="反选" />
        &nbsp; 
        <input name="b3" type="button" id="b3" class="coolbg np" onClick="NoneSel()" value="取消" />
      </td>
  </tr>
  <tr bgcolor="#F9FCEF"> 
    <td height="24" colspan="8"><strong>数据备份选项：</strong></td>
  </tr>
  <tr align="center" bgcolor="#FFFFFF"> 
    <td height="50" colspan="8">
    	  <table width="90%" border="0" cellspacing="0" cellpadding="0">
          <tr> 
            <td height="30">当前数据库版本： <?php echo $mysql_version?></td>
          </tr>
          <tr> 
            <td height="30">
            	指定备份数据格式： 
              <input name="datatype" type="radio" class="np" value="4.0"<?php if($mysql_version<4.1) echo " checked='1'";?> />
              MySQL3.x/4.0.x 版本 
              <input type="radio" name="datatype" value="4.1" class="np"<?php if($mysql_version>=4.1) echo " checked='1'";?> />
              MySQL4.1.x/5.x 版本
              </td>
          </tr>
          <tr> 
            <td height="30">
            	分卷大小： 
              <input name="fsize" type="text" id="fsize" value="2048" size="6" />
              K&nbsp;， 
              <input name="isstruct" type="checkbox" class="np" id="isstruct" value="1" checked='1' />
              备份表结构信息
              <?php  if(@function_exists('gzcompress') && false) {  ?>
              <input name="iszip" type="checkbox" class="np" id="iszip" value="1" checked='1' />
              完成后压缩成ZIP
              <?php } ?>
              <input type="submit" name="Submit" value="提交" class="coolbg np" />
             </td>
          </tr>
        </table>
      </td>
   </tr>
   </form>
  <tr bgcolor="#F9FCEF">
    <td height="24" colspan="8"><strong>进行状态：</strong></td>
  </tr>
  <tr bgcolor="#FFFFFF"> 
    <td height="180" colspan="8">
	<iframe name="stafrm" frameborder="0" id="stafrm" width="100%" height="100%"></iframe>
	</td>
  </tr>
</table>
</body>
</html>