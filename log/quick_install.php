<?php
/**
 * 小仙元码模版 - 快速安装脚本
 * 专门为当前目录结构设计
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 当前工作目录
$current_dir = dirname(__FILE__);
echo "当前目录: " . $current_dir . "<br>";

// 执行安装
if (isset($_GET['install']) && $_GET['install'] == 'now') {
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>快速安装进度</title>
        <style>
            body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; padding: 20px; line-height: 1.6; }
            .container { max-width: 800px; margin: 0 auto; background: #2d2d2d; padding: 30px; border-radius: 10px; }
            .success { color: #4CAF50; }
            .error { color: #f44336; }
            .warning { color: #ff9800; }
            .step { margin: 15px 0; padding: 15px; background: #333; border-radius: 8px; }
            .file-item { margin: 5px 0; padding: 8px; background: #404040; border-radius: 4px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 小仙元码模版快速安装</h1>
            
            <?php
            $success_count = 0;
            $error_count = 0;
            
            // 文件映射关系
            $file_mappings = [
                'default/index_new.htm' => 'templets/default/index.htm',
                'default/list_new.htm' => 'templets/default/list_default.htm',
                'default/article_new.htm' => 'templets/default/article_article.htm',
                'default/head_new.htm' => 'templets/default/head.htm',
                'default/footer_new.htm' => 'templets/default/footer.htm',
                'default/style/main.css' => 'templets/default/style/main.css',
                'default/js/main.js' => 'templets/default/js/main.js'
            ];
            
            // 图片文件
            $image_files = [
                'default/images/logo.svg' => 'templets/default/images/logo.png',
                'default/images/banner.svg' => 'templets/default/images/banner.jpg',
                'default/images/placeholder.svg' => 'templets/default/images/placeholder.jpg'
            ];
            
            // 合并所有文件
            $all_files = array_merge($file_mappings, $image_files);
            
            echo '<div class="step">';
            echo '<h3>📁 创建必要目录</h3>';
            
            $dirs_to_create = [
                'templets/default/style',
                'templets/default/js', 
                'templets/default/images'
            ];
            
            foreach ($dirs_to_create as $dir) {
                if (!is_dir($dir)) {
                    if (@mkdir($dir, 0755, true)) {
                        echo "<div class='file-item success'>✅ 创建目录: $dir</div>";
                    } else {
                        echo "<div class='file-item error'>❌ 无法创建目录: $dir</div>";
                        $error_count++;
                    }
                } else {
                    echo "<div class='file-item success'>✅ 目录已存在: $dir</div>";
                }
            }
            echo '</div>';
            
            echo '<div class="step">';
            echo '<h3>📄 复制模版文件</h3>';
            
            foreach ($all_files as $source => $target) {
                echo "<div class='file-item'>";
                
                if (file_exists($source)) {
                    // 确保目标目录存在
                    $target_dir = dirname($target);
                    if (!is_dir($target_dir)) {
                        @mkdir($target_dir, 0755, true);
                    }
                    
                    if (copy($source, $target)) {
                        echo "<span class='success'>✅ 复制成功:</span> $source → $target";
                        $success_count++;
                    } else {
                        echo "<span class='error'>❌ 复制失败:</span> $source → $target";
                        $error_count++;
                    }
                } else {
                    echo "<span class='warning'>⚠️ 源文件不存在:</span> $source";
                    
                    // 对于图片文件，创建占位符
                    if (strpos($source, 'images/') !== false) {
                        $target_dir = dirname($target);
                        if (!is_dir($target_dir)) {
                            @mkdir($target_dir, 0755, true);
                        }
                        
                        $placeholder = "<!-- 占位符文件，请替换为实际图片 -->";
                        if (file_put_contents($target, $placeholder)) {
                            echo " <span class='warning'>(已创建占位符)</span>";
                        }
                    }
                    $error_count++;
                }
                echo "</div>";
            }
            echo '</div>';
            
            // 显示安装结果
            echo '<div class="step">';
            echo '<h3>📊 安装结果</h3>';
            
            if ($error_count == 0) {
                echo '<p class="success">🎉 模版安装完成！所有文件都已成功复制。</p>';
            } else {
                echo "<p class='warning'>⚠️ 安装完成，成功: $success_count 个，错误: $error_count 个</p>";
            }
            
            echo '<h4>📝 后续步骤：</h4>';
            echo '<ol>';
            echo '<li>登录DedeCMS后台 (通常是 /dede/)</li>';
            echo '<li>进入"生成" → "更新主页HTML"</li>';
            echo '<li>进入"生成" → "更新栏目HTML"</li>';
            echo '<li>进入"生成" → "更新文档HTML"</li>';
            echo '<li>清除浏览器缓存，查看效果</li>';
            echo '</ol>';
            
            echo '<h4>🔍 检查安装结果：</h4>';
            echo '<ul>';
            foreach ($file_mappings as $source => $target) {
                $exists = file_exists($target);
                $status = $exists ? '<span class="success">✅ 存在</span>' : '<span class="error">❌ 缺失</span>';
                echo "<li>$target - $status</li>";
            }
            echo '</ul>';
            
            echo '</div>';
            ?>
            
        </div>
    </body>
    </html>
    <?php
    exit;
}

// 显示安装界面
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小仙元码模版 - 快速安装</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff; 
            padding: 20px; 
            line-height: 1.6;
            min-height: 100vh;
            margin: 0;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: rgba(45, 45, 45, 0.9);
            padding: 40px; 
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #00d4ff;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #00d4ff;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .btn { 
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #fff; 
            padding: 15px 30px; 
            border: none; 
            border-radius: 25px; 
            cursor: pointer; 
            text-decoration: none; 
            display: inline-block; 
            margin: 10px 5px;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn:hover { 
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
        }
        .info-box {
            background: #333;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #00d4ff;
        }
        .file-check {
            background: #333;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .file-item {
            padding: 8px 12px;
            background: #404040;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        .exists { color: #4CAF50; }
        .missing { color: #f44336; }
        .warning { color: #ff9800; }
        pre {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border-left: 4px solid #00d4ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 小仙元码模版</h1>
            <p>快速安装程序 - 专为当前目录结构优化</p>
        </div>
        
        <div class="info-box">
            <h3>📋 安装信息</h3>
            <p><strong>当前目录:</strong> <?php echo $current_dir; ?></p>
            <p><strong>DedeCMS检测:</strong> 
                <?php 
                if (file_exists('include/common.inc.php')) {
                    echo '<span class="exists">✅ 已检测到DedeCMS</span>';
                } else {
                    echo '<span class="missing">❌ 未检测到DedeCMS</span>';
                }
                ?>
            </p>
            <p><strong>模版目录:</strong> 
                <?php 
                if (is_dir('templets/default')) {
                    echo '<span class="exists">✅ templets/default 存在</span>';
                } else {
                    echo '<span class="warning">⚠️ templets/default 不存在（将自动创建）</span>';
                }
                ?>
            </p>
        </div>
        
        <div class="file-check">
            <h3>📄 源文件检查</h3>
            <div class="file-list">
                <?php
                $source_files = [
                    'default/index_new.htm',
                    'default/list_new.htm', 
                    'default/article_new.htm',
                    'default/head_new.htm',
                    'default/footer_new.htm',
                    'default/style/main.css',
                    'default/js/main.js'
                ];
                
                $all_exist = true;
                foreach ($source_files as $file) {
                    $exists = file_exists($file);
                    if (!$exists) $all_exist = false;
                    $class = $exists ? 'exists' : 'missing';
                    $icon = $exists ? '✅' : '❌';
                    echo "<div class='file-item $class'>$icon $file</div>";
                }
                ?>
            </div>
        </div>
        
        <?php if ($all_exist): ?>
        <div class="info-box">
            <h3>🚀 准备安装</h3>
            <p>所有源文件都已找到，可以开始安装模版。</p>
            <p><strong>安装将执行以下操作：</strong></p>
            <ul>
                <li>创建必要的目录结构</li>
                <li>复制模版文件到 templets/default/ 目录</li>
                <li>复制样式和脚本文件</li>
                <li>创建图片占位符文件</li>
            </ul>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="?install=now" class="btn">🚀 开始安装模版</a>
            </div>
        </div>
        <?php else: ?>
        <div class="info-box">
            <h3>❌ 无法安装</h3>
            <p>部分源文件缺失，请检查文件是否完整上传。</p>
        </div>
        <?php endif; ?>
        
        <div class="info-box">
            <h3>📖 手动安装方法</h3>
            <p>如果自动安装失败，您可以手动复制文件：</p>
            <pre>
# Windows 命令行
copy default\index_new.htm templets\default\index.htm
copy default\list_new.htm templets\default\list_default.htm
copy default\article_new.htm templets\default\article_article.htm
copy default\head_new.htm templets\default\head.htm
copy default\footer_new.htm templets\default\footer.htm
copy default\style\main.css templets\default\style\main.css
copy default\js\main.js templets\default\js\main.js

# Linux/Mac 命令行  
cp default/index_new.htm templets/default/index.htm
cp default/list_new.htm templets/default/list_default.htm
cp default/article_new.htm templets/default/article_article.htm
cp default/head_new.htm templets/default/head.htm
cp default/footer_new.htm templets/default/footer.htm
cp default/style/main.css templets/default/style/main.css
cp default/js/main.js templets/default/js/main.js
            </pre>
        </div>
    </div>
</body>
</html>
