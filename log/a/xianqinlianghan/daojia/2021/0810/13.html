<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《庄子》·内篇·齐物论_万婕源码网</title>
<meta name="keywords" content="《,庄子,》,内篇,齐物,论,南郭,子,綦隐,几," />
<meta name="description" content="南郭子綦隐几而坐，仰天而嘘，嗒焉似丧其耦。颜成子游立侍乎前，曰：何居乎？形固可使如槁木，而心固可使如死灰乎？今之隐几者，非昔之隐几者也。子綦曰：偃，不亦善乎而问之" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<style type="text/css">
/* 小仙元码暗色主题样式 - 文章详情页 */
* { box-sizing: border-box; }
html { background: #0a0a0a; }
body {
    font: 14px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: #fff !important;
    line-height: 1.6;
}
a { color: #00d4ff !important; text-decoration: none; transition: all 0.3s ease; }
a:hover { color: #0099cc !important; }

/* 面包屑导航 */
.position {
    background: #1a1a1a !important;
    padding: 15px 20px !important;
    border-bottom: 1px solid #333 !important;
    margin-bottom: 20px !important;
}
.position a { color: #00d4ff !important; }

/* 文章容器 */
.w960 { background: transparent !important; }
.tbox {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    margin-bottom: 30px !important;
}
.tbox dt {
    background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%) !important;
    border: none !important;
    height: 50px !important;
    border-radius: 15px 15px 0 0 !important;
}
.tbox dt strong {
    color: #00d4ff !important;
    line-height: 50px !important;
    font-size: 1.3rem !important;
    padding-left: 20px !important;
}
.tbox dd {
    background: #1a1a1a !important;
    border: none !important;
    padding: 30px !important;
    border-radius: 0 0 15px 15px !important;
}

/* 文章标题 */
.arc_title {
    color: #fff !important;
    font-size: 2.5rem !important;
    margin-bottom: 20px !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 15px !important;
}

/* 文章信息 */
.arc_info {
    color: #ccc !important;
    margin-bottom: 20px !important;
    padding: 15px 0 !important;
    border-bottom: 1px solid #333 !important;
}
.arc_info span { margin-right: 20px !important; }

/* 文章内容 */
.arc_content, .arc_body {
    color: #ccc !important;
    line-height: 1.8 !important;
    margin: 30px 0 !important;
}
.arc_content h1, .arc_content h2, .arc_content h3, .arc_content h4, .arc_content h5, .arc_content h6,
.arc_body h1, .arc_body h2, .arc_body h3, .arc_body h4, .arc_body h5, .arc_body h6 {
    color: #fff !important;
    margin: 30px 0 20px 0 !important;
}
.arc_content h2, .arc_body h2 { color: #00d4ff !important; }
.arc_content p, .arc_body p { margin-bottom: 20px !important; }
.arc_content img, .arc_body img {
    max-width: 100% !important;
    border-radius: 8px !important;
    margin: 20px 0 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* 文章缩略图 */
.arc_pic {
    text-align: center !important;
    margin: 20px 0 !important;
}
.arc_pic img {
    max-width: 100% !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

/* 标签 */
.arc_tags { margin-top: 30px !important; }
.arc_tags a, .tags a {
    background: #333 !important;
    color: #00d4ff !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    text-decoration: none !important;
    display: inline-block !important;
    margin: 5px 10px 5px 0 !important;
    transition: all 0.3s ease !important;
}
.arc_tags a:hover, .tags a:hover {
    background: #00d4ff !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
}

/* 侧边栏 */
.sidebar { background: transparent !important; }
.sidebar .tbox { margin-bottom: 25px !important; }
.sidebar ul li {
    background: none !important;
    border-bottom: 1px solid #333 !important;
    padding: 8px 0 !important;
}
.sidebar ul li a { color: #ccc !important; }
.sidebar ul li a:hover { color: #00d4ff !important; }

/* 评论区域 */
.feedback {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    padding: 30px !important;
    margin-top: 30px !important;
}
.feedback h3 {
    color: #00d4ff !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 10px !important;
    margin-bottom: 20px !important;
}

/* 评论表单 */
.feedback textarea {
    background: #333 !important;
    border: 1px solid #555 !important;
    color: #fff !important;
    border-radius: 8px !important;
    padding: 15px !important;
    width: 100% !important;
    resize: vertical !important;
}
.feedback textarea:focus {
    border-color: #00d4ff !important;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2) !important;
    outline: none !important;
}

/* 按钮样式 */
.btn, input[type="submit"], input[type="button"], button {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
    border: none !important;
    color: #fff !important;
    border-radius: 25px !important;
    padding: 12px 24px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: bold !important;
}
.btn:hover, input[type="submit"]:hover, input[type="button"]:hover, button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .w960 { padding: 0 15px !important; }
    .arc_title { font-size: 1.8rem !important; }
    .tbox dd { padding: 20px !important; }
    .arc_info { flex-direction: column !important; gap: 10px !important; }
}
</style>
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=13">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=13";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	<li class='hover'><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xianqinlianghan/'>先秦两汉</a> > <a href='/a/xianqinlianghan/daojia/'>道家</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《庄子》·内篇·齐物论</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:06<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=13&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">南郭子綦隐几而坐，仰天而嘘，嗒焉似丧其耦。颜成子游立侍乎前，曰：何居乎？形固可使如槁木，而心固可使如死灰乎？今之隐几者，非昔之隐几者也。子綦曰：偃，不亦善乎而问之</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;">南郭子綦隐几而坐，仰天而嘘，嗒焉似丧其耦。颜成子游立侍乎前，曰：&ldquo;何居乎？形固可使如槁木，而心固可使如死灰乎？今之隐几者，非昔之隐几者也。&rdquo;子綦曰：&ldquo;偃，不亦善乎而问之也！今者吾丧我，汝知之乎？女闻人籁而未闻地籁，女闻地籁而未闻天籁夫！&rdquo;子游曰：&ldquo;敢问其方。&rdquo;子綦曰：&ldquo;夫大块噫气，其名为风。是唯无作，作则万窍怒呺。而独不闻之翏翏乎？山林之畏佳，大木百围之窍穴，似鼻，似口，似耳，似枅，似圈，似臼，似洼者，似污者；激者，謞者，叱者，吸者，叫者，譹者，宎者，咬者，前者唱于而随者唱喁。泠风则小和，飘风则大和，厉风济则众窍为虚。而独不见之调调、之刁刁乎？&rdquo;子游曰：&ldquo;地籁则众窍是已，人籁则比竹是已。敢问天籁。&rdquo;子綦曰：&ldquo;夫吹万不同，而使其自已</span><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; text-size-adjust: auto;">1</sup><span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;">也，咸其自取，怒者其谁邪！&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">大知闲闲，小知闲闲；大言炎炎，小言詹詹。其寐也魂交，其觉也形开，与接为构，日以心鬭。缦者，窖者，密者。小恐惴惴，大恐缦缦。其发若机栝，其司是非之谓也；其留如诅盟，其守胜之谓也；其杀如秋冬，以言其日消也；其溺之所为之，不可使复之也；其厌也如缄，以言其老洫也；近死之心，莫使复阳也。喜怒哀乐，虑叹变慹，姚佚启态；乐出虚，蒸成菌。日夜相代乎前，而莫知其所萌。已乎已乎！旦暮得此，其所由以生乎！<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">非彼无我，非我无所取。是亦近矣，而不知其所为使。若有真宰，而特不得其眹。可行已信，而不见其形，有情而无形。百骸、九窍、六藏，赅而存焉，吾谁与为亲？汝皆说之乎？其有私焉？如是皆有，为臣妾乎，其臣妾不足以相治乎。其递相为君臣乎，其有真君存焉。如求得其情与不得，无益损乎其真。一受其成形，不亡以待尽。与物相刃相靡，其行尽如驰，而莫之能止，不亦悲乎！终身役役而不见其成功，苶然疲役而不知其所归，可不哀邪！人谓之不死，奚益？其形化，其心与之然，可不谓大哀乎？人之生也，固若是芒乎！其我独芒，而人亦有不芒者乎！<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">夫随其成心而师之，谁独且无师乎？奚必知代而心自取者有之？愚者与有焉。未成乎心而有是非，是今日适越而昔至也。是以无有为有。无有为有，虽有神禹，且不能知，吾独且柰何哉！夫言非吹也。言者有言，其所言者特未定也。果有言邪？其未尝有言邪？其以为异于鷇音，亦有辩乎，其无辩乎？道恶乎隐而有真伪？言恶乎隐而有是非？道恶乎往而不存？言恶乎存而不可？道隐于小成，言隐于荣华。故有儒、墨之是非，以是其所非，而非其所是。欲是其所非而非其所是，则莫若以明。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">物无非彼，物无非是。自彼则不见，自知则知之。故曰：彼出于是，是亦因彼。彼是，方生之说也。虽然，方生方死，方死方生；方可方不可，方不可方可；因是因非，因非因是。是以圣人不由，而照之于天，亦因是也。是亦彼也，彼亦是也。彼亦一是非，此亦一是非。果且有彼是乎哉？果且无彼是乎哉？彼是莫得其偶，谓之道枢。枢始得其环中，以应无穷。是亦一无穷，非亦一无穷也。故曰&ldquo;莫若以明&rdquo;。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">以指喻指之非指，不若以非指喻指之非指也；以马喻马之非马，不若以非马喻马之非马也。天地，一指也；万物，一马也。</span><br style="padding: 0px; margin: 0px; text-size-adjust: auto;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">可乎可，不可乎不可。道行之而成，物谓之而然。恶乎然？然于然。恶乎不然？不然于不然。物固有所然，物固有所可。无物不然，无物不可。故为是举莛与楹，厉与西施，恢恑憰怪，道通为一。</span><br style="padding: 0px; margin: 0px; text-size-adjust: auto;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">其分也，成也；其成也，毁也。凡物无成与毁，复通为一。唯达者知通为一，为是不用而寓诸庸。庸也者，用也；用也者，通也；通也者，得也。适得而几矣。因是已。已而不知其然，谓之道。劳神明为一，而不知其同也，谓之朝三。何谓朝三？曰狙公赋芧，曰：&ldquo;朝三而莫四。&rdquo;众狙皆怒。曰：&ldquo;然则朝四而莫三。&rdquo;众狙皆悦。名实未亏，而喜怒为用，亦因是也。是以圣人和之以是非，而休乎天钧，是之谓两行。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">古之人，其知有所至矣。恶乎至？有以为未始有物者，至矣尽矣，不可以加矣。其次以为有物矣，而未始有封也。其次以为有封焉，而未始有是非也。是非之彰也，道之所以亏也。道之所以亏，爱之所以成。果且有成与亏乎哉？果且无成与亏乎哉？有成与亏，故昭氏之鼓琴也；无成与亏，故昭氏之不鼓琴也。昭文之鼓琴也，师旷之枝策也，惠子之据梧也，三子之知几乎！皆其盛者也，故载之末年。唯其好之也，以异于彼，其好之也，欲以明之彼。非所明而明之，故以坚白之昧终。而其子又以文之纶终，终身无成。若是而可谓成乎，虽我亦成也。若是而不可谓成乎，物与我无成也。是故滑疑之耀，圣人之所图也。为是不用而寓诸庸，此之谓以明。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">今且有言于此，不知其与是类乎？其与是不类乎？类与不类，相与为类，则与彼无以异矣。虽然，请尝言之。有始也者，有未始有始也者，有未始有夫未始有始也者。有有也者，有无也者，有未始有无也者，有未始有夫未始有无也者。俄而有无矣，而未知有无之果孰有孰无也。今我则已有谓矣，而未知吾所谓之其果有谓乎，其果无谓乎？<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">天下莫大于秋豪之末，而大山为小；莫寿乎殇子，而彭祖为夭。天地与我并生，而万物与我为一。既已为一矣，且得有言乎？既已谓之一矣，且得无言乎？一与言为二，二与一为三。自此以往，巧历不能得，而况其凡乎！故自无适有，以至于三，而况自有适有乎！无适焉，因是已。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">夫道未始有封，言未始有常，为是而有畛也。请言其畛：有左，有右，有伦，有义，有分，有辩，有竞，有争，此之谓八德。六合之外，圣人存而不论；六合之内，圣人论而不议。春秋经世，先王之志，圣人议而不辩。故分也者，有不分也；辩也者，有不辩也。曰：何也？圣人怀之，众人辩之以相示也。故曰：辩也者，有不见也。夫大道不称，大辩不言，大仁不仁，大廉不嗛，大勇不忮。道昭而不道，言辩而不及，仁常而不成，廉清而不信，勇忮而不成。五者园而几向方矣。故知止其所不知，至矣。孰知不言之辩，不道之道？若有能知，此之谓天府。注焉而不满，酌焉而不竭，而不知其所由来，此之谓葆光。故昔者尧问于舜曰：&ldquo;我欲伐宗、脍、胥敖，南面而不释然。其故何也？&rdquo;舜曰：&ldquo;夫三子者，犹存乎蓬艾之间。若不释然，何哉？昔者十日并出，万物皆照，而况德之进乎日者乎！&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">啮缺问乎王倪曰：&ldquo;子知物之所同是乎？&rdquo;曰：&ldquo;吾恶乎知之！&rdquo;&ldquo;子知子之所不知邪？&rdquo;曰：&ldquo;吾恶乎知之！&rdquo;&ldquo;然则物无知邪？&rdquo;曰：&ldquo;吾恶乎知之！虽然，尝试言之。庸讵知吾所谓知之非不知邪？庸讵知吾所谓不知之非知邪？且吾尝试问乎女：民湿寝则腰疾偏死，鰌然乎哉？木处则惴栗恂惧，猨猴然乎哉？三者孰知正处？民食刍豢，麋鹿食荐，蝍且甘带，鸱鸦耆鼠，四者孰知正味？猨，猵狙以为雌，麋与鹿交，鰌与鱼游。毛嫱、丽姬，人之所美也，鱼见之深入，鸟见之高飞，麋鹿见之决骤。四者孰知天下之正色哉？自我观之，仁义之端，是非之涂，樊然淆乱，吾恶能知其辩！&rdquo;啮缺曰：&ldquo;子不知利害，则至人固不知利害乎？&rdquo;王倪曰：&ldquo;至人神矣：大泽焚而不能热，河、汉冱而不能寒，疾雷破山、风振海而不能惊。若然者，乘云气，骑日月，而游乎四海之外。死生无变于己，而况利害之端乎！&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
瞿鹊子问乎长梧子曰：&ldquo;吾闻诸夫子，圣人不从事于务，不就利，不违害，不喜求，不缘道，无谓有谓，有谓无谓，而游乎尘垢之外。夫子以为孟浪之言，而我以为妙道之行也。吾子以为奚若？&rdquo;长梧子曰：&ldquo;是黄帝之所听荧也，而丘也何足以知之！且女亦大早计，见卵而求时夜，见弹而求鴞炙。予尝为女妄言之，女以妄听之，奚？旁日月，挟宇宙，为其脗合，置其滑涽，以隶相尊。众人役役，圣人愚芚，参万岁而一成纯。万物尽然，而以是相蕴。予恶乎知说生之非惑邪！予恶乎知恶死之非弱丧而不知归者邪！丽之姬，艾封人之子也。晋国之始得之也，涕泣沾襟；及其至于王所，与王同筐床，食刍豢，而后悔其泣也。予恶乎知夫死者不悔其始之蕲生乎！梦饮酒者，旦而哭泣；梦哭泣者，旦而田猎。方其梦也，不知其梦也。梦之中又占其梦焉，觉而后知其梦也。且有大觉而后知此其大梦也，而愚者自以为觉，窃窃然知之。君乎，牧乎，固哉！丘也，与女皆梦也；予谓女梦，亦梦也。是其言也，其名为吊诡。万世之后，而一遇大圣知其解者，是旦暮遇之也。既使我与若辩矣，若胜我，我不若胜，若果是也？我果非也邪？我胜若，若不吾胜，我果是也？而果非也邪？其或是也，其或非也邪？其俱是也，其俱非也邪？我与若不能相知也，则人固受其黮暗。吾谁使正之？使同乎若者正之，既与若同矣，恶能正之！使同乎我者正之，既同乎我矣，恶能正之！使异乎我与若者正之，既异乎我与若矣，恶能正之！使同乎我与若者正之，既同乎我与若矣，恶能正之！然则我与若与人俱不能相知也，而待彼也邪？何</span></span></span></span></span></span></span></span></span></span></span><span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">&nbsp;</span>
<div style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px; display: inline-block; zoom: 1;">
	<div class="sprite-unexpand" style="padding: 0px; margin: 0px; overflow: hidden; width: 11px; height: 11px; position: relative; text-indent: -100em; float: left;" title="+">
		<div style="padding: 0px; margin: 0px; background-image: url(&quot;https://ctext.org/static/sprite.png&quot;); height: 11px; left: 0px; position: absolute; top: 0px; width: 11px; background-position: 0px -476px; background-repeat: no-repeat;">
			&nbsp;</div>
	</div>
</div>
<span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">&nbsp;</span><span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;"><span class="container" style="padding: 0px; margin: 0px;"><span class="subcontents" style="padding: 0px; margin: 0px; display: inline;">化声之相待，若其不相待。和之以天倪，因之以曼衍，所以穷年也。</span></span><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px;">1</sup>谓和之以天倪？曰：是不是，然不然。是若果是也，则是之异乎不是也亦无辩；然若果然也，则然之异乎不然也亦无辩。</span><span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">&nbsp;</span>
<div style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px; display: inline-block; zoom: 1;">
	<div class="sprite-expand" style="padding: 0px; margin: 0px; overflow: hidden; width: 11px; height: 11px; position: relative; text-indent: -100em; float: left; zoom: 1;" title="+">
		<div style="padding: 0px; margin: 0px; background-image: url(&quot;https://ctext.org/static/sprite.png&quot;); height: 11px; left: 0px; position: absolute; top: 0px; width: 11px; zoom: 1; background-position: 0px -76px; background-repeat: no-repeat;">
			&nbsp;</div>
	</div>
</div>
<span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">&nbsp;</span><span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;"><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px;">2</sup>忘年忘义，振于无竟，故寓诸无竟。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">罔两问景曰：&ldquo;曩子行，今子止，曩子坐，今子起，何其无特操与？&rdquo;景曰：&ldquo;吾有待而然者邪！吾所待又有待而然者邪！吾待蛇蚹、蜩翼邪！恶识所以然？恶识所以不然？&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">昔者庄周梦为胡蝶，栩栩然胡蝶也，自喻适志与！不知周也。俄然觉，则蘧蘧然周也。不知周之梦为胡蝶与，胡蝶之梦为周与？周与胡蝶，则必有分矣。此之谓物化。</span></span></span>
      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_13'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=13;
  	var __dedeqrcode_aid=13;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',13)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',13)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(13);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/xianqinlianghan/daojia/2021/0810/12.html'>《庄子》·外篇·胠箧</a> </li>
     <li>下一篇：<a href='/a/xianqinlianghan/daojia/2021/0810/14.html'>《庄子》·外篇·骈拇</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=13" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=13&title=《庄子》·内篇·齐物论" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=13" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="13" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=13">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=13&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '13');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/xianqinlianghan/rujia/'>儒家</a></li>
      
      <li><a href='/a/xianqinlianghan/mojia/'>墨家</a></li>
      <li><a href='/a/xianqinlianghan/daojia/' class='thisclass'>道家</a></li>
      <li><a href='/a/xianqinlianghan/fajia/'>法家</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/xianqinlianghan/daojia/2021/0810/16.html">《庄子》·内篇·逍遥游</a>
       <p>北冥有鱼，其名为鲲。鲲之大，不知其几千里也。化而为鸟，其...</p>
      </li>
<li><a href="/a/xianqinlianghan/daojia/2021/0810/15.html">《庄子》·内篇·养生主</a>
       <p>吾生也有涯，而知也无涯。以有涯随无涯，殆已；已而为知者，...</p>
      </li>
<li><a href="/a/xianqinlianghan/daojia/2021/0810/14.html">《庄子》·外篇·骈拇</a>
       <p>骈拇枝指，出乎性哉！而侈于德。附赘县疣，出乎形哉！而侈于...</p>
      </li>
<li><a href="/a/xianqinlianghan/daojia/2021/0810/13.html">《庄子》·内篇·齐物论</a>
       <p>南郭子綦隐几而坐，仰天而嘘，嗒焉似丧其耦。颜成子游立侍乎...</p>
      </li>
<li><a href="/a/xianqinlianghan/daojia/2021/0810/12.html">《庄子》·外篇·胠箧</a>
       <p>将为胠箧、探囊、发匮之盗而为守备，则必摄缄、縢，固扃、鐍...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xianqinlianghan/daojia/2021/0810/12.html">《庄子》·外篇·胠箧</a></li>
<li><a href="/a/xianqinlianghan/daojia/2021/0810/13.html">《庄子》·内篇·齐物论</a></li>
<li><a href="/a/xianqinlianghan/daojia/2021/0810/14.html">《庄子》·外篇·骈拇</a></li>
<li><a href="/a/xianqinlianghan/daojia/2021/0810/16.html">《庄子》·内篇·逍遥游</a></li>
<li><a href="/a/xianqinlianghan/daojia/2021/0810/15.html">《庄子》·内篇·养生主</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
