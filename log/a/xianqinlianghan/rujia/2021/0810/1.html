<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《论语》·为政_万婕源码网</title>
<meta name="keywords" content="《,论语,》,为政,子曰,为政以德,譬如,北辰,居," />
<meta name="description" content="子曰：为政以德，譬如北辰，居其所而众星共之。 孔子说：管理国家要以身做则。如同北极星，安然不动而衆星绕之。 子曰：诗三百，一言以蔽之，曰思无邪。 孔子说：《诗经》三百" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<style type="text/css">
/* 小仙元码暗色主题样式 - 文章详情页 */
* { box-sizing: border-box; }
html { background: #0a0a0a; }
body {
    font: 14px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: #fff !important;
    line-height: 1.6;
}
a { color: #00d4ff !important; text-decoration: none; transition: all 0.3s ease; }
a:hover { color: #0099cc !important; }

/* 面包屑导航 */
.position {
    background: #1a1a1a !important;
    padding: 15px 20px !important;
    border-bottom: 1px solid #333 !important;
    margin-bottom: 20px !important;
}
.position a { color: #00d4ff !important; }

/* 文章容器 */
.w960 { background: transparent !important; }
.tbox {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    margin-bottom: 30px !important;
}
.tbox dt {
    background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%) !important;
    border: none !important;
    height: 50px !important;
    border-radius: 15px 15px 0 0 !important;
}
.tbox dt strong {
    color: #00d4ff !important;
    line-height: 50px !important;
    font-size: 1.3rem !important;
    padding-left: 20px !important;
}
.tbox dd {
    background: #1a1a1a !important;
    border: none !important;
    padding: 30px !important;
    border-radius: 0 0 15px 15px !important;
}

/* 文章标题 */
.arc_title {
    color: #fff !important;
    font-size: 2.5rem !important;
    margin-bottom: 20px !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 15px !important;
}

/* 文章信息 */
.arc_info {
    color: #ccc !important;
    margin-bottom: 20px !important;
    padding: 15px 0 !important;
    border-bottom: 1px solid #333 !important;
}
.arc_info span { margin-right: 20px !important; }

/* 文章内容 */
.arc_content, .arc_body {
    color: #ccc !important;
    line-height: 1.8 !important;
    margin: 30px 0 !important;
}
.arc_content h1, .arc_content h2, .arc_content h3, .arc_content h4, .arc_content h5, .arc_content h6,
.arc_body h1, .arc_body h2, .arc_body h3, .arc_body h4, .arc_body h5, .arc_body h6 {
    color: #fff !important;
    margin: 30px 0 20px 0 !important;
}
.arc_content h2, .arc_body h2 { color: #00d4ff !important; }
.arc_content p, .arc_body p { margin-bottom: 20px !important; }
.arc_content img, .arc_body img {
    max-width: 100% !important;
    border-radius: 8px !important;
    margin: 20px 0 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* 文章缩略图 */
.arc_pic {
    text-align: center !important;
    margin: 20px 0 !important;
}
.arc_pic img {
    max-width: 100% !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

/* 标签 */
.arc_tags { margin-top: 30px !important; }
.arc_tags a, .tags a {
    background: #333 !important;
    color: #00d4ff !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    text-decoration: none !important;
    display: inline-block !important;
    margin: 5px 10px 5px 0 !important;
    transition: all 0.3s ease !important;
}
.arc_tags a:hover, .tags a:hover {
    background: #00d4ff !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
}

/* 侧边栏 */
.sidebar { background: transparent !important; }
.sidebar .tbox { margin-bottom: 25px !important; }
.sidebar ul li {
    background: none !important;
    border-bottom: 1px solid #333 !important;
    padding: 8px 0 !important;
}
.sidebar ul li a { color: #ccc !important; }
.sidebar ul li a:hover { color: #00d4ff !important; }

/* 评论区域 */
.feedback {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    padding: 30px !important;
    margin-top: 30px !important;
}
.feedback h3 {
    color: #00d4ff !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 10px !important;
    margin-bottom: 20px !important;
}

/* 评论表单 */
.feedback textarea {
    background: #333 !important;
    border: 1px solid #555 !important;
    color: #fff !important;
    border-radius: 8px !important;
    padding: 15px !important;
    width: 100% !important;
    resize: vertical !important;
}
.feedback textarea:focus {
    border-color: #00d4ff !important;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2) !important;
    outline: none !important;
}

/* 按钮样式 */
.btn, input[type="submit"], input[type="button"], button {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
    border: none !important;
    color: #fff !important;
    border-radius: 25px !important;
    padding: 12px 24px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: bold !important;
}
.btn:hover, input[type="submit"]:hover, input[type="button"]:hover, button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .w960 { padding: 0 15px !important; }
    .arc_title { font-size: 1.8rem !important; }
    .tbox dd { padding: 20px !important; }
    .arc_info { flex-direction: column !important; gap: 10px !important; }
}
</style>
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=1">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=1";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	<li class='hover'><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xianqinlianghan/'>先秦两汉</a> > <a href='/a/xianqinlianghan/rujia/'>儒家</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《论语》·为政</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:02<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=1&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">子曰：为政以德，譬如北辰，居其所而众星共之。 孔子说：管理国家要以身做则。如同北极星，安然不动而衆星绕之。 子曰：诗三百，一言以蔽之，曰思无邪。 孔子说：《诗经》三百</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;为政以德，譬如北辰，居其所而众星共之。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;管理国家要以身做则。如同北极星，安然不动而衆星绕之。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;诗三百，一言以蔽之，曰&lsquo;思无邪&rsquo;。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;《诗经》三百首，用一句话可以概括，即：&lsquo;思想纯洁&rsquo;。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;道之以政，齐之以刑，民免而无耻；道之以德，齐之以礼，有耻且格。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;以政令来管理，以刑法来约束，百姓虽不敢犯罪，但不以犯罪为耻；以道德来引导，以礼法来约束，百姓不仅遵纪守法，而且引以为荣。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;吾十有五而志于学，三十而立，四十而不惑，五十而知天命，六十而耳顺，七十而从心所欲，不逾矩。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;我十五岁立志于学习，三十岁有所建树，四十岁不困惑，五十理解自然规律，六十明辨是非，七十随心所欲，不违规。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">孟懿子问孝。子曰：&ldquo;无违。&rdquo;樊迟御，子告之曰：&ldquo;孟孙问孝于我，我对曰&lsquo;无违&rsquo;。&rdquo;樊迟曰：&ldquo;何谓也？&rdquo;子曰：&ldquo;生事之以礼；死葬之以礼，祭之以礼。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孟懿子问孝，孔子说：&ldquo;不违礼。&rdquo;樊迟驾车时，孔子告诉他：&ldquo;孟孙问孝于我，我说：&lsquo;不违礼&rsquo;。&rdquo;樊迟说：&ldquo;什麽意思？&rdquo;孔子说：&ldquo;活著时按礼侍奉；死之后按礼安葬、按礼纪念。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">孟武伯问孝。子曰：&ldquo;父母唯其疾之忧。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孟武伯问孝，孔子说：&ldquo;父母只有在子女生病时才担忧。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子游问孝。子曰：&ldquo;今之孝者，是谓能养。至于犬马，皆能有养；不敬，何以别乎？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">子游问孝，孔子说：&ldquo;现在的孝顺，只是能赡养老人。即使是犬马，都会得到饲养。不敬重，有何区别？&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子夏问孝。子曰：&ldquo;色难。有事弟子服其劳，有酒食先生馔，曾是以为孝乎？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">子夏问孝，孔子说：&ldquo;和颜悦色很难。有事情，子女都去做；有酒肉，老人随便吃；这样就是孝吗？&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;吾与回言终日，不违如愚。退而省其私，亦足以发。回也，不愚。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;我曾整天同颜回谈话，他从不反驳，象笨人。后来观察，发现他理解透彻、发挥自如，他不笨。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;视其所以，观其所由，察其所安。人焉廋哉？人焉廋哉？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;分析其动机，观察其行动，了解其态度；人藏哪去？人藏哪去？&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;温故而知新，可以为师矣。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;温习旧知识时，能有新收获，就可以做老师了。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;君子不器。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;君子不能象器皿一样，只有一种用途。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子贡问君子。子曰：&ldquo;先行其言，而后从之。&rdquo;</span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span><br style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;" />
<span style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 16px; caret-color: rgb(0, 0, 0);">子贡问君子，孔子说：&ldquo;先将要说的做出来，然后再说。&rdquo;</span><br style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;" />
<br style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;" />
<span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;君子周而不比，小人比而不周。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;君子团结群衆而不拉帮结派，小人拉帮结派而不团结群衆。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;学而不思则罔，思而不学则殆。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;读书不想事，越学越糊涂；想事不读书，越想越头痛。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;攻乎异端，斯害也已！&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;走入异端邪说中，就是祸害。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;由！诲女知之乎？知之为知之，不知为不知，是知也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;子路啊，我告诉你，知道吗？知道的就是知道的，不知道的就是不知道的，这就关于知道的真谛。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子张学干禄。子曰：&ldquo;多闻阙疑，慎言其馀，则寡尤；多见阙殆，慎行其馀，则寡悔。言寡尤，行寡悔，禄在其中矣。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">子张学做官，孔子说：&ldquo;多听，不要说没把握的话，即使有把握，说话也要谨慎，就能减少错误；多看，不要做没把握的事，即使有把握，行动也要谨慎，则能减少后悔。说话错少，行动悔少，就能当好官了。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">哀公问曰：&ldquo;何为则民服？&rdquo;孔子对曰：&ldquo;举直错诸枉，则民服；举枉错诸直，则民不服。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">哀公问：&ldquo;怎样使人心服？&rdquo;孔子说：&ldquo;以正压邪，则人心服；以邪压正，则人心不服。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">季康子问：&ldquo;使民敬、忠以劝，如之何？&rdquo;子曰：&ldquo;临之以庄则敬，孝慈则忠，举善而教不能，则劝。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">季康子问：&ldquo;怎样使人尊敬、忠诚、勤勉？&rdquo;孔子说：&ldquo;举止端庄，能赢得尊敬；敬老爱幼，能赢得忠诚；任用贤良、培养人才，能使人勤勉。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">或谓孔子曰：&ldquo;子奚不为政？&rdquo;子曰：&ldquo;《</span><a class="popup" href="https://ctext.org/shang-shu/zhs" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; font-size: 18.6667px; text-size-adjust: auto;">书</a><span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">》云：&lsquo;孝乎惟孝、友于兄弟，施于有政。&rsquo;是亦为政，奚其为为政？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">有人问孔子：&ldquo;先生为何不从政？&rdquo;孔子说：&ldquo;孝啊，就是孝顺父母、兄弟友爱，以这种品德影响政治，这就是参政，难道只有做官才算从政？&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;人而无信，不知其可也。大车无輗，小车无軏，其何以行之哉？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;人无信誉，不知能干什麽？就象大车没有车轴，小车没有车轴，怎麽能启动？&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子张问：&ldquo;十世可知也？&rdquo;子曰：&ldquo;殷因于夏礼，所损益，可知也；周因于殷礼，所损益，可知也；其或继周者，虽百世可知也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">子张问：&ldquo;十代以后的社会制度和道德规范可以知道吗？&rdquo;孔子说：&ldquo;商朝继承夏朝，改动了多少，可以知道；周朝继承商朝，改动了多少，也可以知道；以后的朝代继承周朝，即使百代，同样可以推测。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">子曰：&ldquo;非其鬼而祭之，谄也。见义不为，无勇也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">孔子说：&ldquo;祭奠别人的先人，是谄媚；遇到符合道义的事不敢做，是懦夫。&rdquo;</span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span>
      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_1'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=1;
  	var __dedeqrcode_aid=1;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',1)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',1)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(1);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：没有了 </li>
     <li>下一篇：<a href='/a/xianqinlianghan/rujia/2021/0810/2.html'>《论语》·学而</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=1" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=1&title=《论语》·为政" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=1" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="1" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=1">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=1&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '1');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      <li><a href='/a/xianqinlianghan/rujia/' class='thisclass'>儒家</a></li>
      <li><a href='/a/xianqinlianghan/mojia/'>墨家</a></li>
      
      <li><a href='/a/xianqinlianghan/daojia/'>道家</a></li>
      
      <li><a href='/a/xianqinlianghan/fajia/'>法家</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/xianqinlianghan/rujia/2021/0810/6.html">《论语》·里仁</a>
       <p>子曰：里仁为美。择不处仁，焉得知？ 孔子说：同品德高尚的人...</p>
      </li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/4.html">《孟子》·梁惠王上</a>
       <p>孟子见梁惠王。王曰：叟不远千里而来，亦将有以利吾国乎？...</p>
      </li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/3.html">《孟子》·梁惠王下</a>
       <p>庄暴见孟子，曰：暴见于王，王语暴以好乐，暴未有以对也。曰...</p>
      </li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/2.html">《论语》·学而</a>
       <p>子曰：学而时习之，不亦说乎？有朋自远方来，不亦乐乎？人不...</p>
      </li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/1.html">《论语》·为政</a>
       <p>子曰：为政以德，譬如北辰，居其所而众星共之。 孔子说：管理...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xianqinlianghan/rujia/2021/0810/3.html">《孟子》·梁惠王下</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/6.html">《论语》·里仁</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/4.html">《孟子》·梁惠王上</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/2.html">《论语》·学而</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/1.html">《论语》·为政</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/5.html">《论语》·先进</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
