<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《孟子》·梁惠王上_万婕源码网</title>
<meta name="keywords" content="《,孟子,》,梁惠王,上,孟子,见,梁惠王,。," />
<meta name="description" content="孟子见梁惠王。王曰：叟不远千里而来，亦将有以利吾国乎？ 孟子对曰：王何必曰利？亦有仁义而已矣。王曰何以利吾国？大夫曰何以利吾家？士庶人曰何以利吾身？上下交征利而国危" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<style type="text/css">
/* 小仙元码暗色主题样式 - 文章详情页 */
* { box-sizing: border-box; }
html { background: #0a0a0a; }
body {
    font: 14px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: #fff !important;
    line-height: 1.6;
}
a { color: #00d4ff !important; text-decoration: none; transition: all 0.3s ease; }
a:hover { color: #0099cc !important; }

/* 面包屑导航 */
.position {
    background: #1a1a1a !important;
    padding: 15px 20px !important;
    border-bottom: 1px solid #333 !important;
    margin-bottom: 20px !important;
}
.position a { color: #00d4ff !important; }

/* 文章容器 */
.w960 { background: transparent !important; }
.tbox {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    margin-bottom: 30px !important;
}
.tbox dt {
    background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%) !important;
    border: none !important;
    height: 50px !important;
    border-radius: 15px 15px 0 0 !important;
}
.tbox dt strong {
    color: #00d4ff !important;
    line-height: 50px !important;
    font-size: 1.3rem !important;
    padding-left: 20px !important;
}
.tbox dd {
    background: #1a1a1a !important;
    border: none !important;
    padding: 30px !important;
    border-radius: 0 0 15px 15px !important;
}

/* 文章标题 */
.arc_title {
    color: #fff !important;
    font-size: 2.5rem !important;
    margin-bottom: 20px !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 15px !important;
}

/* 文章信息 */
.arc_info {
    color: #ccc !important;
    margin-bottom: 20px !important;
    padding: 15px 0 !important;
    border-bottom: 1px solid #333 !important;
}
.arc_info span { margin-right: 20px !important; }

/* 文章内容 */
.arc_content, .arc_body {
    color: #ccc !important;
    line-height: 1.8 !important;
    margin: 30px 0 !important;
}
.arc_content h1, .arc_content h2, .arc_content h3, .arc_content h4, .arc_content h5, .arc_content h6,
.arc_body h1, .arc_body h2, .arc_body h3, .arc_body h4, .arc_body h5, .arc_body h6 {
    color: #fff !important;
    margin: 30px 0 20px 0 !important;
}
.arc_content h2, .arc_body h2 { color: #00d4ff !important; }
.arc_content p, .arc_body p { margin-bottom: 20px !important; }
.arc_content img, .arc_body img {
    max-width: 100% !important;
    border-radius: 8px !important;
    margin: 20px 0 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* 文章缩略图 */
.arc_pic {
    text-align: center !important;
    margin: 20px 0 !important;
}
.arc_pic img {
    max-width: 100% !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

/* 标签 */
.arc_tags { margin-top: 30px !important; }
.arc_tags a, .tags a {
    background: #333 !important;
    color: #00d4ff !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    text-decoration: none !important;
    display: inline-block !important;
    margin: 5px 10px 5px 0 !important;
    transition: all 0.3s ease !important;
}
.arc_tags a:hover, .tags a:hover {
    background: #00d4ff !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
}

/* 侧边栏 */
.sidebar { background: transparent !important; }
.sidebar .tbox { margin-bottom: 25px !important; }
.sidebar ul li {
    background: none !important;
    border-bottom: 1px solid #333 !important;
    padding: 8px 0 !important;
}
.sidebar ul li a { color: #ccc !important; }
.sidebar ul li a:hover { color: #00d4ff !important; }

/* 评论区域 */
.feedback {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    padding: 30px !important;
    margin-top: 30px !important;
}
.feedback h3 {
    color: #00d4ff !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 10px !important;
    margin-bottom: 20px !important;
}

/* 评论表单 */
.feedback textarea {
    background: #333 !important;
    border: 1px solid #555 !important;
    color: #fff !important;
    border-radius: 8px !important;
    padding: 15px !important;
    width: 100% !important;
    resize: vertical !important;
}
.feedback textarea:focus {
    border-color: #00d4ff !important;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2) !important;
    outline: none !important;
}

/* 按钮样式 */
.btn, input[type="submit"], input[type="button"], button {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
    border: none !important;
    color: #fff !important;
    border-radius: 25px !important;
    padding: 12px 24px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: bold !important;
}
.btn:hover, input[type="submit"]:hover, input[type="button"]:hover, button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .w960 { padding: 0 15px !important; }
    .arc_title { font-size: 1.8rem !important; }
    .tbox dd { padding: 20px !important; }
    .arc_info { flex-direction: column !important; gap: 10px !important; }
}
</style>
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=4">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=4";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	<li class='hover'><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xianqinlianghan/'>先秦两汉</a> > <a href='/a/xianqinlianghan/rujia/'>儒家</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《孟子》·梁惠王上</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:03<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=4&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">孟子见梁惠王。王曰：叟不远千里而来，亦将有以利吾国乎？ 孟子对曰：王何必曰利？亦有仁义而已矣。王曰何以利吾国？大夫曰何以利吾家？士庶人曰何以利吾身？上下交征利而国危</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;">孟子见梁惠王。王曰：&ldquo;叟不远千里而来，亦将有以利吾国乎？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">孟子对曰：&ldquo;王何必曰利？亦有仁义而已矣。王曰&lsquo;何以利吾国&rsquo;？大夫曰&lsquo;何以利吾家&rsquo;？士庶人曰&lsquo;何以利吾身&rsquo;？上下交征利而国危矣。万乘之国弑其君者，必千乘之家；千乘之国弑其君者，必百乘之家。万取千焉，千取百焉，不为不多矣。苟为后义而先利，不夺不餍。未有仁而遗其亲者也，未有义而后其君者也。王亦曰仁义而已矣，何必曰利？&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">孟子见梁惠王，王立于沼上，顾鸿雁麋鹿，曰：&ldquo;贤者亦乐此乎？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">孟子对曰：&ldquo;贤者而后乐此，不贤者虽有此，不乐也。《</span><a class="popup" href="https://ctext.org/book-of-poetry/zhs" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">诗</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">》云：&lsquo;经始灵台，经之营之，庶民攻之，不日成之。经始勿亟，庶民子来。王在灵囿，麀鹿攸伏，麀鹿濯濯，白鸟鹤鹤。王在灵沼，于牣鱼跃。&rsquo;文王以民力为台为沼。而民欢乐之，谓其台曰灵台，谓其沼曰灵沼，乐其有麋鹿鱼鼈。古之人与民偕乐，故能乐也。《</span><a class="popup" href="https://ctext.org/shang-shu/speech-of-tang/zhs" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">汤誓</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">》曰：&lsquo;时日害丧？予及女偕亡。&rsquo;民欲与之偕亡，虽有台池鸟兽，岂能独乐哉？&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">梁惠王曰：&ldquo;寡人之于国也，尽心焉耳矣。河内凶，则移其民于河东，移其粟于河内。河东凶亦然。察邻国之政，无如寡人之用心者。邻国之民不加少，寡人之民不加多，何也？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">孟子对曰：&ldquo;王好战，请以战喻。填然鼓之，兵刃既接，弃甲曳兵而走。或百步而后止，或五十步而后止。以五十步笑百步，则何如？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">曰：&ldquo;不可，直不百步耳，是亦走也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">曰：&ldquo;王如知此，则无望民之多于邻国也。不违农时，谷不可胜食也；数罟不入洿池，鱼鼈不可胜食也；斧斤以时入山林，材木不可胜用也。谷与鱼鼈不可胜食，材木不可胜用，是使民养生丧死无憾也。养生丧死无憾，王道之始也。五亩之宅，树之以桑，五十者可以衣帛矣；鸡豚狗彘之畜，无失其时，七十者可以食肉矣；百亩之田，勿夺其时，数口之家可以无饥矣；谨庠序之教，申之以孝悌之义，颁白者不负戴于道路矣。七十者衣帛食肉，黎民不饥不寒，然而不王者，未之有也。<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">狗彘食人食而不知检，涂有饿莩而不知发；人死，则曰：&lsquo;非我也，岁也。&rsquo;是何异于刺人而杀之，曰：&lsquo;非我也，兵也。&rsquo;王无罪岁，斯天下之民至焉。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">梁惠王曰：&ldquo;寡人愿安承教。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">孟子对曰：&ldquo;杀人以梃与刃，有以异乎？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">曰：&ldquo;无以异也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">曰：&ldquo;庖有肥肉，廐有肥马，民有饥色，野有饿莩，此率兽而食人也。兽相食，且人恶之。为民父母，行政不免于率兽而食人。恶在其为民父母也？仲尼曰：&lsquo;始作俑者，其无后乎！&rsquo;为其象人而用之也。如之何其使斯民饥而死也？&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">梁惠王曰：&ldquo;晋国，天下莫强焉，叟之所知也。及寡人之身，东败于齐，长子死焉；西丧地于秦七百里；南辱于楚。寡人耻之，愿比死者一洒之，如之何则可？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">孟子对曰：&ldquo;地方百里而可以王。王如施仁政于民，省刑罚，薄税敛，深耕易耨。壮者以暇日修其孝悌忠信，入以事其父兄，出以事其长上，可使制梃以挞秦楚之坚甲利兵矣。彼夺其民时，使不得耕耨以养其父母，父母冻饿，兄弟妻子离散。彼陷溺其民，王往而征之，夫谁与王敌？故曰：&lsquo;仁者无敌。&rsquo;王请勿疑！&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">孟子见梁襄王。出，语人曰：&ldquo;望之不似人君，就之而不见所畏焉。卒然问曰：&lsquo;天下恶乎定？&rsquo;吾对曰：&lsquo;定于一。&rsquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">&lsquo;孰能一之？&rsquo;对曰：&lsquo;不嗜杀人者能一之。&rsquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">&lsquo;孰能与之？&rsquo;对曰：&lsquo;天下莫不与也。王知夫苗乎？七八月之间旱，则苗槁矣。天油然作云，沛然下雨，则苗浡然兴之矣。其如是，孰能御之？今夫天下之人牧，未有不嗜杀人者也，如有不嗜杀人者，则天下之民皆引领而望之矣。诚如是也，民归之，由水之就下，沛然谁能御之？&rsquo;&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">齐宣王问曰：&ldquo;齐桓、晋文之事可得闻乎？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">孟子对曰：&ldquo;仲尼之徒无道桓、文之事者，是以后世无传焉。臣未之闻也。无以，则王乎？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">曰：&ldquo;德何如，则可以王矣？&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">曰：&ldquo;保民而王，莫之能御也。&rdquo;</span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span></span><br style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;" />
<table border="0" style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 18.6667px; text-indent: 0px; text-size-adjust: auto;">
	<tbody style="padding: 0px; margin: 0px;">
		<tr id="n12893" style="padding: 0px; margin: 0px;">
			<td class="ctext" style="padding: 0px; margin: 0px; font-size: 14pt;">
				<span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; text-size-adjust: auto;">曰：&ldquo;若寡人者，可以保民乎哉？&rdquo;</span></td>
		</tr>
	</tbody>
</table>
<span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;">曰：&ldquo;可。&rdquo;</span><br style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;" />
<table border="0" style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 18.6667px; text-indent: 0px; text-size-adjust: auto;">
	<tbody style="padding: 0px; margin: 0px;">
		<tr id="n12895" style="padding: 0px; margin: 0px;">
			<td class="ctext" style="padding: 0px; margin: 0px; font-size: 14pt;">
				<span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; text-size-adjust: auto;">曰：&ldquo;何由知吾可也？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;臣闻之胡齕曰，王坐于堂上，有牵牛而过堂下者，王见之，曰：&lsquo;牛何之？&rsquo;对曰：&lsquo;将以衅钟。&rsquo;王曰：&lsquo;舍之！吾不忍其觳觫，若无罪而就死地。&rsquo;对曰：&lsquo;然则废衅钟与？&rsquo;曰：&lsquo;何可废也？以羊易之！&rsquo;不识有诸？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;有之。&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;是心足以王矣。百姓皆以王为爱也，臣固知王之不忍也。&rdquo;<br style="padding: 0px; margin: 0px;" />
				王曰：&ldquo;然。诚有百姓者。齐国虽褊小，吾何爱一牛？即不忍其觳觫，若无罪而就死地，故以羊易之也。&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;王无异于百姓之以王为爱也。以小易大，彼恶知之？王若隐其无罪而就死地，则牛羊何择焉？&rdquo;<br style="padding: 0px; margin: 0px;" />
				王笑曰：&ldquo;是诚何心哉？我非爱其财。而易之以羊也，宜乎百姓之谓我爱也。&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;无伤也，是乃仁术也，见牛未见羊也。君子之于禽兽也，见其生，不忍见其死；闻其声，不忍食其肉。是以君子远庖厨也。&rdquo;<br style="padding: 0px; margin: 0px;" />
				王说曰：&ldquo;《<a class="popup" href="https://ctext.org/book-of-poetry/zhs" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">诗</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">》云：&lsquo;</span><a class="popup" href="https://ctext.org/book-of-poetry/qiao-yan/zhs?searchu=%E4%BB%96%E4%BA%BA%E6%9C%89%E5%BF%83%EF%BC%8C%E4%BA%88%E5%BF%96%E5%BA%A6%E4%B9%8B%E3%80%82&amp;searchmode=showall#result" rel="nofollow" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">他人有心，予忖度之。</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">&rsquo;夫子之谓也。夫我乃行之，反而求之，不得吾心。夫子言之，于我心有戚戚焉。此心之所以合于王者，何也？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;有复于王者曰：&lsquo;吾力足以举百钧&rsquo;，而不足以举一羽；&lsquo;明足以察秋毫之末&rsquo;，而不见舆薪，则王许之乎？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;否。&rdquo;<br style="padding: 0px; margin: 0px;" />
				&ldquo;今恩足以及禽兽，而功不至于百姓者，独何与？然则一羽之不举，为不用力焉；舆薪之不见，为不用明焉，百姓之不见保，为不用恩焉。故王之不王，不为也，非不能也。&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;不为者与不能者之形何以异？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;挟太山以超北海，语人曰&lsquo;我不能&rsquo;，是诚不能也。为长者折枝，语人曰&lsquo;我不能&rsquo;，是不为也，非不能也。故王之不王，非挟太山以超北海之类也；王之不王，是折枝之类也。老吾老，以及人之老；幼吾幼，以及人之幼。天下可运于掌。《<a class="popup" href="https://ctext.org/book-of-poetry/zhs" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">诗</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">》云：&lsquo;</span><a class="popup" href="https://ctext.org/book-of-poetry/si-zhai/zhs?searchu=%E5%88%91%E4%BA%8E%E5%AF%A1%E5%A6%BB%EF%BC%8C%E8%87%B3%E4%BA%8E%E5%85%84%E5%BC%9F%EF%BC%8C%E4%BB%A5%E5%BE%A1%E4%BA%8E%E5%AE%B6%E9%82%A6%E3%80%82&amp;searchmode=showall#result" rel="nofollow" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">刑于寡妻，至于兄弟，以御于家邦。</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">&rsquo;言举斯心加诸彼而已。故推恩足以保四海，不推恩无以保妻子。古之人所以大过人者无他焉，善推其所为而已矣。今恩足以及禽兽，而功不至于百姓者，独何与？权，然后知轻重；度，然后知长短。物皆然，心为甚。王请度之！抑王兴甲兵，危士臣，构怨于诸侯，然后快于心与？&rdquo;<br style="padding: 0px; margin: 0px;" />
				王曰：&ldquo;否。吾何快于是？将以求吾所大欲也。&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;王之所大欲可得闻与？&rdquo;王笑而不言。<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;为肥甘不足于口与？轻暖不足于体与？抑为采色不足视于目与？声音不足听于耳与？便嬖不足使令于前与？王之诸臣皆足以供之，而王岂为是哉？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;否。吾不为是也。&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;然则王之所大欲可知已。欲辟土地，朝秦楚，莅中国而抚四夷也。以若所为求若所欲，犹缘木而求鱼也。&rdquo;<br style="padding: 0px; margin: 0px;" />
				王曰：&ldquo;若是其甚与？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;殆有甚焉。缘木求鱼，虽不得鱼，无后灾。以若所为，求若所欲，尽心力而为之，后必有灾。&rdquo;<br style="padding: 0px; margin: 0px;" />
				王曰：&ldquo;若是其甚与？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;殆有甚焉。缘木求鱼，虽不得鱼，无后灾。以若所为，求若所欲，尽心力而为之，后必有灾。&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;可得闻与？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;邹人与楚人战，则王以为孰胜？&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;楚人胜。&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;然则小固不可以敌大，寡固不可以敌众，弱固不可以敌强。海内之地方千里者九，齐集有其一。以一服八，何以异于邹敌楚哉？盖亦反其本矣。今王发政施仁，使天下仕者皆欲立于王之朝，耕者皆欲耕于王之野，商贾皆欲藏于王之市，行旅皆欲出于王之涂，天下之欲疾其君者皆欲赴诉于王。其若是，孰能御之？&rdquo;<br style="padding: 0px; margin: 0px;" />
				王曰：&ldquo;吾惛，不能进于是矣。愿夫子辅吾志，明以教我。我虽不敏，请尝试之。&rdquo;<br style="padding: 0px; margin: 0px;" />
				曰：&ldquo;无恒产而有恒心者，惟士为能。若民，则无恒产，因无恒心。苟无恒心，放辟，邪侈，无不为已。及陷于罪，然后从而刑之，是罔民也。焉有仁人在位，罔民而可为也？是故明君制民之产，必使仰足以事父母，俯足以畜妻子，乐岁终身饱，凶年免于死亡。然后驱而之善，故民之从之也轻。今也制民之产，仰不足以事父母，俯不足以畜妻子，乐岁终身苦，凶年不免于死亡。此惟救死而恐不赡，奚暇治礼义哉？王欲行之，则盍反其本矣。五亩之宅，树之以桑，五十者可以衣帛矣；鸡豚狗彘之畜，无失其时，七十者可以食肉矣；百亩之田，勿夺其时，八口之家可以无饥矣；谨庠序之教，申之以孝悌之义，颁白者不负戴于道路矣。老者衣帛食肉，黎民不饥不寒，然而不王者，未之有也。&rdquo;</span></span></span><br style="padding: 0px; margin: 0px;" />
				&nbsp;</td>
		</tr>
	</tbody>
</table>
<br style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;" />
<br style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;" />
<table border="0" style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-indent: 0px; text-size-adjust: auto;">
	<tbody style="padding: 0px; margin: 0px;">
		<tr id="n12892" style="padding: 0px; margin: 0px;">
			<td class="ctext" style="padding: 0px; margin: 0px; font-size: 14pt;">
				曰：&ldquo;保民而王，莫之能御也。&rdquo;<br />
				&nbsp;</td>
		</tr>
	</tbody>
</table>
<br />

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_4'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=4;
  	var __dedeqrcode_aid=4;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',4)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',4)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(4);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/xianqinlianghan/rujia/2021/0810/3.html'>《孟子》·梁惠王下</a> </li>
     <li>下一篇：<a href='/a/xianqinlianghan/rujia/2021/0810/5.html'>《论语》·先进</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=4" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=4&title=《孟子》·梁惠王上" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=4" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="4" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=4">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=4&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '4');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      <li><a href='/a/xianqinlianghan/rujia/' class='thisclass'>儒家</a></li>
      <li><a href='/a/xianqinlianghan/mojia/'>墨家</a></li>
      
      <li><a href='/a/xianqinlianghan/daojia/'>道家</a></li>
      
      <li><a href='/a/xianqinlianghan/fajia/'>法家</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/xianqinlianghan/rujia/2021/0810/6.html">《论语》·里仁</a>
       <p>子曰：里仁为美。择不处仁，焉得知？ 孔子说：同品德高尚的人...</p>
      </li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/4.html">《孟子》·梁惠王上</a>
       <p>孟子见梁惠王。王曰：叟不远千里而来，亦将有以利吾国乎？...</p>
      </li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/3.html">《孟子》·梁惠王下</a>
       <p>庄暴见孟子，曰：暴见于王，王语暴以好乐，暴未有以对也。曰...</p>
      </li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/2.html">《论语》·学而</a>
       <p>子曰：学而时习之，不亦说乎？有朋自远方来，不亦乐乎？人不...</p>
      </li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/1.html">《论语》·为政</a>
       <p>子曰：为政以德，譬如北辰，居其所而众星共之。 孔子说：管理...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xianqinlianghan/rujia/2021/0810/3.html">《孟子》·梁惠王下</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/6.html">《论语》·里仁</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/4.html">《孟子》·梁惠王上</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/2.html">《论语》·学而</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/1.html">《论语》·为政</a></li>
<li><a href="/a/xianqinlianghan/rujia/2021/0810/5.html">《论语》·先进</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
