<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《墨子》·卷三·尚同上_万婕源码网</title>
<meta name="keywords" content="《,墨子,》,卷三,尚,同上,子,墨子,言曰,古者," />
<meta name="description" content="子墨子言曰：古者民始生，未有刑政之时，盖其语人异义。是以一人则一义，二人则二义，十人则十义，其人兹众，其所谓义者亦兹众。是以人是其义，以非人之义，故文相非也。是以" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<style type="text/css">
/* 小仙元码暗色主题样式 - 文章详情页 */
* { box-sizing: border-box; }
html { background: #0a0a0a; }
body {
    font: 14px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: #fff !important;
    line-height: 1.6;
}
a { color: #00d4ff !important; text-decoration: none; transition: all 0.3s ease; }
a:hover { color: #0099cc !important; }

/* 面包屑导航 */
.position {
    background: #1a1a1a !important;
    padding: 15px 20px !important;
    border-bottom: 1px solid #333 !important;
    margin-bottom: 20px !important;
}
.position a { color: #00d4ff !important; }

/* 文章容器 */
.w960 { background: transparent !important; }
.tbox {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    margin-bottom: 30px !important;
}
.tbox dt {
    background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%) !important;
    border: none !important;
    height: 50px !important;
    border-radius: 15px 15px 0 0 !important;
}
.tbox dt strong {
    color: #00d4ff !important;
    line-height: 50px !important;
    font-size: 1.3rem !important;
    padding-left: 20px !important;
}
.tbox dd {
    background: #1a1a1a !important;
    border: none !important;
    padding: 30px !important;
    border-radius: 0 0 15px 15px !important;
}

/* 文章标题 */
.arc_title {
    color: #fff !important;
    font-size: 2.5rem !important;
    margin-bottom: 20px !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 15px !important;
}

/* 文章信息 */
.arc_info {
    color: #ccc !important;
    margin-bottom: 20px !important;
    padding: 15px 0 !important;
    border-bottom: 1px solid #333 !important;
}
.arc_info span { margin-right: 20px !important; }

/* 文章内容 */
.arc_content, .arc_body {
    color: #ccc !important;
    line-height: 1.8 !important;
    margin: 30px 0 !important;
}
.arc_content h1, .arc_content h2, .arc_content h3, .arc_content h4, .arc_content h5, .arc_content h6,
.arc_body h1, .arc_body h2, .arc_body h3, .arc_body h4, .arc_body h5, .arc_body h6 {
    color: #fff !important;
    margin: 30px 0 20px 0 !important;
}
.arc_content h2, .arc_body h2 { color: #00d4ff !important; }
.arc_content p, .arc_body p { margin-bottom: 20px !important; }
.arc_content img, .arc_body img {
    max-width: 100% !important;
    border-radius: 8px !important;
    margin: 20px 0 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* 文章缩略图 */
.arc_pic {
    text-align: center !important;
    margin: 20px 0 !important;
}
.arc_pic img {
    max-width: 100% !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

/* 标签 */
.arc_tags { margin-top: 30px !important; }
.arc_tags a, .tags a {
    background: #333 !important;
    color: #00d4ff !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    text-decoration: none !important;
    display: inline-block !important;
    margin: 5px 10px 5px 0 !important;
    transition: all 0.3s ease !important;
}
.arc_tags a:hover, .tags a:hover {
    background: #00d4ff !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
}

/* 侧边栏 */
.sidebar { background: transparent !important; }
.sidebar .tbox { margin-bottom: 25px !important; }
.sidebar ul li {
    background: none !important;
    border-bottom: 1px solid #333 !important;
    padding: 8px 0 !important;
}
.sidebar ul li a { color: #ccc !important; }
.sidebar ul li a:hover { color: #00d4ff !important; }

/* 评论区域 */
.feedback {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    padding: 30px !important;
    margin-top: 30px !important;
}
.feedback h3 {
    color: #00d4ff !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 10px !important;
    margin-bottom: 20px !important;
}

/* 评论表单 */
.feedback textarea {
    background: #333 !important;
    border: 1px solid #555 !important;
    color: #fff !important;
    border-radius: 8px !important;
    padding: 15px !important;
    width: 100% !important;
    resize: vertical !important;
}
.feedback textarea:focus {
    border-color: #00d4ff !important;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2) !important;
    outline: none !important;
}

/* 按钮样式 */
.btn, input[type="submit"], input[type="button"], button {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
    border: none !important;
    color: #fff !important;
    border-radius: 25px !important;
    padding: 12px 24px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: bold !important;
}
.btn:hover, input[type="submit"]:hover, input[type="button"]:hover, button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .w960 { padding: 0 15px !important; }
    .arc_title { font-size: 1.8rem !important; }
    .tbox dd { padding: 20px !important; }
    .arc_info { flex-direction: column !important; gap: 10px !important; }
}
</style>
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=11">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=11";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	<li class='hover'><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xianqinlianghan/'>先秦两汉</a> > <a href='/a/xianqinlianghan/mojia/'>墨家</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《墨子》·卷三·尚同上</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:05<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=11&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">子墨子言曰：古者民始生，未有刑政之时，盖其语人异义。是以一人则一义，二人则二义，十人则十义，其人兹众，其所谓义者亦兹众。是以人是其义，以非人之义，故文相非也。是以</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <table style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;" width="100%">
	<tbody style="padding: 0px; margin: 0px;">
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0px; margin: 0px;">
				<span style="font-size:16px;"><span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; text-size-adjust: auto;">子墨子言曰：&ldquo;古者民始生，未有刑政之时，盖其语&lsquo;人异义&rsquo;。是以一人则一义，二人则二义，十人则十义，其人兹众，其所谓义者亦兹众。是以人是其义，以非人之义，故文相非也。是以内者父子兄弟作怨恶，离散不能相和合。天下之百姓，皆以水火毒药相亏害，至有馀力不能以相劳，腐臭</span><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; text-size-adjust: auto;">1</sup><span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; text-size-adjust: auto;">馀财不以相分，隐匿良道不以相教，天下之乱，若禽兽然。<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">墨子说：古时人类刚刚诞生，还没有刑法政治的时候，人们用言语表达的意见，也因人而异。所以一人就有一种意见，两人就有两种意见，十人就有十种意见。人越多，他们不同的意见也就越多。每个人都以为自己的意见对而别人的意见错，因而相互攻击。所以在家庭内父子兄弟常因意见不同而相互怨恨，使得家人离散而不能和睦相处。天下的百姓，都用水火毒药相互残害，以致有余力的人不能帮助别人；有余财者宁愿让它腐烂，也不分给别人；有好的道理也自己隐藏起来，不肯教给别人，以致天下混乱，有如禽兽一般。<br style="padding: 0px; margin: 0px;" />
				<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">夫明宓天下之所以乱者，生于无政长。是故选天下之贤可者，立以为天子。天子立，以其力为未足，又选择天下之贤可者，置立之以为三公。天子三公既以立，以天下为博大，远国异土之民，是非利害之辩，不可一二而明知，故画分万国，立诸侯国君，诸侯国君既已立，以其力为未足，又选择其国之贤可者，置立之以为正长。<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">明白了天下所以大乱的原因，是由于没有行政长官，所以（人们）就选择贤能的人，立之为天子。立了天子之后，认为他的力量还不够，因而又选择天下贤能的人，把他们立为三公。天子、三公已立，又认为天下地域广大，他们对于远方异邦的人民以及是非利害的辨别，还不能一一了解，所以又把天下划为万国，然后设立诸侯国君。诸侯国君已立，又认为他们的力量还不够，又在他们国内选择一些贤能的人，把他们立为行政长官。<br style="padding: 0px; margin: 0px;" />
				<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">正长既已具，天子发政于天下之百姓，言曰：&lsquo;闻善而不善，皆以告其上。上之所是，必皆是之，所非必皆非之，上有过则规谏之，下有善则傍荐之。上同而不下比者，此上之所赏，而下之所誉也。意若闻善而不善，不以告其上，上之所是，弗能是，上之所非，弗能非，上有过弗规谏，下有善弗傍荐，下比不能上同者，此上之所罚，而百姓所毁也。&rsquo;上以此为赏罚，甚明察以审信。<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">行政长官已经设立之后，天子就向天下的百姓发布政令，说道：&ldquo;你们听到善和不善，都要报告给上面。上面认为是对的，大家都必须认为对；上面认为是错的，大家都必须认为错。上面有过失，就应该规谏，下面有好人好事，就应当广泛地推荐给国君。是非与上面一致，而不与下面勾结，这是上面所赞赏，下面所称誉的。假如听到善与不善，却不向上面报告；上面认为对的，也不认为对，上面认为错的，也不认为错；上面有过失不能规谏，下面有好人好事不能广泛地向上面推荐；与下面勾结而不与上面一致，这是上面所要惩罚，也是百姓所要非议的。&rdquo;上面根据这些方面来行使赏罚，就必然十分审慎、可靠。<br style="padding: 0px; margin: 0px;" />
				<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">是故里长者，里之仁人也。里长发政里之百姓，言曰：&lsquo;闻善而不善，必以告其乡长。乡长之所是，必皆是之，乡长之所非，必皆非之。去若不善言，学乡长之善言；去若不善行，学乡长之善行，则乡何说以乱哉？&rsquo;察乡之所治者何也？乡长唯能壹同乡之义，是以乡治也。<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">所以里长就是这一里内的仁人。里长发布政令于里中的百姓，说道：&ldquo;听到善和不善，必须报告给乡长。乡长认为对的，大家都必须认为对；乡长认为错的，大家都必须认为错。去掉你们不好的话，学习乡长的好话；去掉你们不好的行为，学习乡长的好行为。&rdquo;那么，乡里怎么会说混乱呢？我们考察这一乡得到治理的原因是什么呢？是由于乡长能够统一全乡的意见，所以乡内就治理好了。&rdquo;<br style="padding: 0px; margin: 0px;" />
				<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">乡长者，乡之仁人也。乡长发政乡之百姓，言曰：&lsquo;闻善而不善者，必以告国君。国君之所是，必皆是之，国君之所非，必皆非之。去若不善言，学国君之善言，去若不善行，学国君之善行，则国何说以乱哉。&rsquo;察国之所以治者何也？国君唯能壹同国之义，是以国治也。<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">乡长是这一乡的仁人。乡长发布政令于乡中百姓，说道：&ldquo;听到善和不善，必须把它报告给国君。国君认为是对的，大家都必须认为对；国君认为是错的，大家都必须认为错。去掉你们不好的话，学习国君的好话；去掉你们不好的行为，学习国君的好行为。&rdquo;那么，还怎么能说国内会混乱呢？我们考察一国得到治理的原因是什么呢？是因为国君能统一国中的意见。所以国内就治理好了。<br style="padding: 0px; margin: 0px;" />
				<br style="padding: 0px; margin: 0px;" />
				国君者，国之仁人也。国君发政国之百姓，言曰：&lsquo;闻善而不善。必以告天子。天子之所是，皆是之，天子之所非，皆非之。去若不善言，学天子之善言；去若不善行，学天子之善行，则天下何说以乱哉。&rsquo;察天下之所以治者何也？天子唯能壹同天下之义，是以天下</span></span></span></span></span></span></span></span></span></span></span><span style="font-size:16px;"><span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; text-size-adjust: auto;"><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">治也。<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">国君是这一国的仁人。国君发布政令于国中百姓，说道：&ldquo;听到善和不善，必须报告给天子。天子认为是对的，大家都必须认为对；天子认为是错的，大家都必须认为错。去掉你们不好的话，学习天子的好话，去掉你们不好的行为，学习天子的好行为。&rdquo;那么，还怎么能说天下会乱呢？我们考察天下治理得好的原因是什么呢？是因为天子能够统一天下的意见，所以天下就治理好了。</span><br style="padding: 0px; margin: 0px;" />
				<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">天下之百姓皆上同于天子，而不上同于天，则灾犹未去也。今若天飘风苦雨，溱溱而至者，此天之所以罚百姓之不上同于天者也。&rdquo;</span><br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">天下的老百姓都知道与天子一致，而不知道与天一致，那么灾祸还不能彻底除去。现在假如天刮大风下久雨，频频而至，这就是上天对那些不与上天一致的百姓的惩罚。</span><br style="padding: 0px; margin: 0px;" />
				<br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">是故子墨子言曰：&ldquo;古者圣王为五刑，请以治其民。譬若丝缕之有纪，罔罟之有纲，所连收天下之百姓不尚同其上者也。&rdquo;</span><br style="padding: 0px; margin: 0px;" />
				<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">所以墨子说：&ldquo;古时圣王制定五种刑法，确实用它来治理人民，就好比丝线有纪（丝头的总束）、网罟有纲一样，是用来收紧那些不与上面意见一致的老百姓的。&rdquo;</span></span></span></span></td>
		</tr>
	</tbody>
</table>
<br />

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_11'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=11;
  	var __dedeqrcode_aid=11;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',11)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',11)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(11);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/xianqinlianghan/mojia/2021/0810/10.html'>《墨子》·卷一·修身</a> </li>
     <li>下一篇：没有了 </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=11" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=11&title=《墨子》·卷三·尚同上" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=11" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="11" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=11">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=11&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '11');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/xianqinlianghan/rujia/'>儒家</a></li>
      <li><a href='/a/xianqinlianghan/mojia/' class='thisclass'>墨家</a></li>
      <li><a href='/a/xianqinlianghan/daojia/'>道家</a></li>
      
      <li><a href='/a/xianqinlianghan/fajia/'>法家</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/xianqinlianghan/mojia/2021/0810/11.html">《墨子》·卷三·尚同上</a>
       <p>子墨子言曰：古者民始生，未有刑政之时，盖其语人异义。是以...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/10.html">《墨子》·卷一·修身</a>
       <p>君子战虽有陈，而勇为本焉。丧虽有礼，而哀为本焉。士虽有学...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/9.html">《墨子》·卷三·尚同中</a>
       <p>子墨子曰：方今之时，复古之民始生，未有正长之时，盖其语曰...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/8.html">《墨子》·卷一·亲士</a>
       <p>入国而不存其士，则亡国矣。见贤而不急，则缓其君矣。非贤无...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/7.html">《墨子》·卷一·所染</a>
       <p>子墨子言见染丝者而叹曰：染于苍则苍，染于黄则黄。所入者变...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xianqinlianghan/mojia/2021/0810/11.html">《墨子》·卷三·尚同上</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/10.html">《墨子》·卷一·修身</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/9.html">《墨子》·卷三·尚同中</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/8.html">《墨子》·卷一·亲士</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/7.html">《墨子》·卷一·所染</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
