<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《墨子》·卷一·亲士_万婕源码网</title>
<meta name="keywords" content="《,墨子,》,卷一,亲士,入国,而,不存,其士,则," />
<meta name="description" content="入国而不存其士，则亡国矣。见贤而不急，则缓其君矣。非贤无急，非士无与虑国，缓贤忘士而能以其国存者，未曾有也。 治国而不优待贤士，国家就会灭亡。见到贤士而不急于任用，" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=8">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=8";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	<li class='hover'><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xianqinlianghan/'>先秦两汉</a> > <a href='/a/xianqinlianghan/mojia/'>墨家</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《墨子》·卷一·亲士</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:04<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=8&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">入国而不存其士，则亡国矣。见贤而不急，则缓其君矣。非贤无急，非士无与虑国，缓贤忘士而能以其国存者，未曾有也。 治国而不优待贤士，国家就会灭亡。见到贤士而不急于任用，</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;">入国而不存其士，则亡国矣。见贤而不急，则缓其君矣。非贤无急，非士无与虑国，缓贤忘士而能以其国存者，未曾有也。<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">治国而不优待贤士，国家就会灭亡。见到贤士而不急于任用，他们就会怠慢君主。没有比用贤更急迫的了，若没有贤士，就没有人和自己谋划国事。怠慢遗弃贤士而能使国家长治久安的，还不曾有过。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">昔者文公出走而正天下，桓公去国而霸诸侯，越王句践遇吴王之丑，而尚摄中国之贤君。三子之能达名成功于天下也，皆于其国抑而大丑也。太上无败，其次败而有以成，此之谓用民。<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">从前，晋文公被迫逃亡在外，后为天下盟主；齐桓公被迫离开国家，后来称霸诸侯；越王勾践被吴王战败受辱，终成威慑中原诸国的贤君。这三君所以能成功扬名于天下，是因为他们都能忍辱负耻，以图复仇。最上的是不遭失败，其次是失败而有办法成功，这才叫善于使用士民。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">吾闻之曰：&ldquo;非无安居也，我无安心也。非无足财也，我无足心也。&rdquo;是故君子自难而易彼，众人自易而难彼，君子进不败其志，内究其情，虽杂庸民，终无怨心，彼有自信者也。是故为其所难者，必得其所欲焉，未闻为其所欲，而免其所恶者也。是故逼臣伤君，谄下伤上。君必有弗弗之臣，上必有詻詻之下。分议者延延，而支苟者詻詻，焉可以长生保国。<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">我曾听说：&ldquo;我不是没有安定的住处，而是自己没有安定之心；不是没有丰足的财产，而是怀著无法满足的心。&rdquo;所以君子严以律己，宽以待人。而一般人则宽以律己，严以待人。君子仕进顺利时不改变他的素志，不得志时心情也一样；即使杂处于庸众之中，也终究没有怨尤之心。他们是有著自信的人。所以说，凡事能从难处做起，就一定能达到自己的愿望，但却没有听说只做自己所想的事情，而能免于所厌恶之后果的。所以幸臣与谗佞之辈往往伤害君主。君主必须有敢于矫正君主过失的臣僚，上面必须有直言极谏的下属，分辩议事的人争论锋起，互相责难的人互不退让，这才可以长养民生，保卫国土。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">臣下重其爵位而不言，近臣则喑，远臣则唫，怨结于民心，谄谀在侧，善议障塞，则国危矣。桀纣不以其无天下之士邪？杀其身而丧天下。故曰：&ldquo;归国宝，不若献贤而进士。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">如果臣下只以爵禄为重，不对国事发表意见，近臣缄默不言，远臣闭口暗叹，怨恨就郁结于民心了。谄谀阿奉之人围在身边，好的建议被他们阻障难进，那国家就危险了。桀、纣不正是因为他们不重视天下之士吗？结果身被杀而失天下。所以说：赠送国宝，不如推荐贤士。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">今有五锥，此其銛，銛者必先挫。有五刀，此其错，错者必先靡，是以甘井近竭，招木近伐，灵龟近灼，神蛇近暴。是故比干</span><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px; text-size-adjust: auto;">1</sup><span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">之殪，其抗也；孟贲之杀，其勇也；西施之沈，其美也；吴起之裂，其事也。故彼人者，寡不死其所长，故曰：&ldquo;太盛难守也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">比如现在有五把锥子，一把最锋利，那么这一把必先折断。有五把刀，一把磨得最快，那么这一把必先损坏。所以甜的水井最易用干，高的树木最易被伐，灵验的宝龟最先被火灼占卦，神异的蛇最先被曝晒求雨。所以，比干之死，是因为他抗直；孟贲被杀，是因为他逞勇；西施被沉江，是因为长得美丽；吴起被车裂，是因为他有大功。这些人很少不是死于他们的所长。所以说：太盛了就难以持久。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">故虽有贤君，不爱无功之臣；虽有慈父，不爱无益之子。是故不胜其任而处其位，非此位之人也；不胜其爵而处其禄，非此禄之主也。良弓难张，然可以及高入深；良马难乘，然可以任重致远；良才难令，然可以致君见尊。是故江河不恶小谷之满己也，故能大。圣人者，事无辞也，物无违也，故能为天下器。是故江河之水，非一源</span></span></span></span></span></span></span></span></span></span><span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">&nbsp;</span>
<div style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px; display: inline-block; zoom: 1;">
	<div class="sprite-unexpand" style="padding: 0px; margin: 0px; overflow: hidden; width: 11px; height: 11px; position: relative; text-indent: -100em; float: left;" title="+">
		<div style="padding: 0px; margin: 0px; background-image: url(&quot;https://ctext.org/static/sprite.png&quot;); height: 11px; left: 0px; position: absolute; top: 0px; width: 11px; background-position: 0px -476px; background-repeat: no-repeat;">
			&nbsp;</div>
	</div>
</div>
<span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">&nbsp;</span><span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;"><span class="container" style="padding: 0px; margin: 0px; text-size-adjust: auto;"><span class="subcontents" style="padding: 0px; margin: 0px; display: inline;">之水</span></span><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px; text-size-adjust: auto;">1</sup><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">也。千镒之裘，非一狐之白也。夫恶有同方取不取同而已者乎？盖非兼王之道也。是故天地不昭昭，大水不潦潦，大火不燎燎，王德不尧尧者，乃千人之长也。</span><br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">因此，即使有贤君，他也不爱无功之臣；即使有慈父，他也不爱无益之子。所以，凡是不能胜任其事而占据这一位置的，他就不应居于此位；凡是不胜任其爵而享受这一俸禄的，他就不当享有此禄。良弓不容易张开，但可以射得高没得深；良马不容易乘坐，但可以载得重行得远；好的人才不容易驾驭，但可以使国君受人尊重。所以，长江黄河不嫌小溪灌注它里面，才能让水量增大。圣人勇于任事，又能接受他人的意见，所以能成为治理天下的英才。所以长江黄河里的水，不是从同一水源流下的；价值千金的狐白裘，不是从一只狐狸腋下集成的。哪里有与自己相同的意见才采纳，与自己不同的意见就不采纳的道理呢？这不是统一天下之道。所以大地不昭昭为明（而美丑皆收），大水不潦潦为大（而川泽皆纳），大火不燎燎为盛（而草木皆容），王德不尧尧为高（而贵贱皆亲），才能做千万人的首领。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">其直如矢，其平如砥，不足以覆万物，是故溪陕者速涸，逝浅者速竭，墝埆者其地不育。王者淳泽不出宫中，则不能流国矣。</span><br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">象箭一样直，象磨刀石一样平，那就不能覆盖万物了。所以狭隘的溪流干得快，平浅的川泽枯得早，坚薄的土地不长五谷。做王的人深恩厚泽不出宫中，就不能流遍全国。</span></span>
      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_8'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=8;
  	var __dedeqrcode_aid=8;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',8)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',8)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(8);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/xianqinlianghan/mojia/2021/0810/7.html'>《墨子》·卷一·所染</a> </li>
     <li>下一篇：<a href='/a/xianqinlianghan/mojia/2021/0810/9.html'>《墨子》·卷三·尚同中</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=8" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=8&title=《墨子》·卷一·亲士" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=8" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="8" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=8">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=8&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '8');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/xianqinlianghan/rujia/'>儒家</a></li>
      <li><a href='/a/xianqinlianghan/mojia/' class='thisclass'>墨家</a></li>
      <li><a href='/a/xianqinlianghan/daojia/'>道家</a></li>
      
      <li><a href='/a/xianqinlianghan/fajia/'>法家</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/xianqinlianghan/mojia/2021/0810/11.html">《墨子》·卷三·尚同上</a>
       <p>子墨子言曰：古者民始生，未有刑政之时，盖其语人异义。是以...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/10.html">《墨子》·卷一·修身</a>
       <p>君子战虽有陈，而勇为本焉。丧虽有礼，而哀为本焉。士虽有学...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/9.html">《墨子》·卷三·尚同中</a>
       <p>子墨子曰：方今之时，复古之民始生，未有正长之时，盖其语曰...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/8.html">《墨子》·卷一·亲士</a>
       <p>入国而不存其士，则亡国矣。见贤而不急，则缓其君矣。非贤无...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/7.html">《墨子》·卷一·所染</a>
       <p>子墨子言见染丝者而叹曰：染于苍则苍，染于黄则黄。所入者变...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xianqinlianghan/mojia/2021/0810/11.html">《墨子》·卷三·尚同上</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/10.html">《墨子》·卷一·修身</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/9.html">《墨子》·卷三·尚同中</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/8.html">《墨子》·卷一·亲士</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/7.html">《墨子》·卷一·所染</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
