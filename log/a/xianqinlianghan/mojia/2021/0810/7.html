<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《墨子》·卷一·所染_万婕源码网</title>
<meta name="keywords" content="《,墨子,》,卷一,所染,子,墨子,言见,染丝,者," />
<meta name="description" content="子墨子言见染丝者而叹曰：染于苍则苍，染于黄则黄。所入者变，其色亦变。五入必而已，则为五色矣。故染不可不慎也。 墨子说，他曾见人染丝而感叹说： （丝）染了青颜料就变成" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=7">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=7";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	<li class='hover'><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xianqinlianghan/'>先秦两汉</a> > <a href='/a/xianqinlianghan/mojia/'>墨家</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《墨子》·卷一·所染</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:04<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=7&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">子墨子言见染丝者而叹曰：染于苍则苍，染于黄则黄。所入者变，其色亦变。五入必而已，则为五色矣。故染不可不慎也。 墨子说，他曾见人染丝而感叹说： （丝）染了青颜料就变成</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;">子墨子言见染丝者而叹曰：&ldquo;染于苍则苍，染于黄则黄。所入者变，其色亦变。五入必而已，则为五色矣。故染不可不慎也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">墨子说，他曾见人染丝而感叹说：&ldquo; （丝）染了青颜料就变成青色，染了黄颜料就变成黄色。染料不同，丝的颜色也跟著变化。经过五次之后，就变为五种颜色了。所以染这件事是不可不谨慎的。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">非独染丝然也，国亦有染。舜染于许由</span><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px; text-size-adjust: auto;">1</sup><span style="padding: 0px; margin: 0px; font-size: 18.6667px; text-size-adjust: auto;">、伯阳，禹染于皋陶、伯益，汤染于伊尹、仲虺，武王染于太公、周公。此四王者所染当，故王天下，立为天子，功名蔽天地。举天下之仁义显人，必称此四王者。<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">不仅染丝如此，国家也有&ldquo;染&rdquo;。舜被许由、伯阳所染，禹被皋陶、伯益所染，汤被伊尹、仲虺所染，武王被太公、周公所染。这四位君王因为所染得当，所以能称王于天下，立为天子，功盖四方，名扬天下，凡是提起天下著名的仁义之人，必定要称这四王。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">1. 由 : 原作&ldquo;山&rdquo;。据：《</span><a class="popup" href="https://ctext.org/library.pl?if=gb&amp;file=77777&amp;page=61&amp;remap=gb" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">吕氏春秋</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">》、《</span><a class="popup" href="https://ctext.org/library.pl?if=gb&amp;file=77810&amp;page=100&amp;remap=gb" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">群书治要</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">》。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">夏桀染于干辛、推哆，殷纣染于崇侯、恶来，厉王染于厉公长父、荣夷终，幽王染于傅公夷、蔡公谷。此四王者所染不当，故国残身死，为天下僇。举天下不义辱人，必称此四王者。</span><br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">夏桀被干辛、推哆所染，殷纣被崇侯、恶来所染，周厉王被厉公长父、荣夷终所染，周幽王被傅公夷、蔡公谷所染。这四位君王因为所染不当，结果身死国亡，遗羞于天下。凡是提起天下不义可耻之人，必定要称这四王。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">齐桓染于管仲、鲍叔，晋文染于舅犯、高偃，楚庄染于孙叔、沈尹，吴阖闾染于伍员、文义，越句践染于范蠡大夫种。此五君</span></span></span></span><span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">&nbsp;</span>
<div style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px; display: inline-block; zoom: 1;">
	<div class="sprite-unexpand" style="padding: 0px; margin: 0px; overflow: hidden; width: 11px; height: 11px; position: relative; text-indent: -100em; float: left;" title="+">
		<div style="padding: 0px; margin: 0px; background-image: url(&quot;https://ctext.org/static/sprite.png&quot;); height: 11px; left: 0px; position: absolute; top: 0px; width: 11px; background-position: 0px -476px; background-repeat: no-repeat;">
			&nbsp;</div>
	</div>
</div>
<span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">&nbsp;</span><span style="padding: 0px; margin: 0px; caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); font-family: Verdana, Arial, Helvetica, sans-serif, times, &quot;Heiti TC&quot;, PMingLiU, PMingLiu-ExtB, SimSun, SimSun-ExtB, HanaMinA, HanaMinB; font-size: 18.6667px; text-size-adjust: auto;"><span class="container" style="padding: 0px; margin: 0px; text-size-adjust: auto;"><span class="subcontents" style="padding: 0px; margin: 0px; display: inline;">者</span></span><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px; text-size-adjust: auto;">1</sup><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">所染当，故霸诸侯，功名傅于后世。</span><br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">齐桓公被管仲、鲍叔牙所染，晋文公被舅犯、高偃所染，楚庄王被孙叔敖、沈尹茎所染，吴王阖闾被伍员、文义所染，越王句践被范蠡、文种所染。这五位君主因为所染得当，所以能称霸诸侯，功名传于后世。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">1. 者 : 旧脱。 据：《</span><a class="popup" href="https://ctext.org/library.pl?if=gb&amp;file=77777&amp;page=63&amp;remap=gb" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">吕氏春秋</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">》、《</span><a class="popup" href="https://ctext.org/library.pl?if=gb&amp;file=77810&amp;page=101&amp;remap=gb" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">群书治要</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">》。 孙诒让《墨子闲诂》</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">范吉射染于长柳朔、王胜，中行寅染于籍秦、高强，吴夫差染于王孙雒、太宰嚭，知伯摇染于智国、张武，中山尚染于魏义、偃长，宋康染于唐鞅、佃</span><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px; text-size-adjust: auto;">1</sup><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">不礼。此六君者所染不当，故国家残亡，身为刑戮，宗庙破灭，绝无后类，君臣离散，民人流亡。举天下之贪暴苛扰者，必称此六君也。</span><br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">范吉射被长柳朔、王胜所染，中行寅被籍秦、高强所染，吴王夫差被王孙雒、太宰嚭所染，知伯摇被知国、张武所染，中山尚被魏义、偃长所染，宋康王被唐鞅、佃不礼所染。这六位君主因为所染不当，所以国破家亡，身受刑戮，宗庙毁灭，子孙灭绝，君臣离散，百姓逃亡。凡是提起天下贪暴苛刻的人，必定称这六君。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">1. 佃 : 原作&ldquo;伷&rdquo;。自孙诒让《墨子闲诂》改。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">凡君之所以安者，何也？以其行理也，行理性于染当。故善为君者，劳于论人，而佚于治官。不能为君者，伤形费神，愁心劳意，然国逾危，身逾辱。此六君者，非不重其国，爱其身也，以不知要故也。不知要者，所染不当也。</span><br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">大凡人君之所以能够安定，是什么原因呢？是因为他们行事合理。而行事合理源于所染得当。所以善于做国君的，用心致力于选拔人才。不善于做国君的，劳神伤身，用尽心思，然而国家更危险，自己更受屈辱。上述这六位国君，并非不重视他们的国家、爱惜他们的身体，而是因为他们不知道治国要领的缘故。所谓不知道治国要领，即是所染不得当。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">非独国有染也，士亦有染。其友皆好仁义，淳谨畏令，则家日益，身日安，名日荣，处官得其理矣，则段干木、禽子、傅说之徒是也。其友皆好矜奋，创作比周，则家日损，身日危，名日辱，处官失其理矣，则子西、易牙、竖刀</span><sup class="refindex" style="padding: 0px; margin: 0px; font-size: 13.0667px; text-size-adjust: auto;">1</sup><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">之徒是也。《</span><a class="popup" href="https://ctext.org/book-of-poetry/zhs" style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); text-decoration-line: none; text-size-adjust: auto;">诗</a><span style="padding: 0px; margin: 0px; text-size-adjust: auto;">》曰：&ldquo;必择所堪。&rdquo;必谨所堪者，此之谓也。</span><br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; font-size: 16px; text-size-adjust: auto;">不仅国家有染，士也有&ldquo;染&rdquo;。一个人所交的朋友都爱好仁义，都淳朴谨慎，慑于法纪，那么他的家道就日益兴盛，身体日益平安，名声日益光耀，居官治政也合于正道了，如段干木、禽子、傅说等人即属此类（朋友）。一个人所交的朋友若都不安分守己，结党营私，那么他的家道就日益衰落，身体日益危险，名声日益降低，居官治政也不得其道，如子西、易牙、竖刀等人即属此类（朋友）。《诗》上说：&ldquo;选好染料。&rdquo;所谓选好染料，正是这个意思。</span><br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
<span style="padding: 0px; margin: 0px; text-size-adjust: auto;">1. 刀 : 原作&ldquo;刁&rdquo;。自孙诒让《墨子闲诂》改。</span></span><br style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;" />
<div style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">
	&nbsp;</div>

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_7'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=7;
  	var __dedeqrcode_aid=7;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',7)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',7)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(7);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：没有了 </li>
     <li>下一篇：<a href='/a/xianqinlianghan/mojia/2021/0810/8.html'>《墨子》·卷一·亲士</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=7" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=7&title=《墨子》·卷一·所染" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=7" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="7" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=7">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=7&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '7');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/xianqinlianghan/rujia/'>儒家</a></li>
      <li><a href='/a/xianqinlianghan/mojia/' class='thisclass'>墨家</a></li>
      <li><a href='/a/xianqinlianghan/daojia/'>道家</a></li>
      
      <li><a href='/a/xianqinlianghan/fajia/'>法家</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/xianqinlianghan/mojia/2021/0810/11.html">《墨子》·卷三·尚同上</a>
       <p>子墨子言曰：古者民始生，未有刑政之时，盖其语人异义。是以...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/10.html">《墨子》·卷一·修身</a>
       <p>君子战虽有陈，而勇为本焉。丧虽有礼，而哀为本焉。士虽有学...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/9.html">《墨子》·卷三·尚同中</a>
       <p>子墨子曰：方今之时，复古之民始生，未有正长之时，盖其语曰...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/8.html">《墨子》·卷一·亲士</a>
       <p>入国而不存其士，则亡国矣。见贤而不急，则缓其君矣。非贤无...</p>
      </li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/7.html">《墨子》·卷一·所染</a>
       <p>子墨子言见染丝者而叹曰：染于苍则苍，染于黄则黄。所入者变...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xianqinlianghan/mojia/2021/0810/11.html">《墨子》·卷三·尚同上</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/10.html">《墨子》·卷一·修身</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/9.html">《墨子》·卷三·尚同中</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/8.html">《墨子》·卷一·亲士</a></li>
<li><a href="/a/xianqinlianghan/mojia/2021/0810/7.html">《墨子》·卷一·所染</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
