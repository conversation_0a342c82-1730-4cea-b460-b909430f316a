无法在这个位置找到： head_new.htm

    <!-- 面包屑导航 -->
    <section class="breadcrumb-section">
        <div class="container">
            <div class="breadcrumb">
                <a href="http://103.67.52.227:97">首页</a>
                <span class="separator">/</span>
                <a href='http://103.67.52.227:97/'>主页</a> > <a href='/a/xianqinlianghan/'>先秦两汉</a> > <a href='/a/xianqinlianghan/fajia/'>法家</a>
            </div>
        </div>
    </section>

    <!-- 分类页面主要内容 -->
    <main class="main-content">
        <div class="container">
            <div class="content-wrapper">
                <!-- 左侧内容 -->
                <div class="main-column">
                    <!-- 分类标题和描述 -->
                    <section class="category-header">
                        <div class="category-info">
                            <h1>法家</h1>
                            <p class="category-desc"></p>
                            <div class="category-stats">
                                <span class="stat-item">
                                    <i class="fas fa-file-alt"></i>
                                    共 <div class='col1'>
    <div class='mainlinklist'>·<a href='/a/xianqinlianghan/fajia/2021/0810/20.html'><b>《韩非子》·存韩</b></a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 韩事秦三十馀年，出则为捍蔽，入则为席荐，秦特出锐师取韩地，而随之怨悬于天下，功归于强秦。且夫韩入贡职，与郡县无异也。今臣窃闻贵臣之计，举兵将伐韩。夫赵氏聚士卒，养...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:10:20 点击：113 好评度：0
      <a href="/plus/feedback.php?aid=20"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div><div class='col1'>
    <div class='mainlinklist'>·<a href='/a/xianqinlianghan/fajia/2021/0810/19.html'><b>《韩非子》·难言</b></a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 臣非非难言也，所以难言者：言顺比滑泽，洋洋纚纚然，则见以为华而不实。敦祗恭厚，鲠固慎完，则见以为掘而不伦。多言繁称，连类比物，则见以为虚而无用。摠微说约，径省而不...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:09:57 点击：133 好评度：0
      <a href="/plus/feedback.php?aid=19"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div><div class='col1'>
    <div class='mainlinklist'>·<a href='/a/xianqinlianghan/fajia/2021/0810/18.html'><b>《韩非子》·有度</b></a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 国无常强，无常弱。奉法者强则国强，奉法者弱则国弱。荆庄王并国二十六，开地三千里，庄王之氓社稷也，而荆以亡。齐桓公并国三十，启地三千里，桓公之氓社稷也，而齐以亡。燕...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:09:37 点击：129 好评度：0
      <a href="/plus/feedback.php?aid=18"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div><div class='col1'>
    <div class='mainlinklist'>·<a href='/a/xianqinlianghan/fajia/2021/0810/17.html'><b>《韩非子》·初见秦</b></a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 臣闻不知而言不智，知而不言不忠，为人臣不忠当死，言而不当亦当死。虽然，臣愿悉言所闻，唯大王裁其罪。 臣闻天下阴燕阳魏，连荆固齐，收韩而成从，将西面以与秦强为难，臣窃...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:09:09 点击：74 好评度：0
      <a href="/plus/feedback.php?aid=17"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div> 篇文章
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-eye"></i>
                                    总浏览量 
                                </span>
                            </div>
                        </div>
                    </section>

                    <!-- 筛选和排序 -->
                    <section class="filter-section">
                        <div class="filter-controls">
                            <div class="filter-tabs">
                                <a href="?orderby=default" class="filter-tab active">默认排序</a>
                                <a href="?orderby=pubdate" class="filter-tab">最新发布</a>
                                <a href="?orderby=click" class="filter-tab">最多浏览</a>
                                <a href="?orderby=scores" class="filter-tab">最高评分</a>
                            </div>
                            <div class="view-mode">
                                <button class="view-btn active" data-view="grid">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="view-btn" data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                    </section>

                    <!-- 资源列表 -->
                    <section class="resources-list">
                        <div class="resource-grid" id="resourceGrid">
                            <div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31613404J.jpg" alt="<b>《韩非子》·存韩</b>" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    c,h,p"><b>《韩非子》·存韩</b></a></h3>
                                    <p class="card-desc">韩事秦三十馀年，出则为捍蔽，入则为席荐，秦特出锐师取韩地，而随之怨悬于</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            113
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            113
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/xianqinlianghan/fajia/2021/0810/20.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(20)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div><div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31F5145Y.png" alt="<b>《韩非子》·难言</b>" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    c,h,p"><b>《韩非子》·难言</b></a></h3>
                                    <p class="card-desc">臣非非难言也，所以难言者：言顺比滑泽，洋洋纚纚然，则见以为华而不实。敦</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            133
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            133
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/xianqinlianghan/fajia/2021/0810/19.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(19)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div><div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31F944W7.png" alt="<b>《韩非子》·有度</b>" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    c,h,p"><b>《韩非子》·有度</b></a></h3>
                                    <p class="card-desc">国无常强，无常弱。奉法者强则国强，奉法者弱则国弱。荆庄王并国二十六，开</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            129
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            129
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/xianqinlianghan/fajia/2021/0810/18.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(18)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div><div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210810/210823/1-210R31F924b6.png" alt="<b>《韩非子》·初见秦</b>" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    c,h,p,a"><b>《韩非子》·初见秦</b></a></h3>
                                    <p class="card-desc">臣闻不知而言不智，知而不言不忠，为人臣不忠当死，言而不当亦当死。虽然，</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            74
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            74
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/xianqinlianghan/fajia/2021/0810/17.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(17)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 列表视图 -->
                        <div class="resource-list-view" id="resourceList" style="display: none;">
                            <div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31613404J.jpg" alt="<b>《韩非子》·存韩</b>" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/xianqinlianghan/fajia/2021/0810/20.html"><b>《韩非子》·存韩</b></a></h3>
                                    <p class="item-desc">韩事秦三十馀年，出则为捍蔽，入则为席荐，秦特出锐师取韩地，而随之怨悬于天下，功归于强秦。且夫韩入贡职，与郡县无异也。今臣窃闻贵臣之计，举</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">113 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/xianqinlianghan/fajia/2021/0810/20.html" class="btn-primary">立即下载</a>
                                </div>
                            </div><div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31F5145Y.png" alt="<b>《韩非子》·难言</b>" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/xianqinlianghan/fajia/2021/0810/19.html"><b>《韩非子》·难言</b></a></h3>
                                    <p class="item-desc">臣非非难言也，所以难言者：言顺比滑泽，洋洋纚纚然，则见以为华而不实。敦祗恭厚，鲠固慎完，则见以为掘而不伦。多言繁称，连类比物，则见以为虚</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">133 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/xianqinlianghan/fajia/2021/0810/19.html" class="btn-primary">立即下载</a>
                                </div>
                            </div><div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31F944W7.png" alt="<b>《韩非子》·有度</b>" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/xianqinlianghan/fajia/2021/0810/18.html"><b>《韩非子》·有度</b></a></h3>
                                    <p class="item-desc">国无常强，无常弱。奉法者强则国强，奉法者弱则国弱。荆庄王并国二十六，开地三千里，庄王之氓社稷也，而荆以亡。齐桓公并国三十，启地三千里，桓</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">129 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/xianqinlianghan/fajia/2021/0810/18.html" class="btn-primary">立即下载</a>
                                </div>
                            </div><div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210810/210823/1-210R31F924b6.png" alt="<b>《韩非子》·初见秦</b>" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/xianqinlianghan/fajia/2021/0810/17.html"><b>《韩非子》·初见秦</b></a></h3>
                                    <p class="item-desc">臣闻不知而言不智，知而不言不忠，为人臣不忠当死，言而不当亦当死。虽然，臣愿悉言所闻，唯大王裁其罪。 臣闻天下阴燕阳魏，连荆固齐，收韩而成从</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">74 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/xianqinlianghan/fajia/2021/0810/17.html" class="btn-primary">立即下载</a>
                                </div>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <div class="pagination-wrapper">
                            <li><span class="pageinfo">共 <strong>1</strong>页<strong>4</strong>条记录</span></li>

                        </div>
                    </section>
                </div>

                <!-- 右侧边栏 -->
                <aside class="sidebar">
                    <!-- 热门资源 -->
                    <section class="sidebar-section">
                        <h3>本分类热门</h3>
                        <div class="sidebar-list-simple">
                            <div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/xianqinlianghan/fajia/2021/0810/19.html">《韩非子》·难言</a>
                                <span class="item-count">133</span>
                            </div>
<div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/xianqinlianghan/fajia/2021/0810/18.html">《韩非子》·有度</a>
                                <span class="item-count">129</span>
                            </div>
<div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/xianqinlianghan/fajia/2021/0810/20.html">《韩非子》·存韩</a>
                                <span class="item-count">113</span>
                            </div>
<div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/xianqinlianghan/fajia/2021/0810/17.html">《韩非子》·初见秦</a>
                                <span class="item-count">74</span>
                            </div>

                        </div>
                    </section>

                    <!-- 最新更新 -->
                    <section class="sidebar-section">
                        <h3>最新更新</h3>
                        <div class="sidebar-list">
                            <div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31613404J.jpg" alt="《韩非子》·存韩">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/xianqinlianghan/fajia/2021/0810/20.html">《韩非子》·存韩</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">113</span>
                                    </div>
                                </div>
                            </div>
<div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31F5145Y.png" alt="《韩非子》·难言">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/xianqinlianghan/fajia/2021/0810/19.html">《韩非子》·难言</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">133</span>
                                    </div>
                                </div>
                            </div>
<div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31F944W7.png" alt="《韩非子》·有度">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/xianqinlianghan/fajia/2021/0810/18.html">《韩非子》·有度</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">129</span>
                                    </div>
                                </div>
                            </div>
<div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210810/210823/1-210R31F924b6.png" alt="《韩非子》·初见秦">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/xianqinlianghan/fajia/2021/0810/17.html">《韩非子》·初见秦</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">74</span>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </section>

                    <!-- 相关分类 -->
                    <section class="sidebar-section">
                        <h3>相关分类</h3>
                        <div class="category-links">
                            
                        </div>
                    </section>

                    <!-- 标签云 -->
                    <section class="sidebar-section">
                        <h3>热门标签</h3>
                        <div class="tag-cloud">
                            
                        </div>
                    </section>

                    <!-- 广告位 -->
                    <section class="sidebar-section ad-section">
                        <div class="ad-banner">
                            <img src="/templets/default/images/ad-banner.jpg" alt="广告">
                        </div>
                    </section>
                </aside>
            </div>
        </div>
    </main>

无法在这个位置找到： footer_new.htm

<script>
// 视图切换功能
document.addEventListener('DOMContentLoaded', function() {
    const viewBtns = document.querySelectorAll('.view-btn');
    const gridView = document.getElementById('resourceGrid');
    const listView = document.getElementById('resourceList');
    
    viewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // 移除所有active类
            viewBtns.forEach(b => b.classList.remove('active'));
            
            // 添加active类到当前按钮
            this.classList.add('active');
            
            // 切换视图
            const viewMode = this.dataset.view;
            if (viewMode === 'grid') {
                gridView.style.display = 'grid';
                listView.style.display = 'none';
            } else {
                gridView.style.display = 'none';
                listView.style.display = 'block';
            }
            
            // 保存用户偏好
            localStorage.setItem('viewMode', viewMode);
        });
    });
    
    // 加载用户偏好的视图模式
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode) {
        const targetBtn = document.querySelector(`[data-view="${savedViewMode}"]`);
        if (targetBtn) {
            targetBtn.click();
        }
    }
});

// 添加到收藏夹
function addToFavorites(articleId) {
    // 这里可以添加AJAX请求来处理收藏功能
    console.log('添加到收藏夹:', articleId);
    
    // 显示提示信息
    showNotification('已添加到收藏夹', 'success');
}

// 筛选功能
document.querySelectorAll('.filter-tab').forEach(tab => {
    tab.addEventListener('click', function(e) {
        e.preventDefault();
        
        // 移除所有active类
        document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
        
        // 添加active类到当前标签
        this.classList.add('active');
        
        // 获取排序参数
        const url = new URL(this.href);
        const orderby = url.searchParams.get('orderby');
        
        // 重新加载页面或使用AJAX加载内容
        window.location.href = this.href;
    });
});
</script>
