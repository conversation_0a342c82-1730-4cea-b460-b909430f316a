<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《论语》_万婕源码网</title>
<meta name="keywords" content="《,论语,》,《,论语,》,是,儒家,经典,之一," />
<meta name="description" content="《论语》是 儒家经典 之一，是一部以记言为主的语录体散文集，主要以语录和对话文体的形式记录了孔子及其弟子的言行，集中体现了孔子的政治、审美、道德伦理和功利等价值思想" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=75">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=75";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>
<script language="javascript" type="text/javascript">
<!--
	function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
	}
	function postBadGood(ftype,fid)
	{
		var taget_obj = document.getElementById(ftype+fid);
		var saveid = GetCookie('badgoodid');
		if(saveid != null)
		{
			 var saveids = saveid.split(',');
			 var hasid = false;
			 saveid = '';
			 j = 1;
			 for(i=saveids.length-1;i>=0;i--)
			 {
			 	  if(saveids[i]==fid && hasid) continue;
			 	  else {
			 	  	if(saveids[i]==fid && !hasid) hasid = true;
			 	  	saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
			 	  	j++;
			 	  	if(j==10 && hasid) break;
			 	  	if(j==9 && !hasid) break;
			 	  }
			 }
	     if(hasid) { alert('您刚才已表决过了喔！'); return false;}
	     else saveid += ','+fid;
			 SetCookie('badgoodid',saveid,1);
		}
		else
		{
			SetCookie('badgoodid',fid,1);
		}
		//document.write("feedback.php?action="+ftype+"&fid="+fid);
		//return;
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
	  DedeXHTTP = null;
	}
-->
</script>
</head>
<body class="productview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	<li class='hover'><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
			<strong>当前位置:</strong> <a href='http://*************/'>主页</a> > <a href='/a/tushuguan/'>图书馆</a>
		</div><!-- /place -->
		<div class="viewbox">
			<div class="title">
				<h2>《论语》</h2>
			</div><!-- /title -->
			<div class="infolist">
                <small>信息类型：</small><span></span>
                <small>测试：</small><span></span>
				<small>商品原价：</small><span>200元</span>
				<small>优惠价格：</small><span class="fc-f60">100元</span>
				<small>品牌：</small><span>品牌</span>
                <small>单位：</small><span>本</span>
				<small>上架日期：</small><span>21-08-23 17:32</span>
				<small>人气：</small><span><script src="/plus/count.php?view=yes&aid=75&mid=1" type='text/javascript' language="javascript"></script></span>
                <form id="formcar" name="formcar" method="post" action="/plus/posttocar.php">
                <input type="hidden" name="id" value="75" />
                <input type="hidden" name="title" value="《论语》" />
                <input type="hidden" name="price" value="100" />
                <input type="hidden" name="units" value="本" />
                <small>购物车：</small><span><a href="/plus/car.php" target="_blank">查看购物车</a></span>
                <small>购买：</small><span><button type="submit" name="button" class="btn-2">放入购物车</button></span>
                </form>

			</div><!-- /info -->
			<div class="picview">
				<img src=http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2253U.png>
			</div><!-- /info -->
			<div class="labeltitle">
				<strong>商品介绍</strong>
			</div>
			<div class="content">
                 <div id="contentMidPicAD" style="float:right; clear:both; top:0; vertical-align:top;"></div>
			　　<span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">　</span>
<div class="para" data-pid="3" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	《论语》是<a data-lemmaid="7329675" href="https://baike.baidu.com/item/%E5%84%92%E5%AE%B6%E7%BB%8F%E5%85%B8/7329675" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">儒家经典</a>之一，是一部以记言为主的语录体散文集，主要以语录和对话文体的形式记录了孔子及其弟子的言行，集中体现了孔子的政治、审美、道德伦理和功利等价值思想。</div>
<div class="para" data-pid="4" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	《论语》内容涉及政治、教育、文学、哲学以及立身处世的道理等多方面。早在春秋后期孔子设坛讲学时期，其主体内容就已初始创成；孔子去世以后，他的弟子和再传弟子代代传授他的言论，并逐渐将这些口头记诵的语录言行记录下来，因此称为&ldquo;论&rdquo;；《论语》主要记载孔子及其弟子的言行，因此称为&ldquo;语&rdquo;。清朝<a data-lemmaid="22408" href="https://baike.baidu.com/item/%E8%B5%B5%E7%BF%BC/22408" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">赵翼</a>解释说：&ldquo;语者，圣人之语言，论者，诸儒之讨论也。&rdquo;其实，&ldquo;论&rdquo;又有纂的意思，所谓《论语》，是指将孔子及其弟子的言行记载下来编纂成书。现存《论语》20篇，492章，其中记录孔子与弟子及时人谈论之语约444章，记孔门弟子相互谈论之语48章。</div>
<div class="para" data-pid="5" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	作为儒家经典的《论语》，其内容博大精深，包罗万象，《论语》的思想主要有三个既各自独立又紧密相依的范畴：伦理道德范畴&mdash;&mdash;仁，社会政治范畴&mdash;&mdash;礼，认识方法论范畴&mdash;&mdash;中庸。仁，首先是人内心深处的一种真实的状态，折中真的极致必然是善的，这种真和善的全体状态就是&ldquo;仁&rdquo;。孔子确立的仁的范畴，进而将礼阐述为适应仁、表达仁的一种合理的社会关系与待人接物的规范，进而明确&ldquo;中庸&rdquo;的系统方法论原则。&ldquo;仁&rdquo;是《论语》的思想核心。</div>

			</div>
			<div class="boxoff">
				<strong>------分隔线----------------------------</strong>
			</div>
			<div class="handle">
				<div class="context">
					<ul>
						<li>上一篇：没有了 </li>
						<li>下一篇：<a href='/a/tushuguan/2021/0810/76.html'>《红楼梦》</a> </li>
					</ul>
				</div><!-- /context -->
				<div class="actbox">
					<ul>
						<li id="act-fav"><a href="/plus/stow.php?aid=75" target="_blank">收藏</a></li>
						<li id="act-err"><a href="/plus/erraddsave.php?aid=75&title=《论语》" target="_blank">挑错</a></li>
						<li id="act-pus"><a href="/plus/recommend.php?aid=75" target="_blank">推荐</a></li>
						<li id="act-pnt"><a href="#" target="_blank" onClick="window.print();">打印</a></li>
					</ul>
				</div><!-- /actbox -->
			</div><!-- /handle -->
		</div><!-- viewbox -->

<!-- //AJAX评论区 -->
<!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="75" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=75">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=75&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '75');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->


	</div><!-- /pleft -->

	<div class="pright">
        <div>
          <dl class="tbox">
				<dt><strong>栏目列表</strong></dt>
				<dd>
					<ul class="d6">
                      
					</ul>
				</dd>
			</dl>
        </div>
        	<div class="infos_userinfo">
 			
   	 	</div>
         
		<div class="productrange mt1">
			<dl class="tbox">
				<dt><strong>推荐商品</strong></dt>
				<dd>
					<ul class="f1">
                    
					</ul>
				</dd>
			</dl>
		</div>
        
		<div class="comment mt1">
			<dl class="tbox">
				<dt><strong>热门商品</strong></dt>
				<dd>
					<ul class="e3">
                    <li>
							<a href="/a/tushuguan/2021/0810/76.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2592D.png" alt="《红楼梦》"/></a>
							<a href="/a/tushuguan/2021/0810/76.html" class="title">《红楼梦》</a>
							<span class="intro">人气:<span class="fc-f90">173</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/79.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I600456.png" alt="《史书》"/></a>
							<a href="/a/tushuguan/2021/0810/79.html" class="title">《史书》</a>
							<span class="intro">人气:<span class="fc-f90">167</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/77.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I5034S.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/77.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">165</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/75.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2253U.png" alt="《论语》"/></a>
							<a href="/a/tushuguan/2021/0810/75.html" class="title">《论语》</a>
							<span class="intro">人气:<span class="fc-f90">143</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/87.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I645232.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/87.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">123</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/85.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I61XP.png" alt="《论语》二"/></a>
							<a href="/a/tushuguan/2021/0810/85.html" class="title">《论语》二</a>
							<span class="intro">人气:<span class="fc-f90">117</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/78.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I524352.png" alt="《孟子》"/></a>
							<a href="/a/tushuguan/2021/0810/78.html" class="title">《孟子》</a>
							<span class="intro">人气:<span class="fc-f90">59</span></span>
						</li>


					</ul>
				</dd>
			</dl>
		</div>

	</div><!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->

</body>
</html>
