<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《孟子》_万婕源码网</title>
<meta name="keywords" content="《,孟子,》,作者,简介,孟子,约,公元前,372年," />
<meta name="description" content="作者简介 孟子（约公元前372年-公元前289年），名轲，字不详(子舆、子居等字表皆出自伪书，或后人杜撰)，战国中期 鲁国 邹人（今山东 邹城 人），距离 孔子 的故乡曲阜不远。 孟子" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=78">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=78";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>
<script language="javascript" type="text/javascript">
<!--
	function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
	}
	function postBadGood(ftype,fid)
	{
		var taget_obj = document.getElementById(ftype+fid);
		var saveid = GetCookie('badgoodid');
		if(saveid != null)
		{
			 var saveids = saveid.split(',');
			 var hasid = false;
			 saveid = '';
			 j = 1;
			 for(i=saveids.length-1;i>=0;i--)
			 {
			 	  if(saveids[i]==fid && hasid) continue;
			 	  else {
			 	  	if(saveids[i]==fid && !hasid) hasid = true;
			 	  	saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
			 	  	j++;
			 	  	if(j==10 && hasid) break;
			 	  	if(j==9 && !hasid) break;
			 	  }
			 }
	     if(hasid) { alert('您刚才已表决过了喔！'); return false;}
	     else saveid += ','+fid;
			 SetCookie('badgoodid',saveid,1);
		}
		else
		{
			SetCookie('badgoodid',fid,1);
		}
		//document.write("feedback.php?action="+ftype+"&fid="+fid);
		//return;
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
	  DedeXHTTP = null;
	}
-->
</script>
</head>
<body class="productview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	<li class='hover'><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
			<strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/tushuguan/'>图书馆</a>
		</div><!-- /place -->
		<div class="viewbox">
			<div class="title">
				<h2>《孟子》</h2>
			</div><!-- /title -->
			<div class="infolist">
                <small>信息类型：</small><span>商品 -- 出售</span>
                <small>测试：</small><span>互联网 -- 网站制作</span>
				<small>商品原价：</small><span>160元</span>
				<small>优惠价格：</small><span class="fc-f60">50元</span>
				<small>品牌：</small><span>品牌</span>
                <small>单位：</small><span>本</span>
				<small>上架日期：</small><span>21-08-23 17:35</span>
				<small>人气：</small><span><script src="/plus/count.php?view=yes&aid=78&mid=1" type='text/javascript' language="javascript"></script></span>
                <form id="formcar" name="formcar" method="post" action="/plus/posttocar.php">
                <input type="hidden" name="id" value="78" />
                <input type="hidden" name="title" value="《孟子》" />
                <input type="hidden" name="price" value="50" />
                <input type="hidden" name="units" value="本" />
                <small>购物车：</small><span><a href="/plus/car.php" target="_blank">查看购物车</a></span>
                <small>购买：</small><span><button type="submit" name="button" class="btn-2">放入购物车</button></span>
                </form>

			</div><!-- /info -->
			<div class="picview">
				<img src=http://www.dedecms.com/demoimg/uploads/210823/1-210R31I524352.png>
			</div><!-- /info -->
			<div class="labeltitle">
				<strong>商品介绍</strong>
			</div>
			<div class="content">
                 <div id="contentMidPicAD" style="float:right; clear:both; top:0; vertical-align:top;"></div>
			　　<div class="para-title level-2  J-chapter" data-index="1" data-pid="4" label-module="para-title" style="padding: 0px; margin: 35px 0px 15px -30px; text-indent: 28px; caret-color: rgb(51, 51, 51); clear: both; border-left: 12px solid rgb(79, 156, 238); line-height: 24px; font-size: 22px; font-family: &quot;Microsoft YaHei&quot;, SimHei, Verdana; background-image: url(&quot;https://bkssl.bdimg.com/static/wiki-lemma/normal/resource/img/paraTitle-line_743dba1.png&quot;); position: relative; text-size-adjust: auto;">
	作者简介</div>
<div class="para" data-pid="6" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	<div class="album-wrap" style="padding: 0px; margin: 0px; border: 1px solid rgb(224, 224, 224); width: 220px; height: 264.73px; overflow: hidden; position: relative;">
		<font color="#136ec2" style="padding: 0px; margin: 0px;"><span style="padding: 0px; margin: 10px 0px 10px 20px; position: relative; float: right; clear: right; border-style: initial; border-color: initial; border-image: initial; height: 264.73px;"><img alt="孟子" class="picture" src="http://127.0.0.1/v5.7http://www.dedecms.com/demoimg/uploads/allimg/210722/13540V3N-0.jpg" style="padding: 0px; margin: 0px; border: 0px; width: 220px; height: 264.73px; display: block;" /></span></font></div>
	<div class="albumBg" style="padding: 0px; margin: 0px; position: relative; height: 4px; width: 220px;">
		&nbsp;</div>
	孟子（约公元前372年-公元前289年），名轲，字不详(子舆、子居等字表皆出自伪书，或后人杜撰)，战国中期<a data-lemmaid="2024164" href="https://baike.baidu.com/item/%E9%B2%81%E5%9B%BD/2024164" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">鲁国</a>邹人（今山东<a data-lemmaid="216350" href="https://baike.baidu.com/item/%E9%82%B9%E5%9F%8E/216350" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">邹城</a>人），距离<a href="https://baike.baidu.com/item/%E5%AD%94%E5%AD%90" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孔子</a>的故乡曲阜不远。</div>
<div class="para" data-pid="7" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	孟子是著名的<a data-lemmaid="1214475" href="https://baike.baidu.com/item/%E6%80%9D%E6%83%B3%E5%AE%B6/1214475" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">思想家</a>、<a data-lemmaid="1209571" href="https://baike.baidu.com/item/%E6%94%BF%E6%B2%BB%E5%AE%B6/1209571" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">政治家</a>、<a data-lemmaid="10341583" href="https://baike.baidu.com/item/%E6%95%99%E8%82%B2%E5%AE%B6/10341583" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">教育家</a>，孔子学说的继承者，儒家的重要代表人物。相传孟子是鲁国贵族<a data-lemmaid="4571032" href="https://baike.baidu.com/item/%E5%AD%9F%E5%AD%99%E6%B0%8F/4571032" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孟孙氏</a>的后裔，幼年丧父，家庭贫困，曾受业于子思（<a data-lemmaid="7271055" href="https://baike.baidu.com/item/%E5%AD%94%E4%BC%8B/7271055" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孔伋</a>，孔子之孙）的门人。学成以后，以士的身份游说诸侯，企图推行自己的政治主张，到过梁（魏）国、<a data-lemmaid="50284" href="https://baike.baidu.com/item/%E9%BD%90%E5%9B%BD/50284" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">齐国</a>、<a data-lemmaid="31041" href="https://baike.baidu.com/item/%E5%AE%8B%E5%9B%BD/31041" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">宋国</a>、<a data-lemmaid="25335" href="https://baike.baidu.com/item/%E6%BB%95%E5%9B%BD/25335" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">滕国</a>、<a data-lemmaid="2024164" href="https://baike.baidu.com/item/%E9%B2%81%E5%9B%BD/2024164" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">鲁国</a>。当时几个大国都致力于富国强兵，争取通过武力的手段实现统一。而他继承了孔子&ldquo;仁&rdquo;的思想并将其发展成为&ldquo;<a href="https://baike.baidu.com/item/%E4%BB%81%E6%94%BF" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">仁政</a>&rdquo;思想，被称为&ldquo;<a href="https://baike.baidu.com/item/%E4%BA%9A%E5%9C%A3" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">亚圣</a>&rdquo;。</div>
<div class="para" data-pid="8" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	<div class="album-wrap" style="padding: 0px; margin: 0px; border: 1px solid rgb(224, 224, 224); width: 220px; height: 229.91px; overflow: hidden; position: relative;">
		<img alt="孟子" class="picture" src="http://127.0.0.1/v5.7http://www.dedecms.com/demoimg/uploads/allimg/210722/13540SB2-1.jpg" style="padding: 0px; margin: 0px; border: 0px; width: 220px; height: 229.91px; display: block;" /></div>
	<div class="albumBg" style="padding: 0px; margin: 0px; position: relative; height: 4px; width: 220px;">
		&nbsp;</div>
	孟子的出生距孔子之死（公元前479）大约百年左右。关于他的身世，流传下来的已很少，《<a data-lemmaid="2970486" href="https://baike.baidu.com/item/%E9%9F%A9%E8%AF%97%E5%A4%96%E4%BC%A0/2970486" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">韩诗外传</a>》载有他母亲&ldquo;断织&rdquo;的故事，《<a href="https://baike.baidu.com/item/%E5%88%97%E5%A5%B3%E4%BC%A0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">列女传</a>》载有他母亲&ldquo;三迁&rdquo;和&ldquo;去齐&rdquo;等故事，可见他得力于母亲的教育不少。据《列女传》和赵岐《孟子题辞》说，孟子曾受教于孔子的孙子子思。但从年代推算，似乎不可信。《<a data-lemmaid="15749201" href="https://baike.baidu.com/item/%E5%8F%B2%E8%AE%B0%C2%B7%E5%AD%9F%E5%AD%90%E8%8D%80%E5%8D%BF%E5%88%97%E4%BC%A0/15749201" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史记&middot;孟子荀卿列传</a>》说他&ldquo;受业子思之门人&rdquo;，这倒是有可能的。无论是受业于子思也罢，子思门人也罢，孟子的学说都受到孔子思想的影响。所以，荀子把子思和孟子列为一派，这就是后世所称儒家中的思孟学派。</div>
<div class="para" data-pid="9" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	和孔子一样，孟子也曾带领学生游历魏、齐、宋、鲁、滕、薛等国，并一度担任过齐宣王的客卿。由于他的政治主张也与孔子的一样不被重用，所以便回到家乡聚徒讲学，与学生万章等人著书立说，&ldquo;序《<a data-lemmaid="9066319" href="https://baike.baidu.com/item/%E8%AF%97/9066319" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">诗</a>》《<a data-lemmaid="15970189" href="https://baike.baidu.com/item/%E4%B9%A6/15970189" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">书</a>》，述仲尼之意，作《孟子》七篇。&rdquo;（《<a data-lemmaid="15749201" href="https://baike.baidu.com/item/%E5%8F%B2%E8%AE%B0%C2%B7%E5%AD%9F%E5%AD%90%E8%8D%80%E5%8D%BF%E5%88%97%E4%BC%A0/15749201" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史记&middot;孟子荀卿列传</a>》）我们所见的《孟子》七篇，每篇分为上下，约三万五千字，一共二百六十章。</div>
<div class="para" data-pid="10" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	但《汉书.艺文志》著录&ldquo;孟子十一篇&rdquo;，比现存的《孟子》多出四篇。赵岐在为《孟子》作注时，对十一篇进行了鉴别，认为七篇为真，七篇以外的四篇为伪篇。东汉以后，这几篇便相继失佚了。赵岐在《<a href="https://baike.baidu.com/item/%E5%AD%9F%E5%AD%90%E9%A2%98%E8%BE%9E" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孟子题辞</a>》中把《孟子》与《<a href="https://baike.baidu.com/item/%E8%AE%BA%E8%AF%AD" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">论语</a>》相比，认为《孟子》是&ldquo;拟圣而作&rdquo;。所以，尽管《汉书.文艺志》仅仅把《孟子》放在诸子略中，视为子书，但实际上在汉代人的心目中已经把它看作辅助&ldquo;经书&rdquo;的&ldquo;传&rdquo;书了。</div>
<div class="para" data-pid="11" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	<a href="https://baike.baidu.com/item/%E6%B1%89%E6%96%87%E5%B8%9D" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">汉文帝</a>把《<a data-lemmaid="372830" href="https://baike.baidu.com/item/%E8%AE%BA%E8%AF%AD/372830" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">论语</a>》《<a data-lemmaid="779810" href="https://baike.baidu.com/item/%E5%AD%9D%E7%BB%8F/779810" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孝经</a>》《孟子》《<a data-lemmaid="735520" href="https://baike.baidu.com/item/%E5%B0%94%E9%9B%85/735520" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">尔雅</a>》各置博士，便叫&ldquo;传记博士&rdquo;。到五代后蜀时，后蜀主<a data-lemmaid="4215980" href="https://baike.baidu.com/item/%E5%AD%9F%E6%98%B6/4215980" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孟昶</a>命令人楷书十一经刻石，其中包括了《孟子》，这可能是《孟子》列入&ldquo;<a href="https://baike.baidu.com/item/%E7%BB%8F%E4%B9%A6" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">经书</a>&rdquo;的开始。后来<a href="https://baike.baidu.com/item/%E5%AE%8B%E5%A4%AA%E5%AE%97" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">宋太宗</a>又翻刻了这十一经。到南宋孝宗时，朱熹编《四书》列入了《孟子》，正式把《孟子》提到了非常高的地位。元、明以后又成为科举考试的内容，更是读书人的必读之书了。</div>
<div class="para-title level-3  " data-index="1_2" data-pid="12" label-module="para-title" style="padding: 0px; margin: 20px 0px 12px; text-indent: 28px; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); clear: both; zoom: 1; line-height: 20px; font-size: 18px; font-family: &quot;Microsoft YaHei&quot;, SimHei, Verdana; text-size-adjust: auto;">
	<h3 class="title-text" style="padding: 0px; margin: 0px; font-size: 18px; font-weight: 400;">
		主要封赠</h3>
</div>
<div class="para" data-pid="13" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	战国<a href="https://baike.baidu.com/item/%E9%BD%90%E5%AE%A3%E7%8E%8B" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">齐宣王</a>在稷下学宫册封的第一任&ldquo;上大夫&rdquo;就是孟子。1083年（宋元丰六年），升邹国公。1330年（元<a data-lemmaid="417219" href="https://baike.baidu.com/item/%E8%87%B3%E9%A1%BA/417219" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">至顺</a>元年），加赠为邹国亚圣公。1530年（明<a data-lemmaid="5520882" href="https://baike.baidu.com/item/%E5%98%89%E9%9D%96/5520882" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">嘉靖</a>九年），奉为亚圣，罢公爵。明<a data-lemmaid="13301" href="https://baike.baidu.com/item/%E6%99%AF%E6%B3%B0/13301" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">景泰</a>二年，孟子嫡派后裔被封为<a href="https://baike.baidu.com/item/%E7%BF%B0%E6%9E%97%E9%99%A2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">翰林院</a><a href="https://baike.baidu.com/item/%E4%BA%94%E7%BB%8F%E5%8D%9A%E5%A3%AB" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">五经博士</a>，子孙世袭，一直到<a data-lemmaid="2209725" href="https://baike.baidu.com/item/%E6%B0%91%E5%9B%BD/2209725" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">民国</a>3年，73代翰林院五经博士孟庆棠改封<a data-lemmaid="4796503" href="https://baike.baidu.com/item/%E5%A5%89%E7%A5%80%E5%AE%98/4796503" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">奉祀官</a>，民国24年改称亚圣奉祀官。</div>
<div class="para-title level-2  J-chapter" data-index="2" data-pid="14" label-module="para-title" style="padding: 0px; margin: 35px 0px 15px -30px; text-indent: 28px; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); clear: both; border-left: 12px solid rgb(79, 156, 238); line-height: 24px; font-size: 22px; font-family: &quot;Microsoft YaHei&quot;, SimHei, Verdana; background-image: url(&quot;https://bkssl.bdimg.com/static/wiki-lemma/normal/resource/img/paraTitle-line_743dba1.png&quot;); position: relative; text-size-adjust: auto;">
	<h2 class="title-text" style="padding: 0px 8px 0px 18px; margin: 0px; font-size: 22px; color: rgb(0, 0, 0); float: left; line-height: 24px; font-weight: 400;">
		著作简介</h2>
	<a class="audio-play part-audio-play J-part-audio-play" style="padding: 0px 0px 0px 24px; margin: 0px; color: rgb(136, 136, 136); display: block; float: right; height: 22px; line-height: 22px; font-size: 12px; font-family: SimSun; outline: 0px;"><span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span></a></div>
<div class="para" data-pid="15" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	《孟子》一书共七篇，是战国时期孟子的言论汇编，记录了孟子与其他各家思想的争辩，对弟子的言传身教，游说诸侯等内容，由孟子及其弟子（<a href="https://baike.baidu.com/item/%E4%B8%87%E7%AB%A0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">万章</a>等）共同编撰而成。《孟子》记录了孟子的治国思想、政治策略（仁政、王霸之辨、民本、格君心之非，民为贵社稷次之君为轻）和政治行动，成书大约在战国中期，属儒家经典著作。</div>
<div class="para" data-pid="16" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	其学说出发点为性善论，主张德治。南宋时朱熹将《孟子》与《论语》《大学》《中庸》合在一起称&ldquo;四书&rdquo;。自从宋、元、明、清以来，都把它当做家传户诵的书。就像我们的教科书一样。</div>
<div class="para" data-pid="17" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	<div class="lemma-picture J-lemma-picture text-pic layout-right" style="padding: 0px; margin: 0px 0px 3px 20px; border: 1px solid rgb(224, 224, 224); overflow: hidden; position: relative; float: right; clear: right; width: 220px;">
		<font color="#136ec2" style="padding: 0px; margin: 0px;"><span style="padding: 0px; margin: 0px auto; height: 146.618px; border-style: initial; border-color: initial; border-image: initial;"><img alt="孟子" src="http://127.0.0.1/v5.7http://www.dedecms.com/demoimg/uploads/allimg/210722/13540Q393-2.jpg" style="padding: 0px; margin: 0px auto; border: 0px; display: block; width: 220px; height: 146.618px;" /></span></font><span class="description" style="padding: 8px 7px; margin: 0px; display: block; color: rgb(85, 85, 85); font-size: 12px; text-indent: 0px; font-family: 宋体; overflow-wrap: break-word; word-break: break-all; line-height: 24px; min-height: 12px; border-top: 1px solid rgb(224, 224, 224);">孟子</span></div>
	《孟子》是四书中篇幅最长，部头最重的一本，有三万五千多字，直到清末都是科举必考内容。《孟子》这部书的理论，不但纯粹宏博，文章也极雄健优美。（五经：《诗》《书》《礼》《易》《<a data-lemmaid="982820" href="https://baike.baidu.com/item/%E6%98%A5%E7%A7%8B/982820" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">春秋</a>》）《孟子》是记录孟轲言行的一部著作，也是儒家重要经典之一。篇目有：（一）《<a data-lemmaid="1097709" href="https://baike.baidu.com/item/%E6%A2%81%E6%83%A0%E7%8E%8B/1097709" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">梁惠王</a>》上、下，（二）《<a href="https://baike.baidu.com/item/%E5%85%AC%E5%AD%99%E4%B8%91" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">公孙丑</a>》上、下，（三）《<a data-lemmaid="5846932" href="https://baike.baidu.com/item/%E6%BB%95%E6%96%87%E5%85%AC/5846932" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">滕文公</a>》上、下，（四）《<a data-lemmaid="10412554" href="https://baike.baidu.com/item/%E7%A6%BB%E5%A8%84/10412554" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">离娄</a>》上、下，（五）《<a href="https://baike.baidu.com/item/%E4%B8%87%E7%AB%A0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">万章</a>》上、下，（六）《<a data-lemmaid="7271495" href="https://baike.baidu.com/item/%E5%91%8A%E5%AD%90/7271495" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">告子</a>》上、下，（七）《尽心》上、下。</div>
<div class="para" data-pid="18" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	《孟子》行文气势磅礴，感情充沛，雄辩滔滔，极富感染力，流传后世，影响深远，成为儒家经典著作之一。</div>
<div class="para" data-pid="19" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	《史记&middot;孟荀列传》：&ldquo;孟轲所如不合，退与万章之徒序《诗》、《书》，述仲尼之意，作《孟子》七篇。&rdquo;谓《孟子》七篇由孟轲自作，赵岐《孟子题辞》曰：&ldquo;此书孟子之所作也，故总谓之《孟子》。&rdquo;又曰：&ldquo;于是退而论集，所与高弟弟子公孙丑、万章之徒，难疑答问，又自撰其法度之言，着书七篇。&rdquo;此亦主孟子自撰。</div>
<div class="para" data-pid="20" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	清<a data-lemmaid="699774" href="https://baike.baidu.com/item/%E9%98%8E%E8%8B%A5%E7%92%A9/699774" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">阎若璩</a>《<a data-lemmaid="770709" href="https://baike.baidu.com/item/%E5%AD%9F%E5%AD%90%E7%94%9F%E5%8D%92%E5%B9%B4%E6%9C%88%E8%80%83/770709" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孟子生卒年月考</a>》亦以孟子自作是，且曰：&ldquo;《论语》成于门人之手，故记圣人容貌甚悉。七篇成于己手，故但记言语或出处耳。&rdquo;但考诸《孟子》，孟轲所见时君如梁惠王、梁襄王、齐宣王、邹穆公、滕文公、鲁平公等皆称谥号，恐非孟子自作时所为也；又记孟子弟子乐正子、公都子、屋卢子皆以&ldquo;子&rdquo;称，也断非孟子之所为，其编定者极可能是孟子的弟子。成书大约在战国中期。</div>
<div class="para" data-pid="21" label-module="para" style="padding: 0px; margin: 0px 0px 15px; font-family: arial, 宋体, sans-serif; font-size: 14px; text-indent: 2em; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
	《孟子》的主要注本有《<a href="https://baike.baidu.com/item/%E5%AD%9F%E5%AD%90%E6%B3%A8%E7%96%8F" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孟子注疏</a>》，《<a href="https://baike.baidu.com/item/%E5%9B%9B%E9%83%A8%E5%A4%87%E8%A6%81" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">四部备要</a>》本14卷；《<a href="https://baike.baidu.com/item/%E5%AD%9F%E5%AD%90%E9%9B%86%E6%B3%A8" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孟子集注</a>》，《四部备要》本7卷；《孟子正义》，《四部备要》本30卷。另有今人<a data-lemmaid="1142405" href="https://baike.baidu.com/item/%E6%9D%A8%E4%BC%AF%E5%B3%BB/1142405" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">杨伯峻</a>《<a data-lemmaid="2229844" href="https://baike.baidu.com/item/%E5%AD%9F%E5%AD%90%E8%AF%91%E6%B3%A8/2229844" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孟子译注</a>》（<a href="https://baike.baidu.com/item/%E4%B8%AD%E5%8D%8E%E4%B9%A6%E5%B1%80" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">中华书局</a>本）。</div>

			</div>
			<div class="boxoff">
				<strong>------分隔线----------------------------</strong>
			</div>
			<div class="handle">
				<div class="context">
					<ul>
						<li>上一篇：<a href='/a/tushuguan/2021/0810/77.html'>《孙子兵法》</a> </li>
						<li>下一篇：<a href='/a/tushuguan/2021/0810/79.html'>《史书》</a> </li>
					</ul>
				</div><!-- /context -->
				<div class="actbox">
					<ul>
						<li id="act-fav"><a href="/plus/stow.php?aid=78" target="_blank">收藏</a></li>
						<li id="act-err"><a href="/plus/erraddsave.php?aid=78&title=《孟子》" target="_blank">挑错</a></li>
						<li id="act-pus"><a href="/plus/recommend.php?aid=78" target="_blank">推荐</a></li>
						<li id="act-pnt"><a href="#" target="_blank" onClick="window.print();">打印</a></li>
					</ul>
				</div><!-- /actbox -->
			</div><!-- /handle -->
		</div><!-- viewbox -->

<!-- //AJAX评论区 -->
<!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="78" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=78">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=78&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '78');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->


	</div><!-- /pleft -->

	<div class="pright">
        <div>
          <dl class="tbox">
				<dt><strong>栏目列表</strong></dt>
				<dd>
					<ul class="d6">
                      
					</ul>
				</dd>
			</dl>
        </div>
        	<div class="infos_userinfo">
 			
   	 	</div>
         
		<div class="productrange mt1">
			<dl class="tbox">
				<dt><strong>推荐商品</strong></dt>
				<dd>
					<ul class="f1">
                    
					</ul>
				</dd>
			</dl>
		</div>
        
		<div class="comment mt1">
			<dl class="tbox">
				<dt><strong>热门商品</strong></dt>
				<dd>
					<ul class="e3">
                    <li>
							<a href="/a/tushuguan/2021/0810/76.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2592D.png" alt="《红楼梦》"/></a>
							<a href="/a/tushuguan/2021/0810/76.html" class="title">《红楼梦》</a>
							<span class="intro">人气:<span class="fc-f90">175</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/79.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I600456.png" alt="《史书》"/></a>
							<a href="/a/tushuguan/2021/0810/79.html" class="title">《史书》</a>
							<span class="intro">人气:<span class="fc-f90">167</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/77.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I5034S.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/77.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">165</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/75.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2253U.png" alt="《论语》"/></a>
							<a href="/a/tushuguan/2021/0810/75.html" class="title">《论语》</a>
							<span class="intro">人气:<span class="fc-f90">143</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/87.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I645232.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/87.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">123</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/85.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I61XP.png" alt="《论语》二"/></a>
							<a href="/a/tushuguan/2021/0810/85.html" class="title">《论语》二</a>
							<span class="intro">人气:<span class="fc-f90">117</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/78.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I524352.png" alt="《孟子》"/></a>
							<a href="/a/tushuguan/2021/0810/78.html" class="title">《孟子》</a>
							<span class="intro">人气:<span class="fc-f90">59</span></span>
						</li>


					</ul>
				</dd>
			</dl>
		</div>

	</div><!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->

</body>
</html>
