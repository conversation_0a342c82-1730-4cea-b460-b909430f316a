<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《红楼梦》_万婕源码网</title>
<meta name="keywords" content="《,红楼梦,》,商品,介绍,《,红楼梦,》,中国," />
<meta name="description" content="商品介绍 《红楼梦》，中国古代 章回体 长篇小说 ，中国古典 四大名著 之一，一般认为是清代作家 曹雪芹 所著。小说以贾、史、王、薛 四大家族 的兴衰为背景，以富贵公子 贾宝玉" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=76">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=76";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>
<script language="javascript" type="text/javascript">
<!--
	function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
	}
	function postBadGood(ftype,fid)
	{
		var taget_obj = document.getElementById(ftype+fid);
		var saveid = GetCookie('badgoodid');
		if(saveid != null)
		{
			 var saveids = saveid.split(',');
			 var hasid = false;
			 saveid = '';
			 j = 1;
			 for(i=saveids.length-1;i>=0;i--)
			 {
			 	  if(saveids[i]==fid && hasid) continue;
			 	  else {
			 	  	if(saveids[i]==fid && !hasid) hasid = true;
			 	  	saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
			 	  	j++;
			 	  	if(j==10 && hasid) break;
			 	  	if(j==9 && !hasid) break;
			 	  }
			 }
	     if(hasid) { alert('您刚才已表决过了喔！'); return false;}
	     else saveid += ','+fid;
			 SetCookie('badgoodid',saveid,1);
		}
		else
		{
			SetCookie('badgoodid',fid,1);
		}
		//document.write("feedback.php?action="+ftype+"&fid="+fid);
		//return;
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
	  DedeXHTTP = null;
	}
-->
</script>
</head>
<body class="productview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	<li class='hover'><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
			<strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/tushuguan/'>图书馆</a>
		</div><!-- /place -->
		<div class="viewbox">
			<div class="title">
				<h2>《红楼梦》</h2>
			</div><!-- /title -->
			<div class="infolist">
                <small>信息类型：</small><span>商品 -- 出售</span>
                <small>测试：</small><span>互联网 -- 网站制作</span>
				<small>商品原价：</small><span>300元</span>
				<small>优惠价格：</small><span class="fc-f60">100元</span>
				<small>品牌：</small><span>品牌</span>
                <small>单位：</small><span>本</span>
				<small>上架日期：</small><span>21-08-23 17:38</span>
				<small>人气：</small><span><script src="/plus/count.php?view=yes&aid=76&mid=1" type='text/javascript' language="javascript"></script></span>
                <form id="formcar" name="formcar" method="post" action="/plus/posttocar.php">
                <input type="hidden" name="id" value="76" />
                <input type="hidden" name="title" value="《红楼梦》" />
                <input type="hidden" name="price" value="100" />
                <input type="hidden" name="units" value="本" />
                <small>购物车：</small><span><a href="/plus/car.php" target="_blank">查看购物车</a></span>
                <small>购买：</small><span><button type="submit" name="button" class="btn-2">放入购物车</button></span>
                </form>

			</div><!-- /info -->
			<div class="picview">
				<img src=http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2592D.png>
			</div><!-- /info -->
			<div class="labeltitle">
				<strong>商品介绍</strong>
			</div>
			<div class="content">
                 <div id="contentMidPicAD" style="float:right; clear:both; top:0; vertical-align:top;"></div>
			　　<div style="text-indent: 12px;">
	&nbsp;</div>
<div class="viewbox" style="padding: 0px 0px 8px; margin: 0px; width: 712px; overflow: hidden; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma;">
	<div class="title" style="padding: 10px 0px 0px; margin: 0px; height: 56px; line-height: 56px; text-align: center; overflow: hidden;">
		<h2 style="padding: 0px; margin: 0px; font-size: 24px; color: rgb(43, 43, 43);">
			<strong style="background: url(&quot;../images/view-labeltitle-bg.gif&quot;) left -44px no-repeat; font-size: 12px; padding: 0px; margin: 0px; width: 80px; height: 23px; line-height: 23px; color: rgb(255, 255, 255); letter-spacing: 1px; float: left; overflow: hidden; display: inline !important;">商品介绍<span style="color: rgb(51, 51, 51); font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51);">《红楼梦》，中国古代</span><a data-lemmaid="11018000" href="https://baike.baidu.com/item/%E7%AB%A0%E5%9B%9E%E4%BD%93/11018000" style="font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">章回体</a><a data-lemmaid="7708668" href="https://baike.baidu.com/item/%E9%95%BF%E7%AF%87%E5%B0%8F%E8%AF%B4/7708668" style="font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">长篇小说</a><span style="color: rgb(51, 51, 51); font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51);">，中国古典</span><a data-lemmaid="8376" href="https://baike.baidu.com/item/%E5%9B%9B%E5%A4%A7%E5%90%8D%E8%91%97/8376" style="font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">四大名著</a><span style="color: rgb(51, 51, 51); font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51);">之一，一般认为是清代作家</span><a data-lemmaid="14919" href="https://baike.baidu.com/item/%E6%9B%B9%E9%9B%AA%E8%8A%B9/14919" style="font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">曹雪芹</a><span style="color: rgb(51, 51, 51); font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51);">所著。小说以贾、史、王、薛</span><a data-lemmaid="2314013" href="https://baike.baidu.com/item/%E5%9B%9B%E5%A4%A7%E5%AE%B6%E6%97%8F/2314013" style="font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">四大家族</a><span style="color: rgb(51, 51, 51); font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51);">的兴衰为背景，以富贵公子</span><a data-lemmaid="59563" href="https://baike.baidu.com/item/%E8%B4%BE%E5%AE%9D%E7%8E%89/59563" style="font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">贾宝玉</a><span style="color: rgb(51, 51, 51); font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51);">为视角，以贾宝玉与</span><a data-lemmaid="260081" href="https://baike.baidu.com/item/%E6%9E%97%E9%BB%9B%E7%8E%89/260081" style="font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">林黛玉</a><span style="color: rgb(51, 51, 51); font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51);">、</span><a data-lemmaid="396702" href="https://baike.baidu.com/item/%E8%96%9B%E5%AE%9D%E9%92%97/396702" style="font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">薛宝钗</a><span style="color: rgb(51, 51, 51); font-size: 14px; font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51);">的爱情婚姻悲剧为主线，描绘了一批举止见识出于须眉之上的闺阁佳人的人生百态，展现了真正的人性美和悲剧美，可以说是一部从各个角度展现女性美以及中国古代社会世态百相的史诗性著作。</span><br />
			<br />
			<span style="font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); font-size: 14px;">《红楼梦》版本有120回&ldquo;</span><a data-lemmaid="54302962" href="https://baike.baidu.com/item/%E7%A8%8B%E6%9C%AC/54302962" style="font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); font-size: 14px; padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">程本</a><span style="font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); font-size: 14px;">&rdquo;和80回&ldquo;</span><a data-lemmaid="9517049" href="https://baike.baidu.com/item/%E8%84%82%E6%9C%AC/9517049" style="font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); font-size: 14px; padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">脂本</a><span style="font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); font-size: 14px;">&rdquo;两大系统。程本为</span><a href="https://baike.baidu.com/item/%E7%A8%8B%E4%BC%9F%E5%85%83" style="font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); font-size: 14px; padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">程伟元</a><span style="font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); font-size: 14px;">排印的印刷本，脂本为</span><a href="https://baike.baidu.com/item/%E8%84%82%E7%A0%9A%E6%96%8B" style="font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); font-size: 14px; padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">脂砚斋</a><span style="font-family: arial, 宋体, sans-serif; text-indent: 28px; caret-color: rgb(51, 51, 51); color: rgb(51, 51, 51); font-size: 14px;">在不同时期抄评的早期手抄本。脂本是程本的底本。</span></strong></h2>
	</div>
	<div class="content" style="padding: 12px 16px; margin: 0px; font-size: 14px; line-height: 25px; color: rgb(51, 51, 51);">
		<div class="para" data-pid="3" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 28px; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
			<div class="para" data-pid="1" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
				<a data-lemmaid="11018000" href="https://baike.baidu.com/item/%E7%AB%A0%E5%9B%9E%E4%BD%93/11018000" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">回体</a><a data-lemmaid="7708668" href="https://baike.baidu.com/item/%E9%95%BF%E7%AF%87%E5%B0%8F%E8%AF%B4/7708668" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">长篇小说</a>，中国古典<a data-lemmaid="8376" href="https://baike.baidu.com/item/%E5%9B%9B%E5%A4%A7%E5%90%8D%E8%91%97/8376" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">四大名著</a>之一，一般认为是清代作家<a data-lemmaid="14919" href="https://baike.baidu.com/item/%E6%9B%B9%E9%9B%AA%E8%8A%B9/14919" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">曹雪芹</a>所著。小说以贾、史、王、薛<a data-lemmaid="2314013" href="https://baike.baidu.com/item/%E5%9B%9B%E5%A4%A7%E5%AE%B6%E6%97%8F/2314013" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">四大家族</a>的兴衰为背景，以富贵公子<a data-lemmaid="59563" href="https://baike.baidu.com/item/%E8%B4%BE%E5%AE%9D%E7%8E%89/59563" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">贾宝玉</a>为视角，以贾宝玉与<a data-lemmaid="260081" href="https://baike.baidu.com/item/%E6%9E%97%E9%BB%9B%E7%8E%89/260081" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">林黛玉</a>、<a data-lemmaid="396702" href="https://baike.baidu.com/item/%E8%96%9B%E5%AE%9D%E9%92%97/396702" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">薛宝钗</a>的爱情婚姻悲剧为主线，描绘了一批举止见识出于须眉之上的闺阁佳人的人生百态，展现了真正的人性美和悲剧美，可以说是一部从各个角度展现女性美以及中国古代社会世态百相的史诗性著作。</div>
			<div class="para" data-pid="2" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
				《红楼梦》版本有120回&ldquo;<a data-lemmaid="54302962" href="https://baike.baidu.com/item/%E7%A8%8B%E6%9C%AC/54302962" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">程本</a>&rdquo;和80回&ldquo;<a data-lemmaid="9517049" href="https://baike.baidu.com/item/%E8%84%82%E6%9C%AC/9517049" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">脂本</a>&rdquo;两大系统。程本为<a href="https://baike.baidu.com/item/%E7%A8%8B%E4%BC%9F%E5%85%83" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">程伟元</a>排印的印刷本，脂本为<a href="https://baike.baidu.com/item/%E8%84%82%E7%A0%9A%E6%96%8B" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">脂砚斋</a>在不同时期抄评的早期手抄本。脂本是程本的底本。</div>
			<div class="para" data-pid="3" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; line-height: 24px; zoom: 1; text-size-adjust: auto;">
				《红楼梦》是一部具有世界影响力的<a href="https://baike.baidu.com/item/%E4%BA%BA%E6%83%85%E5%B0%8F%E8%AF%B4" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">人情小说</a>，举世公认的<a href="https://baike.baidu.com/item/%E4%B8%AD%E5%9B%BD%E5%8F%A4%E5%85%B8%E5%B0%8F%E8%AF%B4" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">中国古典小说</a>巅峰之作，中国封建社会的百科全书，传统文化的集大成者。小说作者以&ldquo;大旨谈情，实录其事&rdquo;自勉，只按自己的事体情理，按迹循踪，摆脱旧套，新鲜别致，取得了非凡的艺术成就。&ldquo;真事隐去，假语村言&rdquo;的特殊笔法更是令后世读者脑洞大开，揣测之说久而遂多。二十世纪以来，学术界因《红楼梦》异常出色的艺术成就和丰富深刻的思想底蕴而产生了以《红楼梦》为研究对象的专门学问&mdash;&mdash;<a href="https://baike.baidu.com/item/%E7%BA%A2%E5%AD%A6" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">红学</a>。</div>
		</div>
	</div>
</div>
<br />

			</div>
			<div class="boxoff">
				<strong>------分隔线----------------------------</strong>
			</div>
			<div class="handle">
				<div class="context">
					<ul>
						<li>上一篇：<a href='/a/tushuguan/2021/0810/75.html'>《论语》</a> </li>
						<li>下一篇：<a href='/a/tushuguan/2021/0810/77.html'>《孙子兵法》</a> </li>
					</ul>
				</div><!-- /context -->
				<div class="actbox">
					<ul>
						<li id="act-fav"><a href="/plus/stow.php?aid=76" target="_blank">收藏</a></li>
						<li id="act-err"><a href="/plus/erraddsave.php?aid=76&title=《红楼梦》" target="_blank">挑错</a></li>
						<li id="act-pus"><a href="/plus/recommend.php?aid=76" target="_blank">推荐</a></li>
						<li id="act-pnt"><a href="#" target="_blank" onClick="window.print();">打印</a></li>
					</ul>
				</div><!-- /actbox -->
			</div><!-- /handle -->
		</div><!-- viewbox -->

<!-- //AJAX评论区 -->
<!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="76" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=76">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=76&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '76');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->


	</div><!-- /pleft -->

	<div class="pright">
        <div>
          <dl class="tbox">
				<dt><strong>栏目列表</strong></dt>
				<dd>
					<ul class="d6">
                      
					</ul>
				</dd>
			</dl>
        </div>
        	<div class="infos_userinfo">
 			
   	 	</div>
         
		<div class="productrange mt1">
			<dl class="tbox">
				<dt><strong>推荐商品</strong></dt>
				<dd>
					<ul class="f1">
                    
					</ul>
				</dd>
			</dl>
		</div>
        
		<div class="comment mt1">
			<dl class="tbox">
				<dt><strong>热门商品</strong></dt>
				<dd>
					<ul class="e3">
                    <li>
							<a href="/a/tushuguan/2021/0810/76.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2592D.png" alt="《红楼梦》"/></a>
							<a href="/a/tushuguan/2021/0810/76.html" class="title">《红楼梦》</a>
							<span class="intro">人气:<span class="fc-f90">175</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/79.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I600456.png" alt="《史书》"/></a>
							<a href="/a/tushuguan/2021/0810/79.html" class="title">《史书》</a>
							<span class="intro">人气:<span class="fc-f90">167</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/77.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I5034S.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/77.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">165</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/75.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2253U.png" alt="《论语》"/></a>
							<a href="/a/tushuguan/2021/0810/75.html" class="title">《论语》</a>
							<span class="intro">人气:<span class="fc-f90">143</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/87.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I645232.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/87.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">123</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/85.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I61XP.png" alt="《论语》二"/></a>
							<a href="/a/tushuguan/2021/0810/85.html" class="title">《论语》二</a>
							<span class="intro">人气:<span class="fc-f90">117</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/78.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I524352.png" alt="《孟子》"/></a>
							<a href="/a/tushuguan/2021/0810/78.html" class="title">《孟子》</a>
							<span class="intro">人气:<span class="fc-f90">59</span></span>
						</li>


					</ul>
				</dd>
			</dl>
		</div>

	</div><!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->

</body>
</html>
