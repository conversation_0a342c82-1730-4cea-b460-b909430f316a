<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《史书》_万婕源码网</title>
<meta name="keywords" content="《,史书,》,史书,发展,中国,西周,末年,各," />
<meta name="description" content="史书发展 中国 西周 末年各诸侯国已有历 史记 载，如 晋国 之《 乘 》、郑国之《 志 》、楚国之《 梼杌 》、 鲁国 之《 春秋 》等，《 墨子 》里面说 墨翟 曾见过百国春秋。 早期的史" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=79">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=79";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>
<script language="javascript" type="text/javascript">
<!--
	function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
	}
	function postBadGood(ftype,fid)
	{
		var taget_obj = document.getElementById(ftype+fid);
		var saveid = GetCookie('badgoodid');
		if(saveid != null)
		{
			 var saveids = saveid.split(',');
			 var hasid = false;
			 saveid = '';
			 j = 1;
			 for(i=saveids.length-1;i>=0;i--)
			 {
			 	  if(saveids[i]==fid && hasid) continue;
			 	  else {
			 	  	if(saveids[i]==fid && !hasid) hasid = true;
			 	  	saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
			 	  	j++;
			 	  	if(j==10 && hasid) break;
			 	  	if(j==9 && !hasid) break;
			 	  }
			 }
	     if(hasid) { alert('您刚才已表决过了喔！'); return false;}
	     else saveid += ','+fid;
			 SetCookie('badgoodid',saveid,1);
		}
		else
		{
			SetCookie('badgoodid',fid,1);
		}
		//document.write("feedback.php?action="+ftype+"&fid="+fid);
		//return;
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
	  DedeXHTTP = null;
	}
-->
</script>
</head>
<body class="productview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	<li class='hover'><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
			<strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/tushuguan/'>图书馆</a>
		</div><!-- /place -->
		<div class="viewbox">
			<div class="title">
				<h2>《史书》</h2>
			</div><!-- /title -->
			<div class="infolist">
                <small>信息类型：</small><span>商品 -- 出售</span>
                <small>测试：</small><span>互联网 -- 网站制作</span>
				<small>商品原价：</small><span>230元</span>
				<small>优惠价格：</small><span class="fc-f60">120元</span>
				<small>品牌：</small><span>品牌</span>
                <small>单位：</small><span></span>
				<small>上架日期：</small><span>21-08-23 17:35</span>
				<small>人气：</small><span><script src="/plus/count.php?view=yes&aid=79&mid=1" type='text/javascript' language="javascript"></script></span>
                <form id="formcar" name="formcar" method="post" action="/plus/posttocar.php">
                <input type="hidden" name="id" value="79" />
                <input type="hidden" name="title" value="《史书》" />
                <input type="hidden" name="price" value="120" />
                <input type="hidden" name="units" value="" />
                <small>购物车：</small><span><a href="/plus/car.php" target="_blank">查看购物车</a></span>
                <small>购买：</small><span><button type="submit" name="button" class="btn-2">放入购物车</button></span>
                </form>

			</div><!-- /info -->
			<div class="picview">
				<img src=http://www.dedecms.com/demoimg/uploads/210823/1-210R31I600456.png>
			</div><!-- /info -->
			<div class="labeltitle">
				<strong>商品介绍</strong>
			</div>
			<div class="content">
                 <div id="contentMidPicAD" style="float:right; clear:both; top:0; vertical-align:top;"></div>
			　　<div class="para-title level-3  " data-index="1_1" data-pid="5" label-module="para-title" style="padding: 0px; margin: 20px 0px 12px; color: rgb(51, 51, 51); clear: both; zoom: 1; line-height: 20px; font-size: 18px; font-family: &quot;Microsoft YaHei&quot;, SimHei, Verdana; caret-color: rgb(51, 51, 51); text-size-adjust: auto;">
	<h3 class="title-text" style="padding: 0px; margin: 0px; font-size: 18px; font-weight: 400;">
		史书发展</h3>
</div>
<div class="para" data-pid="6" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	中国<a href="https://baike.baidu.com/item/%E8%A5%BF%E5%91%A8" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">西周</a>末年各诸侯国已有历<a href="https://baike.baidu.com/item/%E5%8F%B2%E8%AE%B0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史记</a>载，如<a href="https://baike.baidu.com/item/%E6%99%8B%E5%9B%BD" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">晋国</a>之《<a href="https://baike.baidu.com/item/%E4%B9%98" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">乘</a>》、郑国之《<a href="https://baike.baidu.com/item/%E5%BF%97" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">志</a>》、楚国之《<a href="https://baike.baidu.com/item/%E6%A2%BC%E6%9D%8C" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">梼杌</a>》、<a href="https://baike.baidu.com/item/%E9%B2%81%E5%9B%BD" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">鲁国</a>之《<a href="https://baike.baidu.com/item/%E6%98%A5%E7%A7%8B" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">春秋</a>》等，《<a href="https://baike.baidu.com/item/%E5%A2%A8%E5%AD%90" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">墨子</a>》里面说<a href="https://baike.baidu.com/item/%E5%A2%A8%E7%BF%9F" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">墨翟</a>曾见过&ldquo;百国春秋&rdquo;。</div>
<div class="anchor-list " style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-size: 14px; position: relative; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	&nbsp;</div>
<div class="para-title level-3  " data-index="1_2" data-pid="7" label-module="para-title" style="padding: 0px; margin: 20px 0px 12px; color: rgb(51, 51, 51); clear: both; zoom: 1; line-height: 20px; font-size: 18px; font-family: &quot;Microsoft YaHei&quot;, SimHei, Verdana; caret-color: rgb(51, 51, 51); text-size-adjust: auto;">
	<h3 class="title-text" style="padding: 0px; margin: 0px; font-size: 18px; font-weight: 400;">
		早期的史书</h3>
</div>
<div class="para" data-pid="8" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	都是以编年史的形式存在，<a href="https://baike.baidu.com/item/%E6%99%8B%E6%9C%9D" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">晋朝</a><a href="https://baike.baidu.com/item/%E5%A4%AA%E5%BA%B7" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">太康</a>年间汲冢出土的《<a href="https://baike.baidu.com/item/%E7%AB%B9%E4%B9%A6%E7%BA%AA%E5%B9%B4" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">竹书纪年</a>》也是编年体。东汉末年，<a href="https://baike.baidu.com/item/%E8%8D%80%E6%82%A6" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">荀悦</a>撰成《<a href="https://baike.baidu.com/item/%E6%B1%89%E7%BA%AA" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">汉纪</a>》，开创了编年体的<a href="https://baike.baidu.com/item/%E6%96%AD%E4%BB%A3%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">断代史</a>。北宋<a href="https://baike.baidu.com/item/%E5%8F%B8%E9%A9%AC%E5%85%89" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">司马光</a>撰《<a href="https://baike.baidu.com/item/%E8%B5%84%E6%B2%BB%E9%80%9A%E9%89%B4" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">资治通鉴</a>》，上起<a href="https://baike.baidu.com/item/%E5%91%A8%E5%A8%81%E7%83%88%E7%8E%8B" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">周威烈王</a>二十三年（前403年），下至五代<a href="https://baike.baidu.com/item/%E5%91%A8%E4%B8%96%E5%AE%97" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">周世宗</a>显德六年（959年），<a href="https://baike.baidu.com/item/%E7%BC%96%E5%B9%B4%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">编年体</a>的优点是方便考查历史事件发生的具体时间，了解历史事件之间的联系，还避免叙事重复，《资治通鉴》的成功开创了撰写编年史的高潮。<a href="https://baike.baidu.com/item/%E9%99%88%E5%AF%85%E6%81%AA" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">陈寅恪</a>曾说：&ldquo;中国史学莫盛于宋。&rdquo;。编年体盛行起来，从而产生了<a href="https://baike.baidu.com/item/%E7%BA%B2%E7%9B%AE%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">纲目体</a>与<a href="https://baike.baidu.com/item/%E7%BA%AA%E4%BA%8B%E6%9C%AC%E6%9C%AB%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">纪事本末体</a>，<a href="https://baike.baidu.com/item/%E6%A2%81%E5%90%AF%E8%B6%85" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">梁启超</a>以为：&ldquo;盖<a href="https://baike.baidu.com/item/%E7%BA%AA%E4%BC%A0%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">纪传体</a>以人为主，编年体以年为主，而纪事本末体以事为主。夫欲求史迹之原因结果以为鉴往知来之用，非以事为主不可。&rdquo;</div>
<div class="para" data-pid="9" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	<a href="https://baike.baidu.com/item/%E8%8C%83%E6%99%94" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">范晔</a>撰著《<a href="https://baike.baidu.com/item/%E5%90%8E%E6%B1%89%E4%B9%A6" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">后汉书</a>》时曾对纪传体和编年体进行过比较。他说：&ldquo;《<a href="https://baike.baidu.com/item/%E6%98%A5%E7%A7%8B" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">春秋</a>》者，文既总略，好失事形，今人拟作，所以为短；纪传体，<a href="https://baike.baidu.com/item/%E5%8F%B2%E7%8F%AD" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史班</a>之所变也，网罗一代，事义周悉，适之后学，此焉为优，故继而作之。&rdquo;<a href="https://baike.baidu.com/item/%E5%BC%A0%E8%BE%85" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">张辅</a>根据《<a href="https://baike.baidu.com/item/%E5%8F%B2%E8%AE%B0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史记</a>》、《汉书》字数多寡来评价作者，&ldquo;迁之著述，辞约而事举&rdquo;，所以认为《史记》优于《汉书》。干宝《晋纪》，&ldquo;其书简略，直而能婉，咸称良史。&rdquo;</div>
<div class="para" data-pid="10" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	<a href="https://baike.baidu.com/item/%E8%A2%81%E5%AE%8F" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">袁宏</a>谈及写作<a href="https://baike.baidu.com/item/%E5%8A%A8%E6%9C%BA" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">动机</a>说：&ldquo;予尝读后汉书，<a href="https://baike.baidu.com/item/%E7%83%A6%E7%A7%BD" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">烦秽</a>杂乱，睡而不能竟也，聊以暇日，撰集为《后<a href="https://baike.baidu.com/item/%E6%B1%89%E7%BA%AA" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">汉纪</a>》。&rdquo;<a href="https://baike.baidu.com/item/%E8%A2%81%E5%B1%B1%E6%9D%BE" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">袁山松</a>撰写《<a href="https://baike.baidu.com/item/%E5%90%8E%E6%B1%89%E4%B9%A6" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">后汉书</a>》，他总结说：&ldquo;书之为难也有五：烦而不整，一难也；俗而不典，二难也；书不实录，三难也;赏罚不中，四难也；文不胜质，五难也。&rdquo;</div>
<div class="para" data-pid="11" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	<a href="https://baike.baidu.com/item/%E6%9D%9C%E4%BD%91" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">杜佑</a>推出《<a href="https://baike.baidu.com/item/%E9%80%9A%E5%85%B8" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">通典</a>》后，史书增加了典制体，又衍生了会要体。</div>
<div class="para" data-pid="12" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	<a href="https://baike.baidu.com/item/%E7%AB%A0%E5%A4%AA%E7%82%8E" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">章太炎</a>在《中国<a href="https://baike.baidu.com/item/%E9%80%9A%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">通史</a>略例》主张以《表》、《典》、《记》、《考纪》、《<a href="https://baike.baidu.com/item/%E5%88%AB%E5%BD%95" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">别录</a>》等五种体例来撰写历史；<a href="https://baike.baidu.com/item/%E6%A2%81%E5%90%AF%E8%B6%85" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">梁启超</a>在《中国史叙论》<a href="https://baike.baidu.com/item/%E4%B8%BB%E5%BC%A0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">主张</a>以《年表》、《载记》、《志略》、《传志》四种体例撰写历史。</div>
<div class="para" data-pid="13" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	梁启超在《<a href="https://baike.baidu.com/item/%E6%96%B0%E5%8F%B2%E5%AD%A6" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">新史学</a>》将史籍划分为十种二十三类，即正史(官书、别史)、编年、<a href="https://baike.baidu.com/item/%E7%BA%AA%E4%BA%8B%E6%9C%AC%E6%9C%AB" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">纪事本末</a>(通体、<a href="https://baike.baidu.com/item/%E5%88%AB%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">别体</a>)、<a href="https://baike.baidu.com/item/%E6%94%BF%E4%B9%A6" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">政书</a>(通体、别体、<a href="https://baike.baidu.com/item/%E5%B0%8F%E7%BA%AA" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">小纪</a>)、杂史(综记、<a href="https://baike.baidu.com/item/%E7%90%90%E8%AE%B0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">琐记</a>、诏令奏议)、传记(通体、别体二)、地志(通体、别体)、学史、史学(理论、事论、杂论)、<a href="https://baike.baidu.com/item/%E9%99%84%E5%BA%B8" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">附庸</a>(考据、注释)等。<br style="padding: 0px; margin: 0px;" />
	<br style="padding: 0px; margin: 0px;" />
	<div class="para-title level-3  " data-index="2_1" data-pid="15" label-module="para-title" style="padding: 0px; margin: 20px 0px 12px; clear: both; zoom: 1; line-height: 20px; font-size: 18px; font-family: &quot;Microsoft YaHei&quot;, SimHei, Verdana; text-size-adjust: auto;">
		<h3 class="title-text" style="padding: 0px; margin: 0px; font-size: 18px; font-weight: 400;">
			按真实性分类</h3>
	</div>
	<div class="para" data-pid="16" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		<div class="lemma-picture J-lemma-picture text-pic layout-right" style="padding: 0px; margin: 0px 0px 3px 20px; border: 1px solid rgb(224, 224, 224); overflow: hidden; position: relative; float: right; clear: right; width: 220px;">
			<img alt="" src="http://127.0.0.1/v5.7http://www.dedecms.com/demoimg/uploads/allimg/210722/1413202115-0.jpg" style="padding: 0px; margin: 0px auto; border: 0px; color: rgb(19, 110, 194); display: block; width: 220px; height: 176.332px;" /></div>
		<a href="https://baike.baidu.com/item/%E6%AD%A3%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">正史</a>：以<a href="https://baike.baidu.com/item/%E7%BA%AA%E4%BC%A0%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">纪传体</a>、<a href="https://baike.baidu.com/item/%E7%BC%96%E5%B9%B4%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">编年体</a>的<a href="https://baike.baidu.com/item/%E4%BD%93%E4%BE%8B" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">体例</a>，记载帝王政绩、王朝历史，人物传纪和经济、军事、文化、地理等诸方面情况的史书叫正史。如，通常所说的<a href="https://baike.baidu.com/item/%E4%BA%8C%E5%8D%81%E5%9B%9B%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">二十四史</a>。除少数是个人著述(如<a href="https://baike.baidu.com/item/%E5%8F%B8%E9%A9%AC%E8%BF%81" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">司马迁</a>的《<a href="https://baike.baidu.com/item/%E5%8F%B2%E8%AE%B0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史记</a>》、<a href="https://baike.baidu.com/item/%E8%8C%83%E6%99%94" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">范晔</a>的《<a href="https://baike.baidu.com/item/%E5%90%8E%E6%B1%89%E4%B9%A6" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">后汉书</a>》、<a href="https://baike.baidu.com/item/%E9%99%88%E5%AF%BF" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">陈寿</a>的《<a href="https://baike.baidu.com/item/%E4%B8%89%E5%9B%BD%E5%BF%97" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">三国志</a>》、<a href="https://baike.baidu.com/item/%E6%AC%A7%E9%98%B3%E4%BF%AE" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">欧阳修</a>的《<a href="https://baike.baidu.com/item/%E6%96%B0%E4%BA%94%E4%BB%A3%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">新五代史</a>》)外，大部分正史是由<a href="https://baike.baidu.com/item/%E5%AE%98%E4%BF%AE" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">官修</a>的。</div>
	<div class="para" data-pid="17" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		<a href="https://baike.baidu.com/item/%E5%88%AB%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">别史</a>：主要指编年体、纪传体之外，杂记历代或一代史实的史书，如《东观<a href="https://baike.baidu.com/item/%E6%B1%89%E7%BA%AA" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">汉纪</a>》、《<a href="https://baike.baidu.com/item/%E4%B8%9C%E9%83%BD%E4%BA%8B%E7%95%A5" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">东都事略</a>》、《<a href="https://baike.baidu.com/item/%E5%A4%A7%E9%87%91%E5%9B%BD%E5%BF%97" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">大金国志</a>》以及《通志》等史书都属于别史。由此可见，别史实际上是正史类史籍的重要补充部分，犹正史之别支，所以《四库全书总目&middot;史部&middot;别史类叙》<a href="https://baike.baidu.com/item/%E4%B8%AD%E6%89%8D" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">中才</a>有&ldquo;犹大宗之有别支&rdquo;的说法。由著名学者创作的，有时与<a href="https://baike.baidu.com/item/%E6%9D%82%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">杂史</a>难以区分。如《<a href="https://baike.baidu.com/item/%E6%B1%89%E6%99%8B%E6%98%A5%E7%A7%8B" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">汉晋春秋</a>》。</div>
	<div class="para" data-pid="18" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		<span style="padding: 0px; margin: 0px; font-weight: 700;"><a href="https://baike.baidu.com/item/%E6%9D%82%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">杂史</a></span>：只记载一事之始末，一时之见闻或一家之私记，是带有掌故性的史书。它不同于纪、传、表、志等体例齐全的正史，也不同于关系一朝执政的别史。它不受体例限制，博录所闻，虽杂荒疏浅，却可弥补<a href="https://baike.baidu.com/item/%E5%AE%98%E4%BF%AE" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">官修</a>史书的疏漏与不足，包括家史，外史，小史，<a href="https://baike.baidu.com/item/%E7%A8%97%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">稗史</a>，<a href="https://baike.baidu.com/item/%E9%87%8E%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">野史</a>，逸史等类别。</div>
	<div class="para" data-pid="19" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		野史：有别于<a href="https://baike.baidu.com/item/%E5%AE%98%E6%92%B0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">官撰</a>正史的民间编写的史书。</div>
	<div class="para" data-pid="20" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		稗史：通常指记载闾巷风俗，民间琐事及旧闻之类的史籍，如清代人<a href="https://baike.baidu.com/item/%E6%BD%98%E6%B0%B8%E5%9B%A0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">潘永因</a>的《<a href="https://baike.baidu.com/item/%E5%AE%8B%E7%A8%97%E7%B1%BB%E9%92%9E" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">宋稗类钞</a>》，近代人<a href="https://baike.baidu.com/item/%E5%BE%90%E7%8F%82" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">徐珂</a>的《<a href="https://baike.baidu.com/item/%E6%B8%85%E7%A8%97%E7%B1%BB%E9%92%9E" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">清稗类钞</a>》。有时也泛指&ldquo;<span style="padding: 0px; margin: 0px; font-weight: 700;"><a href="https://baike.baidu.com/item/%E9%87%8E%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">野史</a></span>&rdquo;。</div>
	<div class="anchor-list " style="padding: 0px; margin: 0px; position: relative; font-size: 12px; text-size-adjust: auto;">
		&nbsp;</div>
	<div class="para-title level-3  " data-index="2_2" data-pid="21" label-module="para-title" style="padding: 0px; margin: 20px 0px 12px; clear: both; zoom: 1; line-height: 20px; font-size: 18px; font-family: &quot;Microsoft YaHei&quot;, SimHei, Verdana; text-size-adjust: auto;">
		<h3 class="title-text" style="padding: 0px; margin: 0px; font-size: 18px; font-weight: 400;">
			按体例分类</h3>
	</div>
	<div class="para" data-pid="22" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		<span style="padding: 0px; margin: 0px; font-weight: 700;"><a href="https://baike.baidu.com/item/%E7%BA%AA%E4%BC%A0%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">纪传体</a></span>：纪传体史书创始于西汉司马迁的《<a href="https://baike.baidu.com/item/%E5%8F%B2%E8%AE%B0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史记</a>》，它以人物传记为中心，用&ldquo;<a href="https://baike.baidu.com/item/%E6%9C%AC%E7%BA%AA" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">本纪</a>&rdquo;叙述帝王；用&ldquo;世家&rdquo;记叙王侯封国和特殊人物；用&ldquo;表&rdquo;统系年代、世系及人物；用&ldquo;书&rdquo;或&ldquo;志&rdquo;记载典章制度；用&ldquo;<a href="https://baike.baidu.com/item/%E5%88%97%E4%BC%A0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">列传</a>&rdquo;记人物、民族及外国。历代修正史都以此为典范。又如《汉书》。有个别的正史没有书或者志，比如《<a href="https://baike.baidu.com/item/%E4%B8%89%E5%9B%BD%E5%BF%97" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">三国志</a>》</div>
	<div class="para" data-pid="23" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		<span style="padding: 0px; margin: 0px; font-weight: 700;"><a href="https://baike.baidu.com/item/%E7%BC%96%E5%B9%B4%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">编年体</a></span>：编年体史书按年、月、日顺序编写，以年月为经，以事实为纬，比较容易反映出同一时期各个历史事件的联系。以编年体记录历史的方式最早起源于中国。如《<a href="https://baike.baidu.com/item/%E5%B7%A6%E4%BC%A0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">左传</a>》、《<a href="https://baike.baidu.com/item/%E8%B5%84%E6%B2%BB%E9%80%9A%E9%89%B4" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">资治通鉴</a>》等都属于这一类。《<a href="https://baike.baidu.com/item/%E6%98%A5%E7%A7%8B" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">春秋</a>》是我国现存最早的一部编年体史书。</div>
	<div class="para" data-pid="24" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		<span style="padding: 0px; margin: 0px; font-weight: 700;"><a href="https://baike.baidu.com/item/%E7%BA%AA%E4%BA%8B%E6%9C%AC%E6%9C%AB%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">纪事本末体</a></span>：创始于南宋<a href="https://baike.baidu.com/item/%E8%A2%81%E6%9E%A2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">袁枢</a>的《<a href="https://baike.baidu.com/item/%E9%80%9A%E9%89%B4%E7%BA%AA%E4%BA%8B%E6%9C%AC%E6%9C%AB" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">通鉴纪事本末</a>》。这种体裁的特点是以历史事件为纲，重要史事分别列目，独立成篇，各篇又按年、月、日顺序编写。现有九部纪事本末体的古籍。</div>
	<div class="para" data-pid="25" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		<span style="padding: 0px; margin: 0px; font-weight: 700;"><a href="https://baike.baidu.com/item/%E5%9B%BD%E5%88%AB%E4%BD%93" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">国别体</a></span>：国别体史书创始于《<a data-lemmaid="64453" href="https://baike.baidu.com/item/%E5%9B%BD%E8%AF%AD/64453" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">国语</a>》。国别体史书是一部分国记事的历史散文。分载多国历史。如《<a href="https://baike.baidu.com/item/%E6%88%98%E5%9B%BD%E7%AD%96" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">战国策</a>》都属于这一类。</div>
	<div class="para-title level-3  " data-index="2_3" data-pid="26" label-module="para-title" style="padding: 0px; margin: 20px 0px 12px; clear: both; zoom: 1; line-height: 20px; font-size: 18px; font-family: &quot;Microsoft YaHei&quot;, SimHei, Verdana; text-size-adjust: auto;">
		<h3 class="title-text" style="padding: 0px; margin: 0px; font-size: 18px; font-weight: 400;">
			按时空分类</h3>
	</div>
	<div class="para" data-pid="27" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		<a href="https://baike.baidu.com/item/%E9%80%9A%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">通史</a>：连贯地记叙各个时代的史实的史书称为通史，如西汉司马迁的《<a data-lemmaid="254522" href="https://baike.baidu.com/item/%E5%8F%B2%E8%AE%B0/254522" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史记</a>》。因为他记载了上<a href="https://baike.baidu.com/item/%E8%87%AA%E4%BC%A0%E8%AF%B4" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">自传说</a>中的<a href="https://baike.baidu.com/item/%E9%BB%84%E5%B8%9D" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">黄帝</a>，下至汉武帝时代，历时三千多年的史实。</div>
	<div class="para" data-pid="28" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		<a href="https://baike.baidu.com/item/%E6%96%AD%E4%BB%A3%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">断代史</a>：记载一朝一代历史的史书称为断代史，创始于东汉<a href="https://baike.baidu.com/item/%E7%8F%AD%E5%9B%BA" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">班固</a>的《<a href="https://baike.baidu.com/item/%E6%B1%89%E4%B9%A6" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">汉书</a>》。<a href="https://baike.baidu.com/item/%E4%BA%8C%E5%8D%81%E5%9B%9B%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">二十四史</a>中除《<a href="https://baike.baidu.com/item/%E5%8F%B2%E8%AE%B0" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史记</a>》外，其余都属断代史。</div>
	<div class="para-title level-3  " data-index="2_4" data-pid="29" label-module="para-title" style="padding: 0px; margin: 20px 0px 12px; clear: both; zoom: 1; line-height: 20px; font-size: 18px; font-family: &quot;Microsoft YaHei&quot;, SimHei, Verdana; text-size-adjust: auto;">
		<h3 class="title-text" style="padding: 0px; margin: 0px; font-size: 18px; font-weight: 400;">
			按学科分类</h3>
	</div>
	<div class="para" data-pid="30" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
		记载各种专门学科历史的史书称<span style="padding: 0px; margin: 0px; font-weight: 700;"><a href="https://baike.baidu.com/item/%E4%B8%93%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">专史</a></span>，如：<a href="https://baike.baidu.com/item/%E7%BB%8F%E6%B5%8E%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">经济史</a>、<a href="https://baike.baidu.com/item/%E6%80%9D%E6%83%B3%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">思想史</a>、<a data-lemmaid="2170680" href="https://baike.baidu.com/item/%E6%96%87%E5%AD%A6%E5%8F%B2/2170680" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">文学史</a>、<a href="https://baike.baidu.com/item/%E5%8F%B2%E5%AD%A6%E5%8F%B2" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">史学史</a>等。</div>
</div>
<br />

			</div>
			<div class="boxoff">
				<strong>------分隔线----------------------------</strong>
			</div>
			<div class="handle">
				<div class="context">
					<ul>
						<li>上一篇：<a href='/a/tushuguan/2021/0810/78.html'>《孟子》</a> </li>
						<li>下一篇：<a href='/a/tushuguan/2021/0810/85.html'>《论语》二</a> </li>
					</ul>
				</div><!-- /context -->
				<div class="actbox">
					<ul>
						<li id="act-fav"><a href="/plus/stow.php?aid=79" target="_blank">收藏</a></li>
						<li id="act-err"><a href="/plus/erraddsave.php?aid=79&title=《史书》" target="_blank">挑错</a></li>
						<li id="act-pus"><a href="/plus/recommend.php?aid=79" target="_blank">推荐</a></li>
						<li id="act-pnt"><a href="#" target="_blank" onClick="window.print();">打印</a></li>
					</ul>
				</div><!-- /actbox -->
			</div><!-- /handle -->
		</div><!-- viewbox -->

<!-- //AJAX评论区 -->
<!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="79" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=79">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=79&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '79');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->


	</div><!-- /pleft -->

	<div class="pright">
        <div>
          <dl class="tbox">
				<dt><strong>栏目列表</strong></dt>
				<dd>
					<ul class="d6">
                      
					</ul>
				</dd>
			</dl>
        </div>
        	<div class="infos_userinfo">
 			
   	 	</div>
         
		<div class="productrange mt1">
			<dl class="tbox">
				<dt><strong>推荐商品</strong></dt>
				<dd>
					<ul class="f1">
                    
					</ul>
				</dd>
			</dl>
		</div>
        
		<div class="comment mt1">
			<dl class="tbox">
				<dt><strong>热门商品</strong></dt>
				<dd>
					<ul class="e3">
                    <li>
							<a href="/a/tushuguan/2021/0810/76.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2592D.png" alt="《红楼梦》"/></a>
							<a href="/a/tushuguan/2021/0810/76.html" class="title">《红楼梦》</a>
							<span class="intro">人气:<span class="fc-f90">175</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/79.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I600456.png" alt="《史书》"/></a>
							<a href="/a/tushuguan/2021/0810/79.html" class="title">《史书》</a>
							<span class="intro">人气:<span class="fc-f90">167</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/77.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I5034S.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/77.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">165</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/75.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2253U.png" alt="《论语》"/></a>
							<a href="/a/tushuguan/2021/0810/75.html" class="title">《论语》</a>
							<span class="intro">人气:<span class="fc-f90">143</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/87.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I645232.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/87.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">123</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/85.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I61XP.png" alt="《论语》二"/></a>
							<a href="/a/tushuguan/2021/0810/85.html" class="title">《论语》二</a>
							<span class="intro">人气:<span class="fc-f90">117</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/78.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I524352.png" alt="《孟子》"/></a>
							<a href="/a/tushuguan/2021/0810/78.html" class="title">《孟子》</a>
							<span class="intro">人气:<span class="fc-f90">59</span></span>
						</li>


					</ul>
				</dd>
			</dl>
		</div>

	</div><!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->

</body>
</html>
