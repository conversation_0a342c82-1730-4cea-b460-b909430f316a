<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《孙子兵法》_万婕源码网</title>
<meta name="keywords" content="《,孙子兵法,》,《,孙子兵法,》,又称," />
<meta name="description" content="《孙子兵法》，又称《孙武兵法》《吴孙子兵法》《孙子兵书》《孙武兵书》等，是中国现存最早的兵书，也是世界上最早的军事著作，早于 克劳塞维茨 《 战争论 》约2300年，被誉为" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=87">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=87";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>
<script language="javascript" type="text/javascript">
<!--
	function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
	}
	function postBadGood(ftype,fid)
	{
		var taget_obj = document.getElementById(ftype+fid);
		var saveid = GetCookie('badgoodid');
		if(saveid != null)
		{
			 var saveids = saveid.split(',');
			 var hasid = false;
			 saveid = '';
			 j = 1;
			 for(i=saveids.length-1;i>=0;i--)
			 {
			 	  if(saveids[i]==fid && hasid) continue;
			 	  else {
			 	  	if(saveids[i]==fid && !hasid) hasid = true;
			 	  	saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
			 	  	j++;
			 	  	if(j==10 && hasid) break;
			 	  	if(j==9 && !hasid) break;
			 	  }
			 }
	     if(hasid) { alert('您刚才已表决过了喔！'); return false;}
	     else saveid += ','+fid;
			 SetCookie('badgoodid',saveid,1);
		}
		else
		{
			SetCookie('badgoodid',fid,1);
		}
		//document.write("feedback.php?action="+ftype+"&fid="+fid);
		//return;
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
	  DedeXHTTP = null;
	}
-->
</script>
</head>
<body class="productview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	<li class='hover'><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
			<strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/tushuguan/'>图书馆</a>
		</div><!-- /place -->
		<div class="viewbox">
			<div class="title">
				<h2>《孙子兵法》</h2>
			</div><!-- /title -->
			<div class="infolist">
                <small>信息类型：</small><span></span>
                <small>测试：</small><span></span>
				<small>商品原价：</small><span>160元</span>
				<small>优惠价格：</small><span class="fc-f60">70元</span>
				<small>品牌：</small><span>品牌</span>
                <small>单位：</small><span>本</span>
				<small>上架日期：</small><span>21-08-23 17:36</span>
				<small>人气：</small><span><script src="/plus/count.php?view=yes&aid=87&mid=1" type='text/javascript' language="javascript"></script></span>
                <form id="formcar" name="formcar" method="post" action="/plus/posttocar.php">
                <input type="hidden" name="id" value="87" />
                <input type="hidden" name="title" value="《孙子兵法》" />
                <input type="hidden" name="price" value="70" />
                <input type="hidden" name="units" value="本" />
                <small>购物车：</small><span><a href="/plus/car.php" target="_blank">查看购物车</a></span>
                <small>购买：</small><span><button type="submit" name="button" class="btn-2">放入购物车</button></span>
                </form>

			</div><!-- /info -->
			<div class="picview">
				<img src=http://www.dedecms.com/demoimg/uploads/210823/1-210R31I645232.png>
			</div><!-- /info -->
			<div class="labeltitle">
				<strong>商品介绍</strong>
			</div>
			<div class="content">
                 <div id="contentMidPicAD" style="float:right; clear:both; top:0; vertical-align:top;"></div>
			　　<div class="para" data-pid="3" label-module="para" style="padding: 0px; margin: 0px 0px 15px; color: rgb(51, 51, 51); font-size: 14px; overflow-wrap: break-word; text-indent: 28px; line-height: 24px; zoom: 1; caret-color: rgb(51, 51, 51); font-family: arial, 宋体, sans-serif; text-size-adjust: auto;">
	<div class="lemma-summary" label-module="lemmaSummary" style="padding: 0px; margin: 0px 0px 15px; clear: both; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1;">
		<div class="para" data-pid="1" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1;">
			<br />
			《孙子兵法》，又称《孙武兵法》《吴孙子兵法》《孙子兵书》《孙武兵书》等，是中国现存最早的兵书，也是世界上最早的军事著作，早于<a data-lemmaid="894202" href="https://baike.baidu.com/item/%E5%85%8B%E5%8A%B3%E5%A1%9E%E7%BB%B4%E8%8C%A8/894202" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">克劳塞维茨</a>《<a data-lemmaid="790408" href="https://baike.baidu.com/item/%E6%88%98%E4%BA%89%E8%AE%BA/790408" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">战争论</a>》约2300年，被誉为&ldquo;兵学圣典&rdquo;。现存共有六千字左右，一共十三篇。作者为<a data-lemmaid="982807" href="https://baike.baidu.com/item/%E6%98%A5%E7%A7%8B/982807" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">春秋</a>时祖籍齐国乐安的吴国将军<a data-lemmaid="18641" href="https://baike.baidu.com/item/%E5%AD%99%E6%AD%A6/18641" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孙武</a>。</div>
		<div class="para" data-pid="2" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1;">
			《孙子兵法》是中国古代军事文化遗产中的璀璨瑰宝，优秀传统文化的重要组成部分，其内容博大精深，思想精邃富赡，逻辑缜密严谨，是古代军事思想精华的集中体现。</div>
		<div class="para" data-pid="3" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1;">
			《孙子兵法》被奉为兵家经典。诞生已有2500年历史，历代都有研究。<a data-lemmaid="44058" href="https://baike.baidu.com/item/%E6%9D%8E%E4%B8%96%E6%B0%91/44058" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">李世民</a>说&ldquo;观诸兵书，无出孙武&rdquo;。兵法是谋略，谋略不是小花招，而是大战略、大智慧。如今，《孙子兵法》已经走向世界。它也被翻译成多种语言，在世界军事史上也具有重要的地位。汉代版《孙子兵法》竹简1972年出土于<a href="https://baike.baidu.com/item/%E4%B8%B4%E6%B2%82" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">临沂</a>银雀山汉墓中。<span class="sup--normal" data-ctrmap=":1," data-sup="1" style="padding: 0px 2px; margin: 0px 0px 0px 2px; font-size: 10.5882px; line-height: 0; position: relative; vertical-align: baseline; top: -0.5em; white-space: nowrap; color: rgb(51, 102, 204); cursor: pointer;"><span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span>[1]</span><a class="sup-anchor" name="ref_[1]_4911891" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); position: relative; top: -50px; font-size: 0px; line-height: 0;">&nbsp;</a></div>
		<div style="padding: 0px; margin: 0px;">
			&nbsp;</div>
	</div>
	<div class="lemmaWgt-promotion-leadPVBtn" style="padding: 0px; margin: 0px;">
		&nbsp;</div>
	<div class="configModuleBanner" style="padding: 0px; margin: 0px;">
		<div class="para" data-pid="5" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
			《孙子兵法》又称《吴孙子兵法》《孙子》《孙武兵法》，由孙武撰。<a data-lemmaid="18641" href="https://baike.baidu.com/item/%E5%AD%99%E6%AD%A6/18641" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">孙武</a>，字长卿，春秋末期齐国人，从齐国流亡到吴国，辅助吴王经国治军，显名诸侯，被尊为&ldquo;<a data-lemmaid="74531" href="https://baike.baidu.com/item/%E5%85%B5%E5%9C%A3/74531" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">兵圣</a>&rdquo;。《孙子兵法》被誉为&ldquo;兵学圣典&rdquo;和&ldquo;古代第一兵书&rdquo;。它在我国古代军事学术和战争实践中，都起过极其重要的指导作用。</div>
		<div class="para" data-pid="6" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
			《孙子兵法》是现存最早的兵书，历来备受推崇，研习者辈出。凡十三篇，每篇皆以&ldquo;孙子曰&rdquo;开头，按专题论说，有中心，有层次，逻辑严谨，语言简练，文风质朴，善用排比铺陈叙说，比喻生动具体，如写军队的行动：&ldquo;其疾如风，其徐如林，侵掠如火，不动如山，难知如阴，动如雷震&rdquo;（《军争篇》），既贴切又形象，且音韵铿锵，气势不凡，故<a data-lemmaid="197270" href="https://baike.baidu.com/item/%E5%88%98%E5%8B%B0/197270" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">刘勰</a>称&ldquo;孙武兵经，辞如珠玉&rdquo;（《文心雕龙&middot;程器》）。想来以作战的缜密思维为文章谋篇布局，对孙武而言如烹小鲜矣。</div>
		<div class="para" data-pid="7" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
			<div class="lemma-picture J-lemma-picture text-pic layout-right" style="padding: 0px; margin: 0px 0px 3px 20px; border: 1px solid rgb(224, 224, 224); overflow: hidden; position: relative; float: right; clear: right; width: 220px;">
				<font color="#136ec2" style="padding: 0px; margin: 0px;"><span style="padding: 0px; margin: 0px auto; height: 238.91px; border-style: initial; border-color: initial; border-image: initial;"><img alt="《孙子兵法》作者：孙武画像" src="http://127.0.0.1/v5.7http://www.dedecms.com/demoimg/uploads/allimg/210722/14014514D-0.jpg" style="padding: 0px; margin: 0px auto; border: 0px; display: block; width: 220px; height: 238.91px;" /></span></font><span class="description" style="padding: 8px 7px; margin: 0px; display: block; color: rgb(85, 85, 85); font-size: 12px; text-indent: 0px; font-family: 宋体; overflow-wrap: break-word; word-break: break-all; line-height: 24px; min-height: 12px; border-top: 1px solid rgb(224, 224, 224);">《孙子兵法》作者：孙武画像</span></div>
			&ldquo;兵者，国之大事，死生之地，存亡之道，不可不察也。&rdquo;《孙子兵法》继承和发展了前人的军事理论 ，把政治作为决定战争胜败的首要因素，归纳出战争的原理原则，举凡战前之准备，策略之运用，作战之布署，敌情之研判等，无不详加说明，巨细靡遗，周严完备，具有朴素的唯物辩证思想，二千多年来一直被视为兵家之经典，至今仍具有重大的现实意义。<a data-lemmaid="113835" href="https://baike.baidu.com/item/%E6%AF%9B%E6%B3%BD%E4%B8%9C/113835" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">毛泽东</a>对《孙子兵法》推崇备至，而孙子所主张的智、信、仁、勇、严则成为中国军人的&ldquo;武德&rdquo;。</div>
		<div class="para" data-pid="8" label-module="para" style="padding: 0px; margin: 0px 0px 15px; overflow-wrap: break-word; text-indent: 2em; line-height: 24px; zoom: 1; text-size-adjust: auto;">
			该书迄今最早的传世本为1972年山东<a data-lemmaid="5585707" href="https://baike.baidu.com/item/%E9%93%B6%E9%9B%80%E5%B1%B1/5585707" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">银雀山</a>出土的汉墓竹书《孙子兵法》，惜为残简，不能窥其全貌，经汉简专家整理小组整理，于1975年由文物出版社出版。现存最早的刻本为南宋孝宗、光宗年间的《十一家注孙子》本；又有《武经七书》本《孙子》，1935年中华学艺社影印。其注本以<a data-lemmaid="6772" href="https://baike.baidu.com/item/%E6%9B%B9%E6%93%8D/6772" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); text-decoration-line: none;" target="_blank">曹操</a>注最早，《平津馆丛书》所收影宋本《孙吴司马法》中有《魏武帝孙子》。此后注家颇多，如清孙星衍的《孙子十家注》、朱墉的《武经七书&middot;汇解》、夏振翼的《武经体注大全会解&middot;孙子》，近人杨炳安的《孙子会笺》，今人吴九龙的《孙子校释》、吴如嵩的《孙子兵法新论》等。<span class="sup--normal" data-ctrmap=":2," data-sup="2" style="padding: 0px 2px; margin: 0px 0px 0px 2px; font-size: 10.5882px; line-height: 0; position: relative; vertical-align: baseline; top: -0.5em; white-space: nowrap; color: rgb(51, 102, 204); cursor: pointer;"><span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span>[2]</span><a class="sup-anchor" name="ref_[2]_4911891" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); position: relative; top: -50px; font-size: 0px; line-height: 0;">&nbsp;</a></div>
	</div>
	<div class="basic-info J-basic-info cmn-clearfix" data-pid="card" style="padding: 0px; margin: 20px 0px 35px; clear: both; background-image: url(&quot;https://bkssl.bdimg.com/static/wiki-lemma/widget/lemma_content/mainContent/basicInfo/img/basicInfo-bg_ccaff81.png&quot;);">
		<dl class="basicInfo-block basicInfo-left" style="padding-right: 0px; padding-left: 0px; margin: 0px; width: 395px; float: left; font-size: 12px; text-size-adjust: auto;">
		</dl>
	</div>
	<a class="sup-anchor" name="ref_[1]_4911891" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); position: relative; top: -50px; font-size: 0px; line-height: 0;">&nbsp;</a></div>
<div>
	<a class="sup-anchor" name="ref_[1]_4911891" style="padding: 0px; margin: 0px; color: rgb(19, 110, 194); position: relative; top: -50px; font-size: 0px; line-height: 0;"></a></div>

			</div>
			<div class="boxoff">
				<strong>------分隔线----------------------------</strong>
			</div>
			<div class="handle">
				<div class="context">
					<ul>
						<li>上一篇：<a href='/a/tushuguan/2021/0810/85.html'>《论语》二</a> </li>
						<li>下一篇：没有了 </li>
					</ul>
				</div><!-- /context -->
				<div class="actbox">
					<ul>
						<li id="act-fav"><a href="/plus/stow.php?aid=87" target="_blank">收藏</a></li>
						<li id="act-err"><a href="/plus/erraddsave.php?aid=87&title=《孙子兵法》" target="_blank">挑错</a></li>
						<li id="act-pus"><a href="/plus/recommend.php?aid=87" target="_blank">推荐</a></li>
						<li id="act-pnt"><a href="#" target="_blank" onClick="window.print();">打印</a></li>
					</ul>
				</div><!-- /actbox -->
			</div><!-- /handle -->
		</div><!-- viewbox -->

<!-- //AJAX评论区 -->
<!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="87" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=87">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=87&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '87');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->


	</div><!-- /pleft -->

	<div class="pright">
        <div>
          <dl class="tbox">
				<dt><strong>栏目列表</strong></dt>
				<dd>
					<ul class="d6">
                      
					</ul>
				</dd>
			</dl>
        </div>
        	<div class="infos_userinfo">
 			
   	 	</div>
         
		<div class="productrange mt1">
			<dl class="tbox">
				<dt><strong>推荐商品</strong></dt>
				<dd>
					<ul class="f1">
                    
					</ul>
				</dd>
			</dl>
		</div>
        
		<div class="comment mt1">
			<dl class="tbox">
				<dt><strong>热门商品</strong></dt>
				<dd>
					<ul class="e3">
                    <li>
							<a href="/a/tushuguan/2021/0810/76.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2592D.png" alt="《红楼梦》"/></a>
							<a href="/a/tushuguan/2021/0810/76.html" class="title">《红楼梦》</a>
							<span class="intro">人气:<span class="fc-f90">175</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/79.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I600456.png" alt="《史书》"/></a>
							<a href="/a/tushuguan/2021/0810/79.html" class="title">《史书》</a>
							<span class="intro">人气:<span class="fc-f90">167</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/77.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I5034S.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/77.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">165</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/75.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2253U.png" alt="《论语》"/></a>
							<a href="/a/tushuguan/2021/0810/75.html" class="title">《论语》</a>
							<span class="intro">人气:<span class="fc-f90">143</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/87.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I645232.png" alt="《孙子兵法》"/></a>
							<a href="/a/tushuguan/2021/0810/87.html" class="title">《孙子兵法》</a>
							<span class="intro">人气:<span class="fc-f90">123</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/85.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I61XP.png" alt="《论语》二"/></a>
							<a href="/a/tushuguan/2021/0810/85.html" class="title">《论语》二</a>
							<span class="intro">人气:<span class="fc-f90">117</span></span>
						</li>
<li>
							<a href="/a/tushuguan/2021/0810/78.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I524352.png" alt="《孟子》"/></a>
							<a href="/a/tushuguan/2021/0810/78.html" class="title">《孟子》</a>
							<span class="intro">人气:<span class="fc-f90">59</span></span>
						</li>


					</ul>
				</dd>
			</dl>
		</div>

	</div><!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->

</body>
</html>
