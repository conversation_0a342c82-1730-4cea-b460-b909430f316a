无法在这个位置找到： head_new.htm

    <!-- 面包屑导航 -->
    <section class="breadcrumb-section">
        <div class="container">
            <div class="breadcrumb">
                <a href="http://103.67.52.227:97">首页</a>
                <span class="separator">/</span>
                <a href='http://103.67.52.227:97/'>主页</a> > <a href='/a/tushuguan/'>图书馆</a>
            </div>
        </div>
    </section>

    <!-- 分类页面主要内容 -->
    <main class="main-content">
        <div class="container">
            <div class="content-wrapper">
                <!-- 左侧内容 -->
                <div class="main-column">
                    <!-- 分类标题和描述 -->
                    <section class="category-header">
                        <div class="category-info">
                            <h1>图书馆</h1>
                            <p class="category-desc"></p>
                            <div class="category-stats">
                                <span class="stat-item">
                                    <i class="fas fa-file-alt"></i>
                                    共 <div class='col1'>
    <div class='mainlinklist'>·<a href='/a/tushuguan/2021/0810/76.html'>《红楼梦》</a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 商品介绍 《红楼梦》，中国古代 章回体 长篇小说 ，中国古典 四大名著 之一，一般认为是清代作家 曹雪芹 所著。小说以贾、史、王、薛 四大家族 的兴衰为背景，以富贵公子 贾宝玉...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:38:02 点击：175 好评度：0
      <a href="/plus/feedback.php?aid=76"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div><div class='col1'>
    <div class='mainlinklist'>·<a href='/a/tushuguan/2021/0810/87.html'>《孙子兵法》</a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 《孙子兵法》，又称《孙武兵法》《吴孙子兵法》《孙子兵书》《孙武兵书》等，是中国现存最早的兵书，也是世界上最早的军事著作，早于 克劳塞维茨 《 战争论 》约2300年，被誉为...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:36:34 点击：123 好评度：0
      <a href="/plus/feedback.php?aid=87"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div><div class='col1'>
    <div class='mainlinklist'>·<a href='/a/tushuguan/2021/0810/85.html'>《论语》二</a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 《论语》是 儒家经典 之一，是一部以记言为主的语录体散文集，主要以语录和对话文体的形式记录了孔子及其弟子的言行，集中体现了孔子的政治、审美、道德伦理和功利等价值思想...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:36:26 点击：117 好评度：0
      <a href="/plus/feedback.php?aid=85"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div><div class='col1'>
    <div class='mainlinklist'>·<a href='/a/tushuguan/2021/0810/79.html'>《史书》</a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 史书发展 中国 西周 末年各诸侯国已有历 史记 载，如 晋国 之《 乘 》、郑国之《 志 》、楚国之《 梼杌 》、 鲁国 之《 春秋 》等，《 墨子 》里面说 墨翟 曾见过百国春秋。 早期的史...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:35:47 点击：167 好评度：0
      <a href="/plus/feedback.php?aid=79"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div><div class='col1'>
    <div class='mainlinklist'>·<a href='/a/tushuguan/2021/0810/78.html'>《孟子》</a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 作者简介 孟子（约公元前372年-公元前289年），名轲，字不详(子舆、子居等字表皆出自伪书，或后人杜撰)，战国中期 鲁国 邹人（今山东 邹城 人），距离 孔子 的故乡曲阜不远。 孟子...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:35:14 点击：59 好评度：0
      <a href="/plus/feedback.php?aid=78"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div><div class='col1'>
    <div class='mainlinklist'>·<a href='/a/tushuguan/2021/0810/77.html'>《孙子兵法》</a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 《孙子兵法》，又称《孙武兵法》《吴孙子兵法》《孙子兵书》《孙武兵书》等，是中国现存最早的兵书，也是世界上最早的军事著作，早于 克劳塞维茨 《 战争论 》约2300年，被誉为...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:34:43 点击：165 好评度：0
      <a href="/plus/feedback.php?aid=77"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div><div class='col1'>
    <div class='mainlinklist'>·<a href='/a/tushuguan/2021/0810/75.html'>《论语》</a></div>
    <div class='descriptions'>
      	&nbsp;&nbsp;&nbsp; 《论语》是 儒家经典 之一，是一部以记言为主的语录体散文集，主要以语录和对话文体的形式记录了孔子及其弟子的言行，集中体现了孔子的政治、审美、道德伦理和功利等价值思想...
    </div>
    <div class='addinfos'>
      日期：2021-08-23 17:32:14 点击：143 好评度：0
      <a href="/plus/feedback.php?aid=75"><img src="/images/comment.gif" width="12" height="12" border="0" title="前往踩踩"></a>
    </div>
</div> 篇文章
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-eye"></i>
                                    总浏览量 
                                </span>
                            </div>
                        </div>
                    </section>

                    <!-- 筛选和排序 -->
                    <section class="filter-section">
                        <div class="filter-controls">
                            <div class="filter-tabs">
                                <a href="?orderby=default" class="filter-tab active">默认排序</a>
                                <a href="?orderby=pubdate" class="filter-tab">最新发布</a>
                                <a href="?orderby=click" class="filter-tab">最多浏览</a>
                                <a href="?orderby=scores" class="filter-tab">最高评分</a>
                            </div>
                            <div class="view-mode">
                                <button class="view-btn active" data-view="grid">
                                    <i class="fas fa-th"></i>
                                </button>
                                <button class="view-btn" data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                            </div>
                        </div>
                    </section>

                    <!-- 资源列表 -->
                    <section class="resources-list">
                        <div class="resource-grid" id="resourceGrid">
                            <div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2592D.png" alt="《红楼梦》" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    p">《红楼梦》</a></h3>
                                    <p class="card-desc">商品介绍 《红楼梦》，中国古代 章回体 长篇小说 ，中国古典 四大名著 之一，</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            175
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            175
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/tushuguan/2021/0810/76.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(76)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div><div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I645232.png" alt="《孙子兵法》" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    p">《孙子兵法》</a></h3>
                                    <p class="card-desc">《孙子兵法》，又称《孙武兵法》《吴孙子兵法》《孙子兵书》《孙武兵书》等</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            123
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            123
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/tushuguan/2021/0810/87.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(87)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div><div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I61XP.png" alt="《论语》二" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    p">《论语》二</a></h3>
                                    <p class="card-desc">《论语》是 儒家经典 之一，是一部以记言为主的语录体散文集，主要以语录和</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            117
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            117
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/tushuguan/2021/0810/85.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(85)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div><div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I600456.png" alt="《史书》" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    p">《史书》</a></h3>
                                    <p class="card-desc">史书发展 中国 西周 末年各诸侯国已有历 史记 载，如 晋国 之《 乘 》、郑国之</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            167
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            167
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/tushuguan/2021/0810/79.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(79)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div><div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I524352.png" alt="《孟子》" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    p">《孟子》</a></h3>
                                    <p class="card-desc">作者简介 孟子（约公元前372年-公元前289年），名轲，字不详(子舆、子居等字表</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            59
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            59
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/tushuguan/2021/0810/78.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(78)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div><div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I5034S.png" alt="《孙子兵法》" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    p">《孙子兵法》</a></h3>
                                    <p class="card-desc">《孙子兵法》，又称《孙武兵法》《吴孙子兵法》《孙子兵书》《孙武兵书》等</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            165
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            165
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/tushuguan/2021/0810/77.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(77)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div><div class="resource-card">
                                <div class="card-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2253U.png" alt="《论语》" loading="lazy">
                                    <div class="card-category">{dede:field.typename/}</div>
                                    {dede:field.flag function="GetFlag(@me,'h')" runphp="yes"}
                                    p">《论语》</a></h3>
                                    <p class="card-desc">《论语》是 儒家经典 之一，是一部以记言为主的语录体散文集，主要以语录和</p>
                                    <div class="card-meta">
                                        <span class="time">
                                            <i class="fas fa-clock"></i>
                                            2021-08-23
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            143
                                        </span>
                                        <span class="comments">
                                            <i class="fas fa-comment"></i>
                                            0
                                        </span>
                                        <span class="downloads">
                                            <i class="fas fa-download"></i>
                                            143
                                        </span>
                                        <span class="price">300</span>
                                    </div>
                                    <div class="card-actions">
                                        <a href="/a/tushuguan/2021/0810/75.html" class="btn-primary">查看详情</a>
                                        <button class="btn-secondary" onclick="addToFavorites(75)">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 列表视图 -->
                        <div class="resource-list-view" id="resourceList" style="display: none;">
                            <div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2592D.png" alt="《红楼梦》" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/tushuguan/2021/0810/76.html">《红楼梦》</a></h3>
                                    <p class="item-desc">商品介绍 《红楼梦》，中国古代 章回体 长篇小说 ，中国古典 四大名著 之一，一般认为是清代作家 曹雪芹 所著。小说以贾、史、王、薛 四大家族 的兴衰</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">175 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/tushuguan/2021/0810/76.html" class="btn-primary">立即下载</a>
                                </div>
                            </div><div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I645232.png" alt="《孙子兵法》" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/tushuguan/2021/0810/87.html">《孙子兵法》</a></h3>
                                    <p class="item-desc">《孙子兵法》，又称《孙武兵法》《吴孙子兵法》《孙子兵书》《孙武兵书》等，是中国现存最早的兵书，也是世界上最早的军事著作，早于 克劳塞维茨</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">123 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/tushuguan/2021/0810/87.html" class="btn-primary">立即下载</a>
                                </div>
                            </div><div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I61XP.png" alt="《论语》二" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/tushuguan/2021/0810/85.html">《论语》二</a></h3>
                                    <p class="item-desc">《论语》是 儒家经典 之一，是一部以记言为主的语录体散文集，主要以语录和对话文体的形式记录了孔子及其弟子的言行，集中体现了孔子的政治、审美</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">117 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/tushuguan/2021/0810/85.html" class="btn-primary">立即下载</a>
                                </div>
                            </div><div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I600456.png" alt="《史书》" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/tushuguan/2021/0810/79.html">《史书》</a></h3>
                                    <p class="item-desc">史书发展 中国 西周 末年各诸侯国已有历 史记 载，如 晋国 之《 乘 》、郑国之《 志 》、楚国之《 梼杌 》、 鲁国 之《 春秋 》等，《 墨子 》里面说 墨</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">167 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/tushuguan/2021/0810/79.html" class="btn-primary">立即下载</a>
                                </div>
                            </div><div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I524352.png" alt="《孟子》" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/tushuguan/2021/0810/78.html">《孟子》</a></h3>
                                    <p class="item-desc">作者简介 孟子（约公元前372年-公元前289年），名轲，字不详(子舆、子居等字表皆出自伪书，或后人杜撰)，战国中期 鲁国 邹人（今山东 邹城 人），距离</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">59 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/tushuguan/2021/0810/78.html" class="btn-primary">立即下载</a>
                                </div>
                            </div><div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I5034S.png" alt="《孙子兵法》" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/tushuguan/2021/0810/77.html">《孙子兵法》</a></h3>
                                    <p class="item-desc">《孙子兵法》，又称《孙武兵法》《吴孙子兵法》《孙子兵书》《孙武兵书》等，是中国现存最早的兵书，也是世界上最早的军事著作，早于 克劳塞维茨</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">165 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/tushuguan/2021/0810/77.html" class="btn-primary">立即下载</a>
                                </div>
                            </div><div class="list-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2253U.png" alt="《论语》" loading="lazy">
                                </div>
                                <div class="item-content">
                                    <h3><a href="/a/tushuguan/2021/0810/75.html">《论语》</a></h3>
                                    <p class="item-desc">《论语》是 儒家经典 之一，是一部以记言为主的语录体散文集，主要以语录和对话文体的形式记录了孔子及其弟子的言行，集中体现了孔子的政治、审美</p>
                                    <div class="item-meta">
                                        <span class="category">{dede:field.typename/}</span>
                                        <span class="time">2021-08-23</span>
                                        <span class="views">143 浏览</span>
                                        <span class="price">300 积分</span>
                                    </div>
                                </div>
                                <div class="item-actions">
                                    <a href="/a/tushuguan/2021/0810/75.html" class="btn-primary">立即下载</a>
                                </div>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <div class="pagination-wrapper">
                            <li><span class="pageinfo">共 <strong>1</strong>页<strong>7</strong>条记录</span></li>

                        </div>
                    </section>
                </div>

                <!-- 右侧边栏 -->
                <aside class="sidebar">
                    <!-- 热门资源 -->
                    <section class="sidebar-section">
                        <h3>本分类热门</h3>
                        <div class="sidebar-list-simple">
                            <div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/tushuguan/2021/0810/76.html">《红楼梦》</a>
                                <span class="item-count">175</span>
                            </div>
<div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/tushuguan/2021/0810/79.html">《史书》</a>
                                <span class="item-count">167</span>
                            </div>
<div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/tushuguan/2021/0810/77.html">《孙子兵法》</a>
                                <span class="item-count">165</span>
                            </div>
<div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/tushuguan/2021/0810/75.html">《论语》</a>
                                <span class="item-count">143</span>
                            </div>
<div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/tushuguan/2021/0810/87.html">《孙子兵法》</a>
                                <span class="item-count">123</span>
                            </div>
<div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/tushuguan/2021/0810/85.html">《论语》二</a>
                                <span class="item-count">117</span>
                            </div>
<div class="simple-item">
                                <span class="item-number">{dede:global.autoindex/}</span>
                                <a href="/a/tushuguan/2021/0810/78.html">《孟子》</a>
                                <span class="item-count">59</span>
                            </div>

                        </div>
                    </section>

                    <!-- 最新更新 -->
                    <section class="sidebar-section">
                        <h3>最新更新</h3>
                        <div class="sidebar-list">
                            <div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I2592D.png" alt="《红楼梦》">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/tushuguan/2021/0810/76.html">《红楼梦》</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">175</span>
                                    </div>
                                </div>
                            </div>
<div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I645232.png" alt="《孙子兵法》">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/tushuguan/2021/0810/87.html">《孙子兵法》</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">123</span>
                                    </div>
                                </div>
                            </div>
<div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I61XP.png" alt="《论语》二">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/tushuguan/2021/0810/85.html">《论语》二</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">117</span>
                                    </div>
                                </div>
                            </div>
<div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I600456.png" alt="《史书》">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/tushuguan/2021/0810/79.html">《史书》</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">167</span>
                                    </div>
                                </div>
                            </div>
<div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I524352.png" alt="《孟子》">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/tushuguan/2021/0810/78.html">《孟子》</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">59</span>
                                    </div>
                                </div>
                            </div>
<div class="sidebar-item">
                                <div class="item-image">
                                    <img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31I5034S.png" alt="《孙子兵法》">
                                </div>
                                <div class="item-content">
                                    <h4><a href="/a/tushuguan/2021/0810/77.html">《孙子兵法》</a></h4>
                                    <div class="item-meta">
                                        <span class="time">08-23</span>
                                        <span class="views">165</span>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </section>

                    <!-- 相关分类 -->
                    <section class="sidebar-section">
                        <h3>相关分类</h3>
                        <div class="category-links">
                            
                        </div>
                    </section>

                    <!-- 标签云 -->
                    <section class="sidebar-section">
                        <h3>热门标签</h3>
                        <div class="tag-cloud">
                            
                        </div>
                    </section>

                    <!-- 广告位 -->
                    <section class="sidebar-section ad-section">
                        <div class="ad-banner">
                            <img src="/templets/default/images/ad-banner.jpg" alt="广告">
                        </div>
                    </section>
                </aside>
            </div>
        </div>
    </main>

无法在这个位置找到： footer_new.htm

<script>
// 视图切换功能
document.addEventListener('DOMContentLoaded', function() {
    const viewBtns = document.querySelectorAll('.view-btn');
    const gridView = document.getElementById('resourceGrid');
    const listView = document.getElementById('resourceList');
    
    viewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // 移除所有active类
            viewBtns.forEach(b => b.classList.remove('active'));
            
            // 添加active类到当前按钮
            this.classList.add('active');
            
            // 切换视图
            const viewMode = this.dataset.view;
            if (viewMode === 'grid') {
                gridView.style.display = 'grid';
                listView.style.display = 'none';
            } else {
                gridView.style.display = 'none';
                listView.style.display = 'block';
            }
            
            // 保存用户偏好
            localStorage.setItem('viewMode', viewMode);
        });
    });
    
    // 加载用户偏好的视图模式
    const savedViewMode = localStorage.getItem('viewMode');
    if (savedViewMode) {
        const targetBtn = document.querySelector(`[data-view="${savedViewMode}"]`);
        if (targetBtn) {
            targetBtn.click();
        }
    }
});

// 添加到收藏夹
function addToFavorites(articleId) {
    // 这里可以添加AJAX请求来处理收藏功能
    console.log('添加到收藏夹:', articleId);
    
    // 显示提示信息
    showNotification('已添加到收藏夹', 'success');
}

// 筛选功能
document.querySelectorAll('.filter-tab').forEach(tab => {
    tab.addEventListener('click', function(e) {
        e.preventDefault();
        
        // 移除所有active类
        document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
        
        // 添加active类到当前标签
        this.classList.add('active');
        
        // 获取排序参数
        const url = new URL(this.href);
        const orderby = url.searchParams.get('orderby');
        
        // 重新加载页面或使用AJAX加载内容
        window.location.href = this.href;
    });
});
</script>
