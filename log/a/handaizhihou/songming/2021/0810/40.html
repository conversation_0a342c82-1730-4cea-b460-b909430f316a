<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《孟子集注》· 孟子序说_万婕源码网</title>
<meta name="keywords" content="《,孟子集注,》,孟子,序,说,史记,列传,曰," />
<meta name="description" content="史记列传曰：孟轲，赵氏曰：孟子，鲁公族孟孙之后。汉书注云：字子车。一说：字子舆。驺人也，驺亦作邹，本邾国也。受业子思之门人。子思，孔子之孙，名伋。索隐云：王劭以人" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=40">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=40";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li class='hover'><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/handaizhihou/'>汉代之后</a> > <a href='/a/handaizhihou/songming/'>宋明</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《孟子集注》· 孟子序说</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:15<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=40&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">史记列传曰：孟轲，赵氏曰：孟子，鲁公族孟孙之后。汉书注云：字子车。一说：字子舆。驺人也，驺亦作邹，本邾国也。受业子思之门人。子思，孔子之孙，名伋。索隐云：王劭以人</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <span style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 16px;">史记列传曰：&ldquo;孟轲，赵氏曰：&ldquo;孟子，鲁公族孟孙之后。&rdquo;汉书注云：&ldquo;字子车。&rdquo;一说：&ldquo;字子舆。&rdquo;驺人也，驺亦作邹，本邾国也。受业子思之门人。子思，孔子之孙，名伋。索隐云：&ldquo;王劭以人为衍字。&rdquo;而赵氏注及孔丛子等书亦皆云：&ldquo;孟子亲受业于子思。&rdquo;未知是否？道既通，赵氏曰：&ldquo;孟子通五经，尤长于诗书。&rdquo;程子曰：&ldquo;孟子曰：&lsquo;可以仕则仕，可以止则止，可以久则久，可以速则速。&rsquo;&lsquo;孔子圣之时者也。&rsquo;故知易者莫如孟子。又曰：&lsquo;王者之迹熄而诗亡，诗亡然后春秋作。&rsquo;又曰：&lsquo;春秋无义战。&rsquo;又曰：&lsquo;春秋天子之事&rsquo;，故知春秋者莫如孟子。&rdquo;尹氏曰：&ldquo;以此而言，则赵氏谓孟子长于诗书而已，岂知孟子者哉？&rdquo;游事齐宣王，宣王不能用。适梁，梁惠王不果所言，则见以为迂远而阔于事情。按史记：&ldquo;梁惠王之三十五年乙酉，孟子始至梁。其后二十三年，当齐泯王之十年丁未，齐人伐燕，而孟子在齐。&rdquo;故古史谓&ldquo;孟子先事齐宣王后乃见梁惠王、襄王、齐泯王。&rdquo;独孟子以伐燕为宣王时事，与史记、荀子等书皆不合。而通鉴以伐燕之岁，为宣王十九年，则是孟子先游梁而后至齐见宣王矣。然考异亦无他据，又未知孰是也。当是之时，秦用商鞅，楚魏用吴起，齐用孙子、田忌。天下方务于合从连衡，以攻伐为贤。而孟轲乃述唐、虞、三代之德，是以所如者不合。退而与万章之徒序诗书，述仲尼之意，作孟子七篇。&rdquo;赵氏曰：&ldquo;凡二百六十一章，三万四千六百八十五字。&rdquo;韩子曰：&ldquo;孟轲之书，非轲自著。轲既没，其徒万章、公孙丑相与记轲所言焉耳。&rdquo;愚按：二说不同，史记近是。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
韩子曰：&ldquo;尧以是传之舜，舜以是传之禹，禹以是传之汤，汤以是传之文、武、周公，文、武、周公传之孔子，孔子传之孟轲，轲之死不得其传焉。荀与扬也，择焉而不精，语焉而不详。&rdquo;程子曰&ldquo;韩子此语，非是蹈袭前人，又非凿空撰得出，必有所见。若无所见，不知言所传者何事。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
又曰：&ldquo;孟氏醇乎醇者也。荀与扬，大醇而小疵。&rdquo;程子曰&ldquo;韩子论孟子甚善。非见得孟子意，亦道不到。其论荀扬则非也。荀子极偏驳，只一句性恶，大本已失。扬子虽少过，然亦不识性，更说甚道。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
又曰：&ldquo;孔子之道大而能博，门弟子不能遍观而尽识也，故学焉而皆得其性之所近。其后离散，分处诸侯之国，又各以其所能授弟子，源远而末益分。惟孟轲师子思，而子思之学出于曾子。自孔子没，独孟轲氏之传得其宗。故求观圣人之道者，必自孟子始。&rdquo;程子曰：&ldquo;孔子言参也鲁。然颜子没后，终得圣人之道者，曾子也。观其启手足时之言，可以见矣。所传者子思、孟子，皆其学也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
又曰：&ldquo;扬子云曰：&lsquo;古者杨墨塞路，孟子辞而辟之，廓如也。&rsquo;夫杨墨行，正道废。孟子虽贤圣，不得位。空言无施，虽切何补。然赖其言，而今之学者尚知宗孔氏，崇仁义，贵王贱霸而已。其大经大法，皆亡灭而不救，坏烂而不收。所谓存十一于千百，安在其能廓如也？然向无孟氏，则皆服左衽而言侏离矣。故愈尝推尊孟氏，以为功不在禹下者，为此也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
或问于程子曰：&ldquo;孟子还可谓圣人否？&rdquo;程子曰：&ldquo;未敢便道他是圣人，然学已到至处。&rdquo;愚按：至字，恐当作圣字。<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
程子又曰：&ldquo;孟子有功于圣门，不可胜言。仲尼只说一个仁字，孟子开口便说仁义。仲尼只说一个志，孟子便说许多养气出来。只此二字，其功甚多。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
又曰：&ldquo;孟子有大功于世，以其言性善也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
又曰：&ldquo;孟子性善、养气之论，皆前圣所未发。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
又曰：&ldquo;学者全要识时。若不识时，不足以言学。颜子陋巷自乐，以有孔子在焉。若孟子之时，世既无人，安可不以道自任。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
又曰：&ldquo;孟子有些英气。才有英气，便有圭角，英气甚害事。如颜子便浑厚不同，颜子去圣人只豪发闲。孟子大贤，亚圣之次也。&rdquo;或曰：&ldquo;英气见于甚处？&rdquo;曰：&ldquo;但以孔子之言比之，便可见。且如冰与水精非不光。比之玉，自是有温润含蓄气象，无许多光耀也。&rdquo;<br style="padding: 0px; margin: 0px;" />
<br style="padding: 0px; margin: 0px;" />
杨氏曰：&ldquo;孟子一书，只是要正人心，教人存心养性，收其放心。至论仁、义、礼、智，则以恻隐、善恶、辞让、是非之心为之端。论邪说之害，则曰：&lsquo;生于其心，害于其政。&rsquo;论事君，则曰：&lsquo;格君心之非&rsquo;，&lsquo;一正君而国定&rsquo;。千变万化，只说从心上来。人能正心，则事无足为者矣。大学之修身、齐家、治国、平天下，其本只是正心、诚意而已。心得其正，然后知性之善。故孟子遇人便道性善。欧阳永叔却言&lsquo;圣人之教人，性非所先&rsquo;，可谓误矣。人性上不可添一物，尧舜所以为万世法，亦是率性而已。所谓率性，循天理是也。外边用计用数，假饶立得功业，只是人欲之私。与圣贤作处，天地悬隔。&rdquo;</span><span style="color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">&nbsp;</span>
      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_20'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=20;
  	var __dedeqrcode_aid=40;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',40)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',40)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(40);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/handaizhihou/songming/2021/0810/39.html'>《论语集注》· 读论语孟子法</a> </li>
     <li>下一篇：<a href='/a/handaizhihou/songming/2021/0810/41.html'>《论语集注》· 论语序说</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=40" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=40&title=《孟子集注》· 孟子序说" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=40" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="40" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=40">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=40&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '40');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/handaizhihou/weijinnanbeichao/'>魏晋南北朝</a></li>
      
      <li><a href='/a/handaizhihou/suitang/'>隋唐</a></li>
      <li><a href='/a/handaizhihou/songming/' class='thisclass'>宋明</a></li>
      <li><a href='/a/handaizhihou/weijinnanbeichao/qingdai/'>清代</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      
     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/handaizhihou/songming/2021/0810/40.html">《孟子集注》· 孟子序说</a></li>
<li><a href="/a/handaizhihou/songming/2021/0810/41.html">《论语集注》· 论语序说</a></li>
<li><a href="/a/handaizhihou/songming/2021/0810/42.html">《四书章句集注》·  中庸</a></li>
<li><a href="/a/handaizhihou/songming/2021/0810/39.html">《论语集注》· 读论语孟</a></li>
<li><a href="/a/handaizhihou/songming/2021/0810/43.html">《四书章句集注》 · 大学</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
