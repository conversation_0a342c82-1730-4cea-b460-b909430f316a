<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《论语集注》· 论语序说_万婕源码网</title>
<meta name="keywords" content="《,论语集注,》,论,语序,说,史记,世家,曰," />
<meta name="description" content="史记世家曰：孔子名丘，字仲尼。其先宋人。父叔梁纥，母颜氏。以鲁襄公二十二年，庚戌之岁，十一月庚子，生孔子于鲁昌平乡陬邑。为儿嬉戏，常陈俎豆，设礼容。及长，为委吏，" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=41">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=41";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li class='hover'><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/handaizhihou/'>汉代之后</a> > <a href='/a/handaizhihou/songming/'>宋明</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《论语集注》· 论语序说</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:15<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=41&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">史记世家曰：孔子名丘，字仲尼。其先宋人。父叔梁纥，母颜氏。以鲁襄公二十二年，庚戌之岁，十一月庚子，生孔子于鲁昌平乡陬邑。为儿嬉戏，常陈俎豆，设礼容。及长，为委吏，</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <p style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">
	<span style="padding: 0px; margin: 0px; font-size: 16px;">史记世家曰：&ldquo;孔子名丘，字仲尼。其先宋人。父叔梁纥，母颜氏。以鲁襄公二十二年，庚戌之岁，十一月庚子，生孔子于鲁昌平乡陬邑。为儿嬉戏，常陈俎豆，设礼容。及长，为委吏，料量平；委吏，本作季氏史。索隐云：&ldquo;一本作委吏，与孟子合。&rdquo;今从之。为司职吏，畜蕃息。职，见周礼牛人，读为樴，义与杙同，盖系养牺牲之所。此官即孟子所谓乘田。适周，问礼于老子，既反，而弟子益进。昭公二十五年甲申，孔子年三十五，而昭公奔齐，鲁乱。于是适齐，为高昭子家臣，以通乎景公。有闻韶、问政二事。公欲封以尼溪之田，晏婴不可，公惑之。有季孟吾老之语。孔子遂行，反乎鲁。定公元年壬辰，孔子年四十三，而季氏强僭，其臣阳虎作乱专政。故孔子不仕，而退修诗、书、礼、乐，弟子弥众。九年庚子，孔子年五十一。公山不狃以费畔季氏，召，孔子欲往，而卒不行。有答子路东周语。定公以孔子为中都宰，一年，四方则之，遂为司空，又为大司寇。十年辛丑，相定公会齐侯于夹谷，齐人归鲁侵地。十二年癸卯，使仲由为季氏宰，堕三都，收其甲兵。孟氏不肯堕成，围之不克。十四年乙巳，孔子年五十六，摄行相事，诛少正卯，与闻国政。三月，鲁国大治。齐人归女乐以沮之，季桓子受之。郊又不致膰俎于大夫，孔子行。鲁世家以此以上皆为十二年事。适卫，主于子路妻兄颜浊邹家。孟子作颜雠由。适陈，过匡，匡人以为阳虎而拘之。有颜渊后及文王既没之语。既解，还卫，主蘧伯玉家，见南子。有矢子路及未见好德之语。去适宋，司马桓魋欲杀之。有天生德语及微服过宋事。又去，适陈，主司城贞子家。居三岁而反于卫，灵公不能用。有三年有成之语。晋赵氏家臣佛肸以中牟畔，召孔子，孔子欲往，亦不果。有答子路坚白语及荷蒉过门事。将西见赵简子，至河而反，又主蘧伯玉家。灵公问陈，不对而行，复如陈。据论语则绝粮当在此时。季桓子卒，遗言谓康子必召孔子，其臣止之，康子乃召冉求。史记以论语归与之叹为在此时，又以孟子所记叹辞为主司城贞子时语，疑不然。盖语孟所记，本皆此一时语，而所记有异同耳。孔子如蔡及叶。有叶公问答子路不对、沮溺耦耕、荷蓧丈人等事。史记云：&ldquo;于是楚昭王使人聘孔子，孔子将往拜礼，而陈蔡大夫发徒围之，故孔子绝粮于陈蔡之间。&rdquo;有愠见及告子贡一贯之语。按是时陈蔡臣服于楚，若楚王来聘孔子，陈蔡大夫安敢围之。且据论语，绝粮当在去卫如陈之时。楚昭王将以书社地封孔子，令尹子西不可，乃止。史记云&ldquo;书社地七百里&rdquo;，恐无此理，时则有接舆之歌。又反乎卫，时灵公已卒，卫君辄欲得孔子为政。有鲁卫兄弟及答子贡夷齐、子路正名之语。而冉求为季氏将，与齐战有功，康子乃召孔子，而孔子归鲁，实哀公之十一年丁巳，而孔子年六十八矣。有对哀公及康子语。然鲁终不能用孔子，孔子亦不求仕，乃叙书传礼记。有杞宋、损益、从周等语。删诗正乐，有语大师及乐正之语。序易彖、系、象、说卦、文言。有假我数年之语。弟子盖三千焉，身通六艺者七十二人。弟子颜回最贤，蚤死，后惟曾参得传孔子之道。十四年庚申，鲁西狩获麟，有莫我知之叹。孔子作春秋。有知我罪我等语，论语请讨陈恒事，亦在是年。明年辛酉，子路死于卫。十六年壬戌、四月己丑，孔子卒，年七十三，葬鲁城北泗上。弟子皆服心丧三年而去，惟子贡庐于冢上，凡六年，孔子生鲤，字伯鱼，先卒。伯鱼生伋，字子思，作中庸。&rdquo;子思学于曾子，而孟子受业子思之门人。</span></p>
<p style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">
	<br style="padding: 0px; margin: 0px;" />
	<span style="padding: 0px; margin: 0px; font-size: 16px;">何氏曰：&ldquo;鲁论语二十篇。齐论语别有问王、知道，凡二十二篇，其二十篇中章句，颇多于鲁论。古论出孔氏壁中，分尧曰下章子张问以为一篇，有两子张，凡二十一篇，篇次不与齐鲁论同。&rdquo;<br style="padding: 0px; margin: 0px;" />
	<br style="padding: 0px; margin: 0px;" />
	程子曰：&ldquo;论语之书，成于有子曾子之门人，故其书独二子以子称。&rdquo;<br style="padding: 0px; margin: 0px;" />
	<br style="padding: 0px; margin: 0px;" />
	程子曰：&ldquo;读论语：有读了全然无事者；有读了后其中得一两句喜者；有读了后知好之者；有读了后直有不知手之舞之足之蹈之者。&rdquo;<br style="padding: 0px; margin: 0px;" />
	<br style="padding: 0px; margin: 0px;" />
	程子曰：&ldquo;今人不会读书。如读论语，未读时是此等人，读了后又只是此等人，便是不曾读。&rdquo;<br style="padding: 0px; margin: 0px;" />
	<br style="padding: 0px; margin: 0px;" />
	程子曰：&ldquo;颐自十七八读论语，当时已晓文义。读之愈久，但觉意味深长。&rdquo;</span></p>

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_1'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=1;
  	var __dedeqrcode_aid=41;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',41)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',41)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(41);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/handaizhihou/songming/2021/0810/40.html'>《孟子集注》· 孟子序说</a> </li>
     <li>下一篇：<a href='/a/handaizhihou/songming/2021/0810/42.html'>《四书章句集注》·  中庸章句序</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=41" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=41&title=《论语集注》· 论语序说" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=41" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="41" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=41">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=41&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '41');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/handaizhihou/weijinnanbeichao/'>魏晋南北朝</a></li>
      
      <li><a href='/a/handaizhihou/suitang/'>隋唐</a></li>
      <li><a href='/a/handaizhihou/songming/' class='thisclass'>宋明</a></li>
      <li><a href='/a/handaizhihou/weijinnanbeichao/qingdai/'>清代</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      
     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/handaizhihou/songming/2021/0810/40.html">《孟子集注》· 孟子序说</a></li>
<li><a href="/a/handaizhihou/songming/2021/0810/41.html">《论语集注》· 论语序说</a></li>
<li><a href="/a/handaizhihou/songming/2021/0810/42.html">《四书章句集注》·  中庸</a></li>
<li><a href="/a/handaizhihou/songming/2021/0810/39.html">《论语集注》· 读论语孟</a></li>
<li><a href="/a/handaizhihou/songming/2021/0810/43.html">《四书章句集注》 · 大学</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
