<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《墨子闲诂》· 卷一 · 三辩_万婕源码网</title>
<meta name="keywords" content="《,墨子闲诂,》,卷一,三辩,毕云,此辩,圣王," />
<meta name="description" content="毕云：此辩圣王虽用乐，而治不在此。三者，谓尧舜及汤及武王也。诒让案：此篇所论盖非乐之馀义。 程繁毕云：太平御览引作程子。诒让案：《公孟》篇亦作程子，盖兼治儒墨之学者" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=48">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=48";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li class='hover'><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/handaizhihou/'>汉代之后</a> > <a href='/a/handaizhihou/weijinnanbeichao/qingdai/'>清代</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《墨子闲诂》· 卷一 · 三辩</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:22<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=48&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">毕云：此辩圣王虽用乐，而治不在此。三者，谓尧舜及汤及武王也。诒让案：此篇所论盖非乐之馀义。 程繁毕云：太平御览引作程子。诒让案：《公孟》篇亦作程子，盖兼治儒墨之学者</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">毕云：&ldquo;此辩圣王虽用乐，而治不在此。三者，谓尧舜及汤及武王也。&rdquo;诒让案：此篇所论盖非乐之馀义。</span><br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">程繁毕云：&ldquo;太平御览引作程子。&rdquo;诒让案：《公孟》篇亦作&ldquo;程子&rdquo;，盖兼治儒墨之学者。问于子墨子曰：&ldquo;夫子曰旧本无此三字，王云：&ldquo;&lsquo;圣王&rsquo;上当有&lsquo;夫子曰&rsquo;三字，而今本脱之，则文义不明。下文&lsquo;今夫子曰：圣王不为乐&rsquo;，是其证。&rdquo;案：王说是也，今据增。&lsquo;圣王不为乐&rsquo;。昔诸侯倦于听治，息于钟鼓之乐；钟鼓谓金奏。士大夫倦于听治，息于竽瑟之乐；《周礼小胥》云：&ldquo;卿大夫判县，士特县。&rdquo;《曲礼》云&ldquo;大夫无故不彻县，士无故不彻琴瑟&rdquo;，孔颖达疏以为不命之士，若命士，则特县。若然，士大夫之乐亦有钟鼓。考《贾子新书&middot;审微》篇云：&ldquo;大夫直县，士有琴瑟&rdquo;，公羊隐五年何注引鲁诗传云：&ldquo;大夫士曰琴瑟。&rdquo;《白虎通义&middot;礼乐》篇云：&ldquo;诗传曰：大夫士琴瑟也。大夫士北面之臣，非专事子民，故但琴瑟而已。&rdquo;曲礼疏引春秋说题辞，亦谓&ldquo;乐无大夫士制&rdquo;。此书义盖与鲁诗、春秋纬略同。农夫春耕夏耘，毕云：&ldquo;《说文》云：&lsquo;●，除苗间秽也，●或字&rsquo;，此省文。&rdquo;秋敛冬藏，毕云&ldquo;古只作&lsquo;臧&rsquo;。&rdquo;息于聆缶之乐。毕云&lsquo;聆&rsquo;当为&lsquo;瓴&rsquo;。&lsquo;聆缶&rsquo;，太平御览引作为&lsquo;吟谣&rsquo;，是也。&lsquo;缶&rsquo;是&lsquo;●&rsquo;字之坏。&rdquo;王云：&ldquo;今本墨子作&lsquo;聆缶&rsquo;者，&lsquo;聆&rsquo;乃&lsquo;●&rsquo;字之讹，&lsquo;●&rsquo;即&lsquo;瓴&rsquo;字也，但移瓦于左，移令于右耳。北堂书钞乐部七缶下，钞本太平御览乐部三及二十二缶下引墨子，并作&lsquo;吟&rsquo;缶&rsquo;。&lsquo;吟&rsquo;亦&lsquo;●&rsquo;之讹。盖墨子书&lsquo;瓴&rsquo;字本作&lsquo;●&rsquo;，故今本讹作&lsquo;聆&rsquo;，诸类书讹作&lsquo;吟&rsquo;，而缶字则皆不讹也。其刻本御览作&lsquo;吟谣&rsquo;者，后人不知&lsquo;吟&rsquo;为&lsquo;●&rsquo;之讹，遂改&lsquo;吟缶&rsquo;为&lsquo;吟谣&rsquo;耳。上文云：&lsquo;诸侯息于钟鼓，上大夫息于竽瑟&rsquo;，此云：&lsquo;农夫息于●缶&rsquo;，钟鼓，竽瑟、●缶皆乐器也。《淮南&middot;精神》篇&lsquo;叩盆拊瓴相和而歌&rsquo;，盆即缶也。若吟谣则非乐器，不得言吟谣之乐矣。&rdquo;案：王说是也。《说文&middot;瓦部》云：&ldquo;瓴，●也，似瓶者&rdquo;。又《缶部》云：&ldquo;缶，瓦器，所以盛酒浆，秦人鼓之以节歌。&rdquo;《诗&middot;陈风&middot;宛丘》篇&ldquo;坎其击缶&rdquo;，《毛传》云：&ldquo;盎谓之缶&rdquo;，《尔雅&middot;释器》同，郭注云：&ldquo;盆也&rdquo;《史记李斯传》云：&ldquo;击瓮叩●，真秦之声也。&rdquo;瓴、瓮同物，●即缶之俗。今夫子曰：&lsquo;圣王不为乐&rsquo;，此譬之犹马驾而不税，《方言》云：&ldquo;税，舍车也。赵、宋、陈、魏之闲谓之税&rdquo;，郭璞注云：&ldquo;税犹脱也。&rdquo;毕云：&ldquo;太平御览作&lsquo;脱&rsquo;，同。&rdquo;弓张而不弛，无乃非有血气者之所不能至邪？&rdquo;俞云：&ldquo;&lsquo;非&rsquo;字衍文。&rdquo;</span><br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">子墨子曰：&ldquo;昔者尧舜有茅茨者，毕云：&ldquo;&lsquo;茅茨&rsquo;旧作&lsquo;第期&rsquo;，今据太平御览改。&rdquo;俞云：&ldquo;茅茨土阶，是言古明堂之俭，不得云且以为礼、且以为乐也。下文曰：&lsquo;周成王之治天下也，不若武王；武王之治天下也，不若成汤；成汤之治天下也，不若尧、舜。故其乐逾繁者，其治逾寡&rsquo;，然则其说尧﹑舜，亦当以乐言，不当以宫室言也，疑后人不达第期之义，而臆改之，未可为据，仍当从原文，而阙其疑。&rdquo;案：俞说非也。若第期专以乐言，则下文不当云且以为礼。毕校不误。诗小雅甫田，郑笺云：&ldquo;茨，屋盖也&rdquo;，孔疏云：&ldquo;墨子称茅茨不翦，谓以茅覆屋。&rdquo;且以为礼，且以为乐；汤放桀于大水，苏云：&ldquo;案《列女传》云&lsquo;流于海，死于南巢之山&rsquo;，《尚书大传》云&lsquo;国君之国也，吾闻海外有人，与其属五百人去&rsquo;，与此言合。&rdquo;环天下自立以为王，事成功立，无大后患，因先王之乐，又自作乐，命曰护，又修九招；毕云：&ldquo;&lsquo;修&rsquo;旧作&lsquo;循&rsquo;，今以意改。已上十六字旧脱，今据太平御览增。《吕氏春秋》云：&lsquo;汤命伊伊作为大护，歌晨露，修九招、六列。&rsquo;&rdquo;案：道藏本虽亦有脱文，然尚有&ldquo;自作乐命曰九招&rdquo;七字，则未全脱也，毕说未审，风俗通义声音篇云：&ldquo;汤作护。护言救民也&rdquo;，艺文类聚帝王部引春秋元命苞云：&ldquo;汤之时民大乐其救于患害，故护者救也&rdquo;，《白虎通义&middot;礼乐》篇云：&ldquo;汤曰大护者，言汤承衰能护民之急也&rdquo;，公羊隐五年，何注云：&ldquo;殷曰大护，殷时民乐，大其护己也&rdquo;，并与此同。《周礼大司乐》&ldquo;护&rdquo;作&ldquo;濩&rdquo;，《汉书&middot;礼乐志》同&ldquo;护&rdquo;，&ldquo;濩&rdquo;字亦通。九招，即书皋陶谟&ldquo;箫韶九成&rdquo;，舜乐也。《史记&middot;夏本纪》云：&ldquo;禹兴九招之乐&rdquo;，《吕氏春秋&middot;古乐》篇云：&ldquo;喾作九招，舜令质修之。&rdquo;《山海经&middot;大荒西经》云：&ldquo;启始歌九招&rdquo;，《周礼大司乐》作&ldquo;九●&rdquo;。招、韶、●字并通。武王胜殷杀纣，环天下自立以为王，事成功立，无大后患，因先王之乐，又自作乐，命曰象；毕云；&ldquo;《吕氏春秋》云：&lsquo;周公为三象&rsquo;，乃成王之乐。此云象又是武王作，未详。&rdquo;案：《毛诗周颂序》云：&ldquo;维清，奏象舞也&rdquo;，郑笺云：&ldquo;象，用兵时刺伐之舞，武王制焉。&rdquo;《礼记&middot;文王世子》&ldquo;下管象&rdquo;，郑注云：&ldquo;象，周武王伐纣之乐。&rdquo;《春秋繁露&middot;三代改制质文》篇云：&ldquo;文王作武乐，武王作象乐，周公作汋乐。&rdquo;《淮南子&middot;泛论训》云：&ldquo;周武象。&rdquo;高注云：&ldquo;武王乐也。&rdquo;《白虎通义&middot;礼乐》篇云：&ldquo;周公曰酌，武王曰象者，象太平而作乐，示已太平也，合曰大武。&rdquo;此皆以象为武王所作。毕专据《吕览&middot;古乐》篇以疑此书，殊为失考。周礼大司乐六乐有大武而无象，则大武自为周之正乐，象盖舞之小者。周颂孔疏谓象舞象文王之事，大武象武王之事，大武之乐亦为象，傅合武、象为一，非也。《左&middot;襄二十九年传》云&ldquo;见舞象箾南龠者&rdquo;，杜注云&ldquo;象箾舞所执，文王之乐&rdquo;，杜又以象为文王乐，史记吴世家集解引贾逵、诗周颂疏引服虔，说并同，盖皆传闻之异。周成王因先王之乐，又自作乐，命曰驺虞。王云：&ldquo;御览引作&lsquo;周成王因先王之乐，又自作乐，命曰驺吾&rsquo;，是也。上文云：&lsquo;汤因先王之乐，又自作乐，命曰护。武王因先王之乐，又自作乐，命曰象&rsquo;，即其证。今本脱去&lsquo;又自作乐&rsquo;四字，则义不可通。困学纪闻所引已同。今本书传中，&lsquo;驺虞&rsquo;字多作&lsquo;驺吾&rsquo;，故困学纪闻诗类引墨子尚作&lsquo;驺吾&rsquo;，今作&lsquo;驺虞&rsquo;者，后人依经典改之。&rdquo;案：王说是也，今据增。钞本御览乐部三引此书，&ldquo;驺虞&rdquo;又作&ldquo;邹吾&rdquo;，字并通。《诗召南有驺虞》篇，盖作于成王时，故墨子以为成王之乐。凡诗皆可入乐也。《周礼大司乐》&ldquo;大射令奏驺虞&rdquo;，郑注云：&ldquo;驺虞，乐章名。&rdquo;周成王之治天下也，不若武王，武王之治天下也，不若成汤，成汤之治天下也，不若尧舜。故其乐逾繁者，其治逾寡。自此观之，乐非所以治天下也。&rdquo;</span><br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">程繁曰：&ldquo;子曰：&lsquo;圣王无乐&rsquo;，此亦乐已，若之何其谓圣王无乐也？&rdquo;子墨子曰：&ldquo;圣王之命也，命与令义同。苏云：&ldquo;此下有阙文误字。&rdquo;多寡之。此疑当作&ldquo;多者寡之&rdquo;。言凡物病其多者，则务寡之。食之利也，以知饥而食之者智也，因为无智矣。今圣有乐而少，此亦无也。&rdquo;毕云：&ldquo;言人所以生者，食之利，但必以知饥而食之，否则非智。今圣人虽用乐而少，此亦无违于圣人。&lsquo;无&rsquo;下疑有脱字。&rdquo;案：毕说非也。&ldquo;因&rdquo;，当作&ldquo;固&rdquo;，&ldquo;今圣&rdquo;下当有&ldquo;王&rdquo;字。此言食为人之利，然人饥知食，不足为智，若因饥知食而谓之为智，则所知甚浅，固为无智矣，以喻圣王虽作乐而少，犹之无乐也。末句&ldquo;无&rdquo;下似无脱字。</span>
      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_8'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=8;
  	var __dedeqrcode_aid=48;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',48)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',48)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(48);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/47.html'>《墨子闲诂》· 卷四 ·兼爱上</a> </li>
     <li>下一篇：<a href='/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/49.html'>《墨子闲诂》· 卷一 · 七患</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=48" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=48&title=《墨子闲诂》· 卷一 · 三辩" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=48" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="48" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=48">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=48&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '48');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/handaizhihou/weijinnanbeichao/'>魏晋南北朝</a></li>
      
      <li><a href='/a/handaizhihou/suitang/'>隋唐</a></li>
      
      <li><a href='/a/handaizhihou/songming/'>宋明</a></li>
      <li><a href='/a/handaizhihou/weijinnanbeichao/qingdai/' class='thisclass'>清代</a></li>
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/46.html">《墨子闲诂》· 卷四 ·兼爱下</a>
       <p>子墨子言曰：仁人之事者，必务求兴天下之利，除天下之害。然...</p>
      </li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/49.html">《墨子闲诂》· 卷一 · 七患</a>
       <p>子墨子曰：国有七患。七患者何？城郭沟池不可守，而治宫室，...</p>
      </li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/50.html">《墨子闲诂》· 卷一 · 修身</a>
       <p>毕云：修治之字从彡，从肉者修脯字，经典假借多用此。 君子战...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/50.html">《墨子闲诂》· 卷一 · 修</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/49.html">《墨子闲诂》· 卷一 · 七</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/45.html">《红楼梦》· 薄命女偏逢</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/48.html">《墨子闲诂》· 卷一 · 三</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/47.html">《墨子闲诂》· 卷四 ·兼</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/46.html">《墨子闲诂》· 卷四 ·兼</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/44.html">《红楼梦》· 甄士隐梦幻</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
