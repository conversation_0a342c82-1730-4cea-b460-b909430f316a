<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《墨子闲诂》· 卷一 · 修身_万婕源码网</title>
<meta name="keywords" content="《,墨子闲诂,》,卷一,修身,毕云,修治,之字,从," />
<meta name="description" content="毕云：修治之字从彡，从肉者修脯字，经典假借多用此。 君子战虽有陈，而勇为本焉；丧虽有礼，而哀为本焉；士虽有学，而行为本焉。俞云：君子二字衍文也。此盖以战虽有陈，丧虽" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=50">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=50";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li class='hover'><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/handaizhihou/'>汉代之后</a> > <a href='/a/handaizhihou/weijinnanbeichao/qingdai/'>清代</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《墨子闲诂》· 卷一 · 修身</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:22<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=50&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">毕云：修治之字从彡，从肉者修脯字，经典假借多用此。 君子战虽有陈，而勇为本焉；丧虽有礼，而哀为本焉；士虽有学，而行为本焉。俞云：君子二字衍文也。此盖以战虽有陈，丧虽</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">毕云：&ldquo;修治之字从彡，从肉者修脯字，经典假借多用此。&rdquo;</span><br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">君子战虽有陈，而勇为本焉；丧虽有礼，而哀为本焉；士虽有学，而行为本焉。俞云：&ldquo;&lsquo;君子&rsquo;二字衍文也。此盖以&lsquo;战虽有陈&rsquo;，&lsquo;丧虽有礼&rsquo;二句，起&lsquo;士虽有学&rsquo;一句，若冠以&lsquo;君子&rsquo;二字，则既言君子不必又言士矣。马总意林作&lsquo;君子虽有学，行为本焉；战虽有陈，勇为本焉；丧虽有礼，哀为本焉&rsquo;，与今本不同。然有&lsquo;君子&rsquo;字，即无&lsquo;士&rsquo;字，亦可知今本既言君子又言士之误矣。士虽有学，与君子虽有学，文异而义同。&rdquo;案：《说苑&middot;建本》篇载孔子语，与此略同。&ldquo;君子&rdquo;似非衍文，亦见《家语&middot;六本》篇。是故置本不安者，无务丰末；置与植通，《诗&middot;商颂&middot;那》&ldquo;置我鞉鼓&rdquo;，郑笺云：&ldquo;置读曰植。&rdquo;《方言》云：&ldquo;植，立也。&rdquo;俞云：&ldquo;&lsquo;者&rsquo;，衍字也。下文&lsquo;近者不亲，无务来远；亲戚不附，无务外交；事无终始，无务多业；举物而暗，无务博闻&rsquo;，上句并无&lsquo;者&rsquo;字，是其证。&rdquo;近者不亲，无务来远；亲戚不附，《曲礼》云：&ldquo;兄弟亲戚，称其慈也&rdquo;，孔颖达疏云：&ldquo;亲指族内，戚言族外。&rdquo;案：古多称父母为亲戚，详《兼爱下》篇。此则似通内外族姻言之，与孔义同。无务外交；事无终始，无务多业；《尔雅&middot;释诂》云：&ldquo;业，事也。&rdquo;举物而暗，无务博闻。</span><br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">是故先王之治天下也，必察迩来远。君子察迩而迩修者也。见不修行，毕读句。见毁，毕读句。而反之身者也，此以怨省而行修矣。谮慝之言，无入之耳；&ldquo;之&rdquo;，毕本讹&ldquo;于&rdquo;，今据道藏本正，王校同。毕云：&ldquo;《玉篇》云：&lsquo;慝，他得切，恶也。&rsquo;经典多此字，古只作&lsquo;匿&rsquo;。&rdquo;王云：&ldquo;谮慝，即谗慝。《僖二十八年左传》&lsquo;闲执谗慝之口&rsquo;，是也。谗与谮古字通，故《小雅&middot;巷伯》篇&lsquo;取彼谮人&rsquo;，缁衣注及《后汉书马援传》，并引作&lsquo;取彼谗人&rsquo;。无入之耳，言不听谗慝之言也。故下文曰&lsquo;虽有诋讦之民，无所依矣&rsquo;。&rdquo;批捍之声，《广雅释诂》云：&ldquo;批，击也。&rdquo;易林睽之贲云：&ldquo;批捍之言，我心不快&rdquo;，批捍即批捍也。毕云：&ldquo;《说文》云：&lsquo;捍，忮也。&rsquo;《玉篇》云：&lsquo;忓，古安切，又胡旦切，扰也&rsquo;&rdquo;无出之口；杀伤人之孩，毕云：&ldquo;当读如根荄。&rdquo;无存之心，虽有诋讦之民，毕云：&ldquo;《说文》云：&lsquo;诋，诃也；讦，面相斥罪也。&rsquo;《玉篇》云：&lsquo;诋，都礼切；讦，居谒切，攻人之阴私也&rsquo;。&rdquo;无所依矣。</span><br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">故君子力事日强，愿欲日逾，逾，当读为偷，同声假借字，此与&ldquo;力事日强&rdquo;文相对。《礼记&middot;表记》云：&ldquo;君子庄敬日强，安肆日偷，&rdquo;，郑注云：&ldquo;偷，苟且也。&rdquo;此义与彼正同。设壮日盛。毕云：&ldquo;&lsquo;设壮&rsquo;，疑作为&lsquo;饰庄&rsquo;。&rdquo;君子之道也，贫则见廉，富则见义，毕云：&ldquo;义字当为&lsquo;羛&rsquo;，《说文》云：&lsquo;墨翟书&ldquo;义&rdquo;从弗&rsquo;，则汉时本如此。今书&lsquo;义&rsquo;字，皆俗改也。&rdquo;王引之云：&ldquo;&lsquo;弗&rsquo;，于声义均有未协，&lsquo;弗&rsquo;当作&lsquo;●&rsquo;，&lsquo;●&rsquo;，古文&lsquo;我&rsquo;字，与&lsquo;弗&rsquo;相似，故讹作&lsquo;弗&rsquo;耳。周晋姜鼎铭，&lsquo;我&rsquo;字作&lsquo;●&rsquo;，是其明证。羛之从●声，与义之从我声，一也。《说文》&lsquo;我&rsquo;字下，重文未载，古文作&lsquo;●&rsquo;，故于此亦不知为&lsquo;●&rsquo;字之讹。盖锺鼎古篆，汉人亦不能遍识也。&rdquo;生则见爱，死则见哀，四行者不可虚假，反之身者也。藏于心者无以竭爱，动于身者无以竭恭，出于口者无以竭驯。驯，犹雅驯。《史记&middot;五帝本纪》云：&ldquo;不雅驯&rdquo;，张守节正义云：&ldquo;驯，训也&rdquo;。案：驯、训字通。周礼地官叙官，郑众注云：&ldquo;训读为驯&rdquo;，训与尔雅释训义同，谓出口者皆典雅之言。畅之四支，《说文&middot;肉部》云：&ldquo;肢，体四肢也，或作肢&rdquo;。支，即肢之省。易坤文言云：&ldquo;美在其中，而畅于四支&rdquo;，孔颖达疏云：&ldquo;四支，犹言手足。&rdquo;接之肌肤，《小尔雅&middot;广诂》云：&ldquo;接，达也&rdquo;，亦与挟通。仪礼乡射礼，郑注云：&ldquo;古文&lsquo;挟&rsquo;皆作&lsquo;接&rsquo;，俗作&ldquo;浃&rdquo;，义并同。《吕氏春秋谕威》篇云：&ldquo;其藏于民心，捷于肌肤也，深痛疾固&rdquo;，高注云：&ldquo;捷，养也。&rdquo;案：捷、接字亦通，高失其义。华发隳颠，道藏本，&ldquo;颠&rdquo;作&ldquo;巅&rdquo;，非。《后汉书边让传》，李贤注云：&ldquo;华发，白首也。&rdquo;毕云：&ldquo;&lsquo;隳&rsquo;字当为&lsquo;堕&rsquo;&rdquo;。诒让案：《说文&middot;髟部》云：&ldquo;鬌，发堕也&rdquo;，《页部》云：&ldquo;颠，顶也。&rdquo;堕与鬌通，堕颠即秃顶。《新序&middot;杂事》篇云：&ldquo;齐宣王谓闾丘卬曰：士亦华发堕颠，而后可用耳&rdquo;。而犹弗舍者，其唯圣人乎！</span><br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">志不强者智不达，言不信者行不果。毕云：&ldquo;文选注云：&lsquo;许君注淮南子云：果，成也。&rsquo;&rdquo;据财不能以分人者，不足与友；守道不笃、遍物不博、俞云：&ldquo;遍，亦辩也。《仪礼&middot;乡饮酒礼》&lsquo;众宾辩有脯醢&rsquo;，燕礼&lsquo;大夫辩受酬&rsquo;，少牢馈食礼&lsquo;辩擩于三豆&rsquo;，今文&lsquo;辩&rsquo;皆作&lsquo;遍&rsquo;，是&lsquo;辩&rsquo;与&lsquo;遍&rsquo;通用。物言遍，是非言辩，文异而义同。&rdquo;辩是非不察者，不足与游。本不固者末必几，毕云：&ldquo;《广雅》云：&lsquo;几，微也。&rsquo;或&lsquo;●&rsquo;字之假音，《说文》云：&lsquo;</span>
      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_10'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=10;
  	var __dedeqrcode_aid=50;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',50)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',50)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(50);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/49.html'>《墨子闲诂》· 卷一 · 七患</a> </li>
     <li>下一篇：没有了 </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=50" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=50&title=《墨子闲诂》· 卷一 · 修身" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=50" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="50" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=50">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=50&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '50');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/handaizhihou/weijinnanbeichao/'>魏晋南北朝</a></li>
      
      <li><a href='/a/handaizhihou/suitang/'>隋唐</a></li>
      
      <li><a href='/a/handaizhihou/songming/'>宋明</a></li>
      <li><a href='/a/handaizhihou/weijinnanbeichao/qingdai/' class='thisclass'>清代</a></li>
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/46.html">《墨子闲诂》· 卷四 ·兼爱下</a>
       <p>子墨子言曰：仁人之事者，必务求兴天下之利，除天下之害。然...</p>
      </li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/49.html">《墨子闲诂》· 卷一 · 七患</a>
       <p>子墨子曰：国有七患。七患者何？城郭沟池不可守，而治宫室，...</p>
      </li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/50.html">《墨子闲诂》· 卷一 · 修身</a>
       <p>毕云：修治之字从彡，从肉者修脯字，经典假借多用此。 君子战...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/50.html">《墨子闲诂》· 卷一 · 修</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/49.html">《墨子闲诂》· 卷一 · 七</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/45.html">《红楼梦》· 薄命女偏逢</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/48.html">《墨子闲诂》· 卷一 · 三</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/47.html">《墨子闲诂》· 卷四 ·兼</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/46.html">《墨子闲诂》· 卷四 ·兼</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/44.html">《红楼梦》· 甄士隐梦幻</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
