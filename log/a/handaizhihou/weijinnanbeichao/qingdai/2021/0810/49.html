<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>《墨子闲诂》· 卷一 · 七患_万婕源码网</title>
<meta name="keywords" content="《,墨子闲诂,》,卷一,七患,子,墨子,曰,国有," />
<meta name="description" content="子墨子曰：国有七患。七患者何？城郭沟池不可守，而治宫室，一患也；边国至境毕云：当为竟。本书《耕柱》云：楚四竟之田，只作竟。洪云：边当是适字之讹，古敌字多作适。言敌" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=49">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=49";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	<li class='hover'><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/handaizhihou/'>汉代之后</a> > <a href='/a/handaizhihou/weijinnanbeichao/qingdai/'>清代</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>《墨子闲诂》· 卷一 · 七患</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:22<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=49&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">子墨子曰：国有七患。七患者何？城郭沟池不可守，而治宫室，一患也；边国至境毕云：当为竟。本书《耕柱》云：楚四竟之田，只作竟。洪云：边当是适字之讹，古敌字多作适。言敌</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">子墨子曰：&ldquo;国有七患。七患者何？城郭沟池不可守，而治宫室，一患也；边国至境毕云：&ldquo;当为&lsquo;竟&rsquo;。本书《耕柱》云：&lsquo;楚四竟之田&rsquo;，只作&lsquo;竟&rsquo;。&rdquo;洪云：&ldquo;&lsquo;边&rsquo;当是&lsquo;适&rsquo;字之讹，古&lsquo;敌&rsquo;字多作&lsquo;适&rsquo;。言敌国至境，而四邻莫救，故可患也。&rdquo;四邻莫救，二患也；先尽民力无用之功，赏赐无能之人，民力尽于无用，财宝虚于待客，三患也；仕者持禄，游者爱佼，旧本&ldquo;持&rdquo;讹&ldquo;待&rdquo;，&ldquo;爱佼&rdquo;讹&ldquo;忧反&rdquo;。群书治要引&ldquo;待&rdquo;作&ldquo;持&rdquo;，&ldquo;反&rdquo;作&ldquo;佼&rdquo;。王云：&ldquo;&lsquo;待&rsquo;当为&lsquo;持&rsquo;，&lsquo;忧反&rsquo;当为&lsquo;爱交&rsquo;。吕氏春秋慎大篇注&lsquo;持犹守也&rsquo;。言仕者守其禄，游者爱其交，皆为己而不为国家也。《管子&middot;明法》篇曰：&lsquo;小臣持禄养交，不以官为事&rsquo;。《晏子春秋&middot;问篇》曰：&lsquo;士者持禄，游者养交&rsquo;。养交与爱交同意。今本&lsquo;持&rsquo;作&lsquo;待&rsquo;，&lsquo;爱交&rsquo;作&lsquo;忧反&rsquo;，则义不可通。《逸周书&middot;大开》篇&lsquo;祷无爱玉&rsquo;，今本&lsquo;爱&rsquo;讹作&lsquo;忧&rsquo;。隶书&lsquo;交&rsquo;字或作&lsquo;友&rsquo;，与&lsquo;反&rsquo;相似而讹也。&rdquo;俞云：&ldquo;王说是矣，然以&lsquo;忧&rsquo;为&lsquo;爱&rsquo;字之误，恐未必然。古书多言持禄养交，鲜言持禄爱交者。且持养二字同义，《荀子&middot;劝学》篇&lsquo;除其害者以持养之&rsquo;，《荣辱》篇&lsquo;以相群居，以相持养&rsquo;，议兵篇&lsquo;高爵丰禄以持养之&rsquo;，《吕氏春秋&middot;长见》篇&lsquo;申侯伯善持养吾意&rsquo;，并以&lsquo;持养&rsquo;连文。《墨子天志》篇亦云：&lsquo;持养其万民&rsquo;。然则此文既云持禄，必云养交，不当云爱交也。墨子原文盖本作&lsquo;恙交&rsquo;，&lsquo;恙&rsquo;即&lsquo;养&rsquo;之假字，古同声通用，后人不达假借之旨，改其字作&lsquo;忧&rsquo;，而墨子原文不可复见矣。&rdquo;案：王校是也，今据正。&ldquo;佼&rdquo;即&ldquo;交&rdquo;，字通，今从治要正。《管子&middot;七臣七主》篇云：&ldquo;好佼友而行私请&rdquo;，又明法篇云：&ldquo;以党举官，则民务佼而不求用&rdquo;，明法解云：&ldquo;群臣相推以美名，相假以功伐，务多其佼，而不为主用&rdquo;，并以&ldquo;佼&rdquo;为&ldquo;交&rdquo;。此云爱佼，犹管子云好佼、务佼也。《韩非子&middot;三守》篇云：&ldquo;群臣持禄养交&rdquo;，《荀子&middot;臣道》篇云：&ldquo;偷合苟容，以之持禄养交而已耳&rdquo;，诸书并云持禄，与此书同，而养交之文，则与此书微异。俞校必欲改&ldquo;忧&rdquo;为&ldquo;恙&rdquo;，以傅合之，则又求之太深，恐未塙。君修法讨臣，臣慑而不敢拂，旧本&ldquo;臣&rdquo;字不重，今据群书治要补。&ldquo;拂&rdquo;，治要作&ldquo;咈&rdquo;。案：&ldquo;咈&rdquo;正字，&ldquo;拂&rdquo;假字。《说文&middot;手部》云：&ldquo;拂，过击也&rdquo;，《口部》云：&ldquo;咈，违也。&rdquo;《荀子&middot;臣道》篇云：&ldquo;事暴君者，有补削无挢拂&rdquo;，杨注云：&ldquo;拂，违也。&rdquo;《贾子&middot;保傅》篇云：&ldquo;洁廉而切直，匡过而谏邪者谓之拂。拂者，拂天子之过者也。&rdquo;《书&middot;尧典》&ldquo;咈哉&rdquo;，伪孔传云：&ldquo;咈，戾也。&rdquo;四患也；君自以为圣智而不问事，自以为安强而无守备，四邻谋之不知戒，五患也；所信者不忠，所忠者不信，上句&ldquo;信&rdquo;字旧本讹&ldquo;言&rdquo;，又无两&ldquo;者&rdquo;字，今据群书治要补正。六患也；畜种菽粟&ldquo;畜&rdquo;治要作&ldquo;蓄&rdquo;，字通。毕云：&ldquo;&lsquo;菽&rsquo;正为&lsquo;尗&rsquo;。&rdquo;不足以食之，大臣不足以事之，毕云：&ldquo;旧脱&lsquo;以&rsquo;字，一本有。&rdquo;诒让案：群书治要亦有&ldquo;以&rdquo;字。荀子正名篇，杨注云：&ldquo;事，任使也。&rdquo;赏赐不能喜，诛罚不能威，七患也。以七患居国，必无社稷；&ldquo;无&rdquo;，疑当为&ldquo;亡&rdquo;。毕云：&ldquo;国、稷为韵。&rdquo;以七患守城，敌至国倾。毕云：&ldquo;城倾为韵。&rdquo;七患之所当，国必有殃。毕云：&ldquo;当、殃为韵。&rdquo;</span><br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<br style="padding: 0px; margin: 0px; color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;" />
<span style="color: rgb(0, 0, 0); font-family: Verdana, Arial, Tahoma; font-size: 16px;">凡五谷者，民之所仰也，君之所以为养也，故民无仰则君无养，毕云：&ldquo;仰、养为韵。&rdquo;民无食则不可事，毕云：&ldquo;食、事为韵&rdquo;。故食不可不务也，地不可不力也，用不可不节也。&ldquo;力&rdquo;，毕本作为&ldquo;立&rdquo;，云&ldquo;立、节为韵。&rdquo;案：毕本讹，今据道藏本及明刻本正。王云：&ldquo;毕说非也。古音&lsquo;立&rsquo;在缉部，&lsquo;节&rsquo;在质部，则立、节非韵。原本&lsquo;立&rsquo;作&lsquo;力&rsquo;，&lsquo;力&rsquo;在职部，力、节亦非韵。&rdquo;五谷尽收，则五味尽御于主，独断云：&ldquo;御者进也，凡饮食入于口曰御&rdquo;。不尽收则不尽御。《白虎通义&middot;谏诤》篇云：&ldquo;阴阳不调，五谷不熟，故王者为不尽味而食之。&rdquo;毕云：&ldquo;主、御为韵。&rdquo;王云：古音&lsquo;主&rsquo;在厚部，&lsquo;御&rsquo;在御部，则主、御非韵。&rdquo;一谷不收谓之馑，二谷不收谓之旱，俞云：&ldquo;按旱者不雨也，不得为二谷不收之名。疑&lsquo;旱&rsquo;乃&lsquo;罕&rsquo;字之误。一谷不收谓之馑，二谷不收谓之罕。馑也，罕也，皆稀少之谓。馑犹仅也，故《襄二十四年谷梁传》作&lsquo;一谷不升谓之嗛&rsquo;。嗛，犹歉也。然二谷不收谓之罕，其义正一律矣。&rdquo;三谷不收谓之凶，四谷不收谓之馈，毕云：&ldquo;《汉书&middot;食货志》云：&lsquo;负担馈饷&rsquo;，师古曰：&lsquo;馈亦馈字，言须馈饷&rsquo;。&rdquo;邵晋涵云：&ldquo;馈与匮通。郑注月令曰：&lsquo;匮，乏也。&rsquo;&rdquo;王云：&ldquo;须馈饷不得谓之馈，毕说非，邵说是也。&rdquo;五谷不收谓之饥。毕云：&ldquo;太平御览引作&lsquo;饥&rsquo;，误。此饥饿字。&rdquo;又毕本此下增&ldquo;五谷不熟，谓之大侵&rdquo;八字，云：&ldquo;八字旧脱，据艺文类聚增。《谷梁传》云：&lsquo;一谷不升谓之嗛，二谷不升谓之饥，三谷不升谓之馑，四谷不升谓之康，五谷不升谓之大侵。&rsquo;《尔雅》云&lsquo;谷不孰为饥，蔬不孰为馑，果不孰为荒&rsquo;，与此异。&rdquo;王云：&ldquo;既言五谷不收谓之饥，则不得又言五谷不熟谓之大侵。艺文类聚百谷部引墨子&lsquo;五谷不孰，谓之大侵&rsquo;者，乃涉上文引《谷梁传》&lsquo;五谷不升谓之大侵&rsquo;而衍，故太平御览时序部二十、百谷部一，引墨子皆无此八字。墨子所记本与《谷梁传》不同，不可强合也。下文&lsquo;饥则尽无禄&rsquo;，毕依类聚于&lsquo;饥&rsquo;下增&lsquo;大侵&rsquo;二字，亦御览所无。&rdquo;案：王说是也。释慧苑华严经音义二，引&ldquo;饥&rdquo;亦作&ldquo;饥&rdquo;，下无&ldquo;五谷不孰&rdquo;八字。岁馑，则仕者大夫以下皆损禄五分之一。旱，则损五分之二。凶，则损五分之三。馈，则损五分之四。饥，毕据艺文类聚增大侵&rdquo;二字，误，今不从。则尽无禄禀食而已矣。禀食，谓有稍食而无禄也。《说文&middot;㐭部》云：&ldquo;禀，赐谷也。&rdquo;周礼司士，郑注云：&ldquo;食，稍食也。&rdquo;又宫正注云：&ldquo;稍食禄禀。&rdquo;故凶饥存乎国，人君彻鼎食五分之五，曲礼郑注云：&ldquo;彻，去也。&rdquo;五分之五，义不可通，疑当作五分之三。玉藻云：&ldquo;诸侯日食特牲，朔月少牢。&rdquo;此五鼎则少牢也。以礼经考之，盖羊一、豕二、伦肤三、鱼四、腊五，五者各一鼎，彻其三者，去其牢肉，则唯食鱼腊，不特杀也。《白虎通义&middot;谏诤》篇云：&ldquo;《礼》曰：一谷不升彻鹑鷃，二谷不升彻凫雁，三谷不升彻雉兔，四谷不升损囿兽，五谷不升不备三牲。&rdquo;《白虎通》盖据天子而言。故云三牲。大荒不特杀，则不止不备而已。大夫彻县，周礼小胥云：&ldquo;卿大夫判县&rdquo;，郑注谓左右县。《曲礼》云：&ldquo;大夫无故不彻县&rdquo;，孔疏云：&ldquo;彻亦去也。&rdquo;士不入学，《周书&middot;籴匡》篇云：&ldquo;成年，馀子务艺；年俭，馀子务穑。&rdquo;是不入学也。君朝之衣不革制，君朝之衣，天子皮弁服，诸侯则冠弁服也。周礼司服云：&ldquo;眡朝则皮弁服&rdquo;，郑注云：&ldquo;视朝，视内外朝之事。皮弁之服，十五升白布衣，积素以为裳&rdquo;，又&ldquo;凡甸冠弁服&rdquo;，注云：&ldquo;冠弁委貌，其服缁布衣，亦积素以为裳，诸侯以为视朝之服&rdquo;，是也。《周书&middot;大匡》篇云：&ldquo;大荒祭服漱不制。&rdquo;朝服轻于祭服，不制明矣。苏云：&ldquo;革，改也。&rdquo;诸侯之客，四邻之使，雍食而不盛，毕云：&ldquo;&lsquo;雍食&rsquo;，疑一&lsquo;饔&rsquo;字。《说文》云：&lsquo;饔，孰食也&rsquo;。&rdquo;王云：&ldquo;&lsquo;雍食&rsquo;当为&lsquo;雍飧&rsquo;。周官外饔&lsquo;凡宾客之飧，饔飨食之事&rsquo;，郑注曰：&lsquo;飧，客始至之礼。饔，既将币之礼。&rsquo;飧饔即饔飧也。饔、雍古字通。&rdquo;案：王说是也。籴匡篇云：&ldquo;年俭，宾祭以中盛；年饥，则勤而不宾；大荒，宾旅设位有赐&rdquo;，与此略同。彻骖騑，毕云：&ldquo;高诱注吕氏春秋云：&lsquo;在中曰服，在边曰騑。&rsquo;&rdquo;涂不芸，《谷梁&middot;襄二十四年传》云：&ldquo;大侵之礼，廷道不除。&rdquo;范甯注云&ldquo;廷内道路不修除也。&rdquo;毕云：&ldquo;&lsquo;涂&rsquo;俗写从土，本书《非攻中》云：&lsquo;涂道之修远&rsquo;，只作&lsquo;涂&rsquo;。芸，</span>
      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_9'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=9;
  	var __dedeqrcode_aid=49;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',49)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',49)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(49);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/48.html'>《墨子闲诂》· 卷一 · 三辩</a> </li>
     <li>下一篇：<a href='/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/50.html'>《墨子闲诂》· 卷一 · 修身</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=49" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=49&title=《墨子闲诂》· 卷一 · 七患" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=49" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="49" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=49">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=49&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '49');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/handaizhihou/weijinnanbeichao/'>魏晋南北朝</a></li>
      
      <li><a href='/a/handaizhihou/suitang/'>隋唐</a></li>
      
      <li><a href='/a/handaizhihou/songming/'>宋明</a></li>
      <li><a href='/a/handaizhihou/weijinnanbeichao/qingdai/' class='thisclass'>清代</a></li>
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/46.html">《墨子闲诂》· 卷四 ·兼爱下</a>
       <p>子墨子言曰：仁人之事者，必务求兴天下之利，除天下之害。然...</p>
      </li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/49.html">《墨子闲诂》· 卷一 · 七患</a>
       <p>子墨子曰：国有七患。七患者何？城郭沟池不可守，而治宫室，...</p>
      </li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/50.html">《墨子闲诂》· 卷一 · 修身</a>
       <p>毕云：修治之字从彡，从肉者修脯字，经典假借多用此。 君子战...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/50.html">《墨子闲诂》· 卷一 · 修</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/49.html">《墨子闲诂》· 卷一 · 七</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/45.html">《红楼梦》· 薄命女偏逢</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/48.html">《墨子闲诂》· 卷一 · 三</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/47.html">《墨子闲诂》· 卷四 ·兼</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/46.html">《墨子闲诂》· 卷四 ·兼</a></li>
<li><a href="/a/handaizhihou/weijinnanbeichao/qingdai/2021/0810/44.html">《红楼梦》· 甄士隐梦幻</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
