<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>风景(5)_万婕源码网</title>
<meta name="keywords" content="" />
<meta name="description" content="风景" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<link href="/templets/default/style/picture.css" rel="stylesheet" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=73">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=73";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>
<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	var taget_obj = document.getElementById('_ajax_feedback');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/member/ajax_feedback.php");
	DedeXHTTP = null;
}
function checkSubmit(){
	if(document.feedback.msg.value!='') document.feedback.submit();
	else alert("评论内容不能为空！");
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
	DedeXHTTP = null;
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
	DedeXHTTP = null;
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}


  var shortname = ".html";
  var npage = 5;
	var totalpage = 9;
	var namehand = '73';
	var displaytype = 'st';
	var gtimer = null;
	//大图
	function dPlayBig()
	{
		var imgObj = document.getElementById("bigimg");
		window.open(imgObj.src);
	}
	//停止幻灯
	function dStopPlay()
	{
		if(gtimer) clearTimeout(gtimer);
		else dPlayNext();
	}
	//开始幻灯
	function dStartPlay()
	{
		if(npage!=totalpage) {
			gtimer = setTimeout("dPlayNext()",10000);
		}
	}

	//上一张
	function dPlayPre(){

	if(npage<2)
	{
			alert("这是第一页");
	}
	else
	{
			if(npage==2) {
				if(namehand!='') location.href = namehand+shortname;
				else location.href = "view.php?aid=73";
			} else if(displaytype=='st' && namehand!='') {
				location.href = namehand+"_"+(npage-1)+shortname;
			} else {
				location.href = "view.php?aid=73&pageno="+(npage-1);
			}
		}
	}

	//下一张
	function dPlayNext()
	{
		if(npage==totalpage) { alert("没有了哦"); }
		else
		{
			if(displaytype=='st' && namehand!='') location.href = namehand+"_"+(npage+1)+shortname;
			else location.href = "view.php?aid=73&pageno="+(npage+1);
		}
	}
-->
</script>
</head>
<body class="picboxview">

<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	
      	<li><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	<li class='hover'><a href='/a/tuji/' ><span>图集</span></a></li>
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
	<div class="pleft">
		<div class="place">
			<strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/tuji/'>图集</a>
		</div><!-- /place -->
		<div class="viewbox">
			<div class="title">
				<h2>风景(5)</h2>
			</div><!-- /title -->
			<div class="info">
				<small>时间:</small>2021-08-23 17:41<small>来源:</small>ICER <small>作者:</small>admin <small>点击:</small><script src="/plus/count.php?view=yes&aid=73&mid=1" type='text/javascript' language="javascript"></script>次
			</div><!-- /info -->
			<div class="picbox">

             
					<center>
						<a href='javascript:dPlayBig();' class='c1'>原始图片</a>

						<a href='javascript:dPlayPre();' class='c1'>上一张</a>

						<a href='javascript:dPlayNext();' class='c1'>下一张</a>

						<a href='javascript:dStopPlay();' class='c1'>自动 / 暂停播放</a>

					</center>
                	
             <!-- 如果使用的是多页单图模式(幻灯)，把href里的链接改为 javascript:dPlayNext(); 表示点击看下一页 -->
             
                <a href='http://www.dedecms.com/demoimg/uploads/allimg/2021-08/10164247-1-54F9.png' >
                  <img src='http://www.dedecms.com/demoimg/uploads/allimg/2021-08/10164247-1-54F9.png' id='bigimg'  width='800'  alt='截屏2021-08-10 下午4.28.52' border='0' />
                </a>
                <a href='http://www.dedecms.com/demoimg/uploads/allimg/2021-08/10164247-1-54F9.png' >
                	风景
                </a>
                

             <script language='javascript'>dStartPlay();</script>

			</div>
           	<div class="intro"><br />

            <div style="clear:both"></div>
            </div>
			<div class="newdigg" id="newdigg">
				<div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',73)">
					<div class="digg_act">顶一下</div>
					<div class="digg_num">(0)</div>
					<div class="digg_percent">
						<div class="digg_percent_bar"><span style="width:0%"></span></div>
						<div class="digg_percent_num">0%</div>
					</div>
				</div>
				<div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',73)">
					<div class="digg_act">踩一下</div>
					<div class="digg_num">(0)</div>
					<div class="digg_percent">
						<div class="digg_percent_bar"><span style="width:0%"></span></div>
						<div class="digg_percent_num">0%</div>
					</div>
				</div>
			</div>
			 <script language="javascript" type="text/javascript">getDigg(73);</script>

			<div class="boxoff">
				<strong>------分隔线----------------------------</strong>
			</div>
			<div class="handle">
				<div class="context">
					<ul>
						<li>上一篇：<a href='/a/tuji/2021/0810/72.html'>草原</a> </li>
						<li>下一篇：<a href='/a/tuji/2021/0810/74.html'>花草</a> </li>
					</ul>
				</div><!-- /context -->
				<div class="actbox">
					<ul>
						<li id="act-fav"><a href="/plus/stow.php?aid=73" target="_blank">收藏</a></li>
						<li id="act-err"><a href="/plus/erraddsave.php?aid=73&title=风景(5)" target="_blank">挑错</a></li>
						<li id="act-pus"><a href="/plus/recommend.php?aid=73" target="_blank">推荐</a></li>
						<li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
					</ul>
				</div><!-- /actbox -->
			</div><!-- /handle -->
		</div><!-- /viewbox -->

<!-- //AJAX评论区 -->
<!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="73" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=73">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=73&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '73');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->


	</div><!-- /pleft -->

	<div class="pright">
	    <div class="infos_userinfo">
 			
   	 	</div>

    	<div class="mt1">
			<dl class="tbox">
				<dt><strong>热点图集</strong></dt>
				<dd>
					<ul class="e3">
                    <li>
							<a href="/a/tuji/2021/0823/92.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/allimg/210823/1-210R31644030-L.jpg" alt="花草-"/></a>
							<a href="/a/tuji/2021/0823/92.html" class="title">花草-</a>
							<span class="intro">更新:2021-08-23</span>
						</li>
<li>
							<a href="/a/tuji/2021/0823/90.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/allimg/210823/1-210R31635230-L.jpg" alt="草原-"/></a>
							<a href="/a/tuji/2021/0823/90.html" class="title">草原-</a>
							<span class="intro">更新:2021-08-23</span>
						</li>
<li>
							<a href="/a/tuji/2021/0810/73.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R31613404J.jpg" alt="风景"/></a>
							<a href="/a/tuji/2021/0810/73.html" class="title">风景</a>
							<span class="intro">更新:2021-08-23</span>
						</li>
<li>
							<a href="/a/tuji/2021/0823/89.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/allimg/210823/1-210R31631210-L.jpg" alt="微距-"/></a>
							<a href="/a/tuji/2021/0823/89.html" class="title">微距-</a>
							<span class="intro">更新:2021-08-23</span>
						</li>
<li>
							<a href="/a/tuji/2021/0810/71.html" class="preview"><img src="http://www.dedecms.com/demoimg/uploads/210823/1-210R3160344Q3.jpg" alt="微距"/></a>
							<a href="/a/tuji/2021/0810/71.html" class="title">微距</a>
							<span class="intro">更新:2021-08-23</span>
						</li>


					</ul>
				</dd>
			</dl>
		</div>
		<div class="mt1">
			<dl class="tbox">
				<dt><strong>推荐图集</strong></dt>
				<dd>
					<ul class="e9">
                    
					</ul>
				</dd>
			</dl>
		</div>

	</div><!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->

</body>
</html>