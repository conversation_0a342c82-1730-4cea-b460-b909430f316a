<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>1909年、1927年、1937年南京城市历史地名数据集_万婕源码网</title>
<meta name="keywords" content="1909年,、,1927年,1937年,南京,城市,历史,地" />
<meta name="description" content="中华民国时期是中国近代史上一个重要的过渡期和转型期 [1] ，城市地名作为一种文化形态，见证了城市的发展变迁，对探索不同时期空间格局具有重大意义，地名的变迁记录了城市演" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=52">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=52";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li class='hover'><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xiangguanziliao/'>相关资料</a> > <a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>1909年、1927年、1937年南京城市历史地名数据集</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:21<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=52&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">中华民国时期是中国近代史上一个重要的过渡期和转型期 [1] ，城市地名作为一种文化形态，见证了城市的发展变迁，对探索不同时期空间格局具有重大意义，地名的变迁记录了城市演</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	中华民国时期是中国近代史上一个重要的过渡期和转型期<sup class="reference" id="cite_ref-ref1_1-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1909%E5%B9%B4%E3%80%811927%E5%B9%B4%E3%80%811937%E5%B9%B4%E5%8D%97%E4%BA%AC%E5%9F%8E%E5%B8%82%E5%8E%86%E5%8F%B2%E5%9C%B0%E5%90%8D%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref1-1" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[1]</a></sup>，城市地名作为一种文化形态，见证了城市的发展变迁，对探索不同时期空间格局具有重大意义，地名的变迁记录了城市演变、民族变迁的过程，蕴含了丰富的历史文化信息<sup class="reference" id="cite_ref-ref2_2-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1909%E5%B9%B4%E3%80%811927%E5%B9%B4%E3%80%811937%E5%B9%B4%E5%8D%97%E4%BA%AC%E5%9F%8E%E5%B8%82%E5%8E%86%E5%8F%B2%E5%9C%B0%E5%90%8D%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref2-2" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[2]</a></sup>。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	在地名学理论的研究中，现代地名学理论方法等研究成果较为丰富，主要研究内容包括地名起源、地名演变、地名分类、地名功能等<sup class="reference" id="cite_ref-ref3_3-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1909%E5%B9%B4%E3%80%811927%E5%B9%B4%E3%80%811937%E5%B9%B4%E5%8D%97%E4%BA%AC%E5%9F%8E%E5%B8%82%E5%8E%86%E5%8F%B2%E5%9C%B0%E5%90%8D%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref3-3" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[3]</a></sup>。在地名分类方面，2001年国家民政部提出的地名分类国家标准，按照地名的地理属性建立了地名四级分类体系<sup class="reference" id="cite_ref-ref4_4-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1909%E5%B9%B4%E3%80%811927%E5%B9%B4%E3%80%811937%E5%B9%B4%E5%8D%97%E4%BA%AC%E5%9F%8E%E5%B8%82%E5%8E%86%E5%8F%B2%E5%9C%B0%E5%90%8D%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref4-4" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[4]</a></sup>。对于地名标准化，王际桐从地名国家标准化、罗马化和国际标准化等方面阐述了地名标准化的的原则、要求和建议<sup class="reference" id="cite_ref-ref5_5-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1909%E5%B9%B4%E3%80%811927%E5%B9%B4%E3%80%811937%E5%B9%B4%E5%8D%97%E4%BA%AC%E5%9F%8E%E5%B8%82%E5%8E%86%E5%8F%B2%E5%9C%B0%E5%90%8D%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref5-5" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[5]</a></sup>。城市地名的历史数据为研究城市的演变进程提供了数据支撑。南京作为中华民国的首都，且是我国近现代史上第一个具有完整现代城市规划的城市，其城市空间结构及历史演变影响的重要性不言而喻。因此，本文以民国南京古旧地图作为工作底图、城市地名为研究对象，参考&ldquo;民国良志&rdquo;之称的《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E9%A6%96%E9%83%BD%E5%BF%97&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="首都志（页面不存在）">首都志</a>》，查阅并收集地图和地方志中的地名数据，数字化民国南京3个时期的古旧地图中的地名数据，将所有地名保存为点状矢量数据。<br style="padding: 0px; margin: 0px;" />
	&nbsp;</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	陆师学堂奉两江总督之命所测绘的金陵省城全图，主图比例尺为1:10000，附带秦淮河以南、中华门东西两侧区域的放大一倍图，即比例尺为1:5000。1927年4月出版的《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E6%9C%80%E6%96%B0%E5%8D%97%E4%BA%AC%E5%85%A8%E5%9B%BE&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="最新南京全图（页面不存在）">最新南京全图</a>》，主图比例尺为1:10000，附图为下关及浦口地区，要素明确。苏甲荣编制的《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E6%96%B0%E5%8D%97%E4%BA%AC%E5%9C%B0%E5%9B%BE&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="新南京地图（页面不存在）">新南京地图</a>》（1937年订正版），主图的比例尺为1:20000，附带中山门至汤山图比例尺为1:50000。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	然而，地图有限的范围所包含的地名是有限的、不完整的，且各类地图在绘制时其功能用途决定其上标注的地名是有选择性的。因此，仅提取地图中的地名完全不够。而能够描述历史时期人物、事件、地名等信息的文字性史料文献，尤其是地方志资料，是研究历史时期城市发展的重要资料来源。基于此，本文以古旧地图作为基础，收集汇总了对应时期的历史档案资料，从中摘录并整理出1909、1927及1937年的地名，弥补地图中地名不全的缺陷。对于1909年，本文参考了《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E7%BB%AD%E7%BA%82%E6%B1%9F%E5%AE%81%E5%BA%9C%E5%BF%97%EF%BC%88%E5%90%8C%E6%B2%BB%EF%BC%89&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="续纂江宁府志（同治）（页面不存在）">续纂江宁府志（同治）</a>》《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E4%B8%8A%E6%B1%9F%E4%B8%A4%E5%8E%BF%E5%BF%97%EF%BC%88%E5%90%8C%E6%B2%BB%EF%BC%89&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="上江两县志（同治）（页面不存在）">上江两县志（同治）</a>》《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E9%87%91%E9%99%B5%E6%9D%82%E5%BF%97&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="金陵杂志（页面不存在）">金陵杂志</a>》；1927年，本文主要参考的文献资料为陈迺勋编著的《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E6%96%B0%E4%BA%AC%E5%A4%87%E4%B9%98&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="新京备乘（页面不存在）">新京备乘</a>》，该书是我国早期城市志的一个代表；1937年，本文参考了叶楚伧和柳诒征主编、王焕镳编纂的《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E9%A6%96%E9%83%BD%E5%BF%97&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="首都志（页面不存在）">首都志</a>》，是中国最早的城市志、城市史。表1是数据来源信息。</p>
<div style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">
	<br style="padding: 0px; margin: 0px;" />
	<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
		将3幅古旧地图，在地理信息系统软件ArcGIS平台下，进行配准及手动数字化。</p>
	<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
		将1:25000的江苏省南京市地形图作为本文的标准底图，在进行空间配准时，基于古旧地图上的点数据，依据城墙、城门等不同历史时期变化不大的点进行配准。选取GCS_Beijing_1954坐标系，采集到的点数据直接定位，线状数据和面状数据选取其重心作为定位点，保存为点数据。</p>
	<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
		通过从古旧地图和史料文献中摘录的地名，收集其地址及其他属性信息。此外，利用相关学者的论文并进行实地调研修缮和补充属性数据，而后整理与归纳，完成地名属性资料的汇录。在对地方志中的部分地名进行空间位置考证的过程中，利用百度地图作为现今的地理位置参考，根据其和路网的空间位置关系，从而完成空间配准。在进行地名的空间定位时，通过确定地名的拓扑关系、方位关系和距离关系等信息来判断地名的空间位置。方位指地理空间中的东西南北（基本方位）、上下左右（相对方位）等方向概念。拓扑关系指图形元素之间的空间结构关系，包括拓扑邻接、拓扑关联、拓扑包含几种类型。距离关系对地名数据进行定位，主要依赖于两地理实体之间的距离<sup class="reference" id="cite_ref-ref3_3-1" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1909%E5%B9%B4%E3%80%811927%E5%B9%B4%E3%80%811937%E5%B9%B4%E5%8D%97%E4%BA%AC%E5%9F%8E%E5%B8%82%E5%8E%86%E5%8F%B2%E5%9C%B0%E5%90%8D%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref3-3" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[3]</a></sup>。</p>
</div>
<br />

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_12'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=12;
  	var __dedeqrcode_aid=52;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',52)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',52)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(52);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/51.html'>1901–2014年黄土高原1 km分辨率月均气温和月降水</a> </li>
     <li>下一篇：<a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/53.html'>1952年安徽省人民政府三年财政经济工作报告</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=52" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=52&title=1909年、1927年、1937年南京城市历史地名数据集" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=52" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="52" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=52">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=52&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '52');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      <li><a href='/a/xiangguanziliao/xiangguanjianjie/' class='thisclass'>相关简介</a></li>
      <li><a href='/a/xiangguanziliao/xiangguanziliao/'>相关资料</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      
     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/57.html">厦门中国台湾同志会宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/58.html">General notes about pre-Qin and</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/55.html">商务部关于实施“振兴老</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/51.html">1901–2014年黄土高原1 km分</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/56.html">厦门中国台湾同志会宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/53.html">1952年安徽省人民政府三年</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/52.html">1909年、1927年、1937年南京</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/54.html">厦门中国台湾同志会宣言</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
