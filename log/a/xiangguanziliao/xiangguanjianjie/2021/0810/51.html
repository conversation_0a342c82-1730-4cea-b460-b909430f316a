<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>1901–2014年黄土高原1 km分辨率月均气温和月降水_万婕源码网</title>
<meta name="keywords" content="1901,–,2014年,黄土高原,分辨率,月均,气温," />
<meta name="description" content="气候变化已成为全球科学研究的热点，显著影响着人类的生存和发展。黄土高原地区（33434116N，1005411433E）横贯黄河中上游，年降水量从西北部的200 mm到东南部750 mm [1] ，年平均气温从西" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=51">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=51";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li class='hover'><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xiangguanziliao/'>相关资料</a> > <a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>1901–2014年黄土高原1 km分辨率月均气温和月降水</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:21<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=51&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">气候变化已成为全球科学研究的热点，显著影响着人类的生存和发展。黄土高原地区（33434116N，1005411433E）横贯黄河中上游，年降水量从西北部的200 mm到东南部750 mm [1] ，年平均气温从西</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	气候变化已成为全球科学研究的热点，显著影响着人类的生存和发展。黄土高原地区（33&deg;43&prime;&ndash;41&deg;16&prime;N，100&deg;54&prime;&ndash;114&deg;33&prime;E）横贯黄河中上游，年降水量从西北部的200 mm到东南部750 mm<sup class="reference" id="cite_ref-ref1_1-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref1-1" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[1]</a></sup>，年平均气温从西北部3.6 ℃到东南部14.3 ℃，被公认为对气候变化敏感的半干旱到半湿润的过渡区<sup class="reference" id="cite_ref-ref2_2-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref2-2" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[2]</a></sup>。作为世界上最大的黄土地区，黄土高原面积约为67.8万平方公里，在生态环境和社会经济等方面对中国的发展有着重要作用。在过去的几十年间，针对黄土高原地区的气候变化学界开展了诸多研究，这些研究均揭示了其年降水量减少、气温升高的趋势<sup class="reference" id="cite_ref-ref5_3-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref5-3" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[3]</a></sup><sup class="reference" id="cite_ref-ref4_4-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref4-4" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[4]</a></sup><sup class="reference" id="cite_ref-ref3_5-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref3-5" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[5]</a></sup>。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	多个气候研究组织先后发布了全球到次大陆尺度的多种长期气候网格数据集，其具有时序长、气候要素丰富等特点，但大多空间分辨率偏低，在描绘区域小尺度气候信息时存在较大的偏差<sup class="reference" id="cite_ref-ref6_6-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref6-6" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[6]</a></sup>，阻碍了其在小地理尺度上的应用。例如英国东英格利亚大学气候研究中心（Climatic Research Unit，CRU）<sup class="reference" id="cite_ref-ref7_7-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref7-7" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[7]</a></sup>数据，其空间分辨率为0.5&deg;，在小地理尺度上表达复杂地形、地表特征及气候系统中其他过程的能力有限<sup class="reference" id="cite_ref-ref8_8-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref8-8" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[8]</a></sup>。因此，为了提高这些数据集在中小尺度地理研究中的应用能力，将其与地面高空间分辨率的参考气候数据集（包含气象站记录（校准）和观测记录的地形效应）相结合以实现空间降尺度，一方面可以减少原始数据的不确定性，另一方面可以实现空间分辨率的提高<sup class="reference" id="cite_ref-ref9_9-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref9-9" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[9]</a></sup><sup class="reference" id="cite_ref-ref8_8-1" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref8-8" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[8]</a></sup>。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	气候数据空间降尺度方法主要有统计降尺度和动力降尺度两类。与统计降尺度相比，动力降尺度需要大量参数和较多计算资源驱动<sup class="reference" id="cite_ref-ref10_10-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref10-10" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[10]</a></sup>，而且有时不能真实地反映小尺度上的气候变化特征<sup class="reference" id="cite_ref-ref12_11-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref12-11" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[11]</a></sup><sup class="reference" id="cite_ref-ref11_12-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref11-12" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[12]</a></sup>，因此，统计降尺度往往应用更加广泛。常用的统计降尺度法有Delta法和线性回归法。线性回归法是在历史时期建立网格数据与站点观测数据之间的线性关系，并将这种关系应用到未来时期的网格数据<sup class="reference" id="cite_ref-ref13_13-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref13-13" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[13]</a></sup>，以生成多种未来气候数据集，但其一般仍应用在站点尺度。Delta法使用低空间分辨率的月气候数据和高空间分辨率的参考气候数据作为输入数据，与直接插值不同，该方法可引入地形地貌对气候的影响<sup class="reference" id="cite_ref-ref9_9-1" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/1901%E2%80%932014%E5%B9%B4%E9%BB%84%E5%9C%9F%E9%AB%98%E5%8E%9F1_km%E5%88%86%E8%BE%A8%E7%8E%87%E6%9C%88%E5%9D%87%E6%B0%94%E6%B8%A9%E5%92%8C%E6%9C%88%E9%99%8D%E6%B0%B4%E9%87%8F%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref9-9" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[9]</a></sup>。因此，使用Delta法对网格气候数据进行降尺度可得到小地理尺度上精准的气候数据。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	本数据集采用CRU发布的全球0.5&deg;气候数据集和国家生态系统观测研究网络（CNERN）发布的中国区高分辨率气候数据集，通过Delta空间降尺度方案在黄土高原地区降尺度生成，可应用于中小地理尺度的环境科学研究，为研究黄土高原地区生态环境、水文水资源提供气候数据支撑。</p>

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_11'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=11;
  	var __dedeqrcode_aid=51;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',51)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',51)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(51);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：没有了 </li>
     <li>下一篇：<a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/52.html'>1909年、1927年、1937年南京城市历史地名数据集</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=51" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=51&title=1901–2014年黄土高原1 km分辨率月均气温和月降水" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=51" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="51" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=51">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=51&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '51');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      <li><a href='/a/xiangguanziliao/xiangguanjianjie/' class='thisclass'>相关简介</a></li>
      <li><a href='/a/xiangguanziliao/xiangguanziliao/'>相关资料</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      
     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/57.html">厦门中国台湾同志会宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/58.html">General notes about pre-Qin and</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/55.html">商务部关于实施“振兴老</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/51.html">1901–2014年黄土高原1 km分</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/56.html">厦门中国台湾同志会宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/53.html">1952年安徽省人民政府三年</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/52.html">1909年、1927年、1937年南京</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/54.html">厦门中国台湾同志会宣言</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
