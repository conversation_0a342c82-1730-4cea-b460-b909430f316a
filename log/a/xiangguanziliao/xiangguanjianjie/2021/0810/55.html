<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>商务部关于实施“振兴老字号工程”的通知/附件_万婕源码网</title>
<meta name="keywords" content="商务部,关于,实施,“,振兴,老字号,工程,”,的," />
<meta name="description" content="一、名称 [ 编辑 ] 中华老字号 China Time-honored Brand 二、定义 [ 编辑 ] 历史悠久，拥有世代传承的产品、技艺或服务，具有鲜明的中华民族传统文化背景和深厚的文化底蕴，取得社会广泛" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<style type="text/css">
/* 小仙元码暗色主题样式 - 文章详情页 */
* { box-sizing: border-box; }
html { background: #0a0a0a; }
body {
    font: 14px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: #fff !important;
    line-height: 1.6;
}
a { color: #00d4ff !important; text-decoration: none; transition: all 0.3s ease; }
a:hover { color: #0099cc !important; }

/* 面包屑导航 */
.position {
    background: #1a1a1a !important;
    padding: 15px 20px !important;
    border-bottom: 1px solid #333 !important;
    margin-bottom: 20px !important;
}
.position a { color: #00d4ff !important; }

/* 文章容器 */
.w960 { background: transparent !important; }
.tbox {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    margin-bottom: 30px !important;
}
.tbox dt {
    background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%) !important;
    border: none !important;
    height: 50px !important;
    border-radius: 15px 15px 0 0 !important;
}
.tbox dt strong {
    color: #00d4ff !important;
    line-height: 50px !important;
    font-size: 1.3rem !important;
    padding-left: 20px !important;
}
.tbox dd {
    background: #1a1a1a !important;
    border: none !important;
    padding: 30px !important;
    border-radius: 0 0 15px 15px !important;
}

/* 文章标题 */
.arc_title {
    color: #fff !important;
    font-size: 2.5rem !important;
    margin-bottom: 20px !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 15px !important;
}

/* 文章信息 */
.arc_info {
    color: #ccc !important;
    margin-bottom: 20px !important;
    padding: 15px 0 !important;
    border-bottom: 1px solid #333 !important;
}
.arc_info span { margin-right: 20px !important; }

/* 文章内容 */
.arc_content, .arc_body {
    color: #ccc !important;
    line-height: 1.8 !important;
    margin: 30px 0 !important;
}
.arc_content h1, .arc_content h2, .arc_content h3, .arc_content h4, .arc_content h5, .arc_content h6,
.arc_body h1, .arc_body h2, .arc_body h3, .arc_body h4, .arc_body h5, .arc_body h6 {
    color: #fff !important;
    margin: 30px 0 20px 0 !important;
}
.arc_content h2, .arc_body h2 { color: #00d4ff !important; }
.arc_content p, .arc_body p { margin-bottom: 20px !important; }
.arc_content img, .arc_body img {
    max-width: 100% !important;
    border-radius: 8px !important;
    margin: 20px 0 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* 文章缩略图 */
.arc_pic {
    text-align: center !important;
    margin: 20px 0 !important;
}
.arc_pic img {
    max-width: 100% !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

/* 标签 */
.arc_tags { margin-top: 30px !important; }
.arc_tags a, .tags a {
    background: #333 !important;
    color: #00d4ff !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    text-decoration: none !important;
    display: inline-block !important;
    margin: 5px 10px 5px 0 !important;
    transition: all 0.3s ease !important;
}
.arc_tags a:hover, .tags a:hover {
    background: #00d4ff !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
}

/* 侧边栏 */
.sidebar { background: transparent !important; }
.sidebar .tbox { margin-bottom: 25px !important; }
.sidebar ul li {
    background: none !important;
    border-bottom: 1px solid #333 !important;
    padding: 8px 0 !important;
}
.sidebar ul li a { color: #ccc !important; }
.sidebar ul li a:hover { color: #00d4ff !important; }

/* 评论区域 */
.feedback {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    padding: 30px !important;
    margin-top: 30px !important;
}
.feedback h3 {
    color: #00d4ff !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 10px !important;
    margin-bottom: 20px !important;
}

/* 评论表单 */
.feedback textarea {
    background: #333 !important;
    border: 1px solid #555 !important;
    color: #fff !important;
    border-radius: 8px !important;
    padding: 15px !important;
    width: 100% !important;
    resize: vertical !important;
}
.feedback textarea:focus {
    border-color: #00d4ff !important;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2) !important;
    outline: none !important;
}

/* 按钮样式 */
.btn, input[type="submit"], input[type="button"], button {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
    border: none !important;
    color: #fff !important;
    border-radius: 25px !important;
    padding: 12px 24px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: bold !important;
}
.btn:hover, input[type="submit"]:hover, input[type="button"]:hover, button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .w960 { padding: 0 15px !important; }
    .arc_title { font-size: 1.8rem !important; }
    .tbox dd { padding: 20px !important; }
    .arc_info { flex-direction: column !important; gap: 10px !important; }
}
</style>
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=55">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=55";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li class='hover'><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xiangguanziliao/'>相关资料</a> > <a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>商务部关于实施“振兴老字号工程”的通知/附件</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:20<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=55&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">一、名称 [ 编辑 ] 中华老字号 China Time-honored Brand 二、定义 [ 编辑 ] 历史悠久，拥有世代传承的产品、技艺或服务，具有鲜明的中华民族传统文化背景和深厚的文化底蕴，取得社会广泛</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="一、名称" style="padding: 0px; margin: 0px;">一、名称</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=%E5%95%86%E5%8A%A1%E9%83%A8%E5%85%B3%E4%BA%8E%E5%AE%9E%E6%96%BD%E2%80%9C%E6%8C%AF%E5%85%B4%E8%80%81%E5%AD%97%E5%8F%B7%E5%B7%A5%E7%A8%8B%E2%80%9D%E7%9A%84%E9%80%9A%E7%9F%A5/%E9%99%84%E4%BB%B6%E4%BA%8C&amp;action=edit&amp;section=1" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：一、名称">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	中华老字号 China Time-honored Brand<span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span></p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="二、定义" style="padding: 0px; margin: 0px;">二、定义</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=%E5%95%86%E5%8A%A1%E9%83%A8%E5%85%B3%E4%BA%8E%E5%AE%9E%E6%96%BD%E2%80%9C%E6%8C%AF%E5%85%B4%E8%80%81%E5%AD%97%E5%8F%B7%E5%B7%A5%E7%A8%8B%E2%80%9D%E7%9A%84%E9%80%9A%E7%9F%A5/%E9%99%84%E4%BB%B6%E4%BA%8C&amp;action=edit&amp;section=2" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：二、定义">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	历史悠久，拥有世代传承的产品、技艺或服务，具有鲜明的中华民族传统文化背景和深厚的文化底蕴，取得社会广泛认同，形成良好信誉的品牌。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="三、认定范围" style="padding: 0px; margin: 0px;">三、认定范围</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=%E5%95%86%E5%8A%A1%E9%83%A8%E5%85%B3%E4%BA%8E%E5%AE%9E%E6%96%BD%E2%80%9C%E6%8C%AF%E5%85%B4%E8%80%81%E5%AD%97%E5%8F%B7%E5%B7%A5%E7%A8%8B%E2%80%9D%E7%9A%84%E9%80%9A%E7%9F%A5/%E9%99%84%E4%BB%B6%E4%BA%8C&amp;action=edit&amp;section=3" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：三、认定范围">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	中华人民共和国境内的有关单位（企业或组织）。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="四、认定条件" style="padding: 0px; margin: 0px;">四、认定条件</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=%E5%95%86%E5%8A%A1%E9%83%A8%E5%85%B3%E4%BA%8E%E5%AE%9E%E6%96%BD%E2%80%9C%E6%8C%AF%E5%85%B4%E8%80%81%E5%AD%97%E5%8F%B7%E5%B7%A5%E7%A8%8B%E2%80%9D%E7%9A%84%E9%80%9A%E7%9F%A5/%E9%99%84%E4%BB%B6%E4%BA%8C&amp;action=edit&amp;section=4" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：四、认定条件">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	1、拥有商标所有权或使用权。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	2、品牌创立于1956年（含）以前。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	3、传承独特的产品、技艺或服务。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	4、有传承中华民族优秀传统的企业文化。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	5、具有中华民族特色和鲜明的地域文化特征，具有历史价值和文化价值。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	6、具有良好信誉，得到广泛的社会认同和赞誉。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	7、国内资本及港澳台地区资本相对控股，经营状况良好，且具有较强的可持续发展能力。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="五、认定方式" style="padding: 0px; margin: 0px;">五、认定方式</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=%E5%95%86%E5%8A%A1%E9%83%A8%E5%85%B3%E4%BA%8E%E5%AE%9E%E6%96%BD%E2%80%9C%E6%8C%AF%E5%85%B4%E8%80%81%E5%AD%97%E5%8F%B7%E5%B7%A5%E7%A8%8B%E2%80%9D%E7%9A%84%E9%80%9A%E7%9F%A5/%E9%99%84%E4%BB%B6%E4%BA%8C&amp;action=edit&amp;section=5" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：五、认定方式">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	1、由商务部牵头设立&ldquo;中华老字号振兴发展委员会&rdquo;（以下简称振兴委员会），全面负责&ldquo;中华老字号&rdquo;的认定和相关工作。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	2、中华老字号振兴发展委员会下设秘书处、专家委员会。秘书处设在商务部商业改革发展司，负责振兴委员会的组织、协调和日常管理工作。专家委员会由各行业专家、法律专家、商标专家、品牌专家、企业管理专家、质量专家、历史学家等组成，主要负责&ldquo;中华老字号&rdquo;的评审，并参与相关工作的论证。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	3、原经有关部门认定的&ldquo;中华老字号&rdquo;要重新参加认定。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="六、认定程序" style="padding: 0px; margin: 0px;">六、认定程序</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=%E5%95%86%E5%8A%A1%E9%83%A8%E5%85%B3%E4%BA%8E%E5%AE%9E%E6%96%BD%E2%80%9C%E6%8C%AF%E5%85%B4%E8%80%81%E5%AD%97%E5%8F%B7%E5%B7%A5%E7%A8%8B%E2%80%9D%E7%9A%84%E9%80%9A%E7%9F%A5/%E9%99%84%E4%BB%B6%E4%BA%8C&amp;action=edit&amp;section=6" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：六、认定程序">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	具备&ldquo;中华老字号&rdquo;认定条件的单位，向所在地市级商务主管部门申报，并由省级商务主管部门（含计划单列市商务主管部门，下同）审核后报振兴委员会认定。程序包括：提出申请、资料提交、调查鉴别、认定评审、公示、做出决定、复核、注册存档、核发证书等。具体步骤：</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	1、提出申请：有关单位根据自身情况填写申报表，并报所在地市级商务主管部门。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	2、资料提交：所在地市级商务主管部门对提交的申请进行初评，确认申请有效的，指导申报单位按照规定格式提交有关资料，并报所在省级商务主管部门。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	3、调查鉴别：省级商务主管部门组织有关机构、专家对申报单位提交的资料进行调查与鉴别，并提出初步评估意见报振兴委员会。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	4、认定评审：振兴委员会组织专家对资料进行分析，必要时对有关内容进行现场调研，提出评审意见，撰写认定报告。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	5、公示：在有关媒体公示拟认定为&ldquo;中华老字号&rdquo;的企业和品牌名单，任何单位或个人对名单有不同意见的，均可向振兴委员会提出异议。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	6、做出决定：拟认定为&ldquo;中华老字号&rdquo;的企业和品牌在公示期间无异议或者异议不成立的，由振兴委员会做出决定，认定为&ldquo;中华老字号&rdquo;。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	7、复核：申报单位对认定结果有疑义的，可向振兴委员会提出复核，复核结果在接到复核申请后30天内做出。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	8、注册存档：认定过程涉及的所有资料均由振兴委员会存档保留，并负责管理。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	9、核发证书：对通过认定的&ldquo;中华老字号&rdquo;以商务部的名义颁发牌匾和证书。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="七、动态管理" style="padding: 0px; margin: 0px;">七、动态管理</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=%E5%95%86%E5%8A%A1%E9%83%A8%E5%85%B3%E4%BA%8E%E5%AE%9E%E6%96%BD%E2%80%9C%E6%8C%AF%E5%85%B4%E8%80%81%E5%AD%97%E5%8F%B7%E5%B7%A5%E7%A8%8B%E2%80%9D%E7%9A%84%E9%80%9A%E7%9F%A5/%E9%99%84%E4%BB%B6%E4%BA%8C&amp;action=edit&amp;section=7" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：七、动态管理">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	1、&ldquo;中华老字号&rdquo;所在单位须于每年12月31日前向振兴委员会提交上一年度经营情况的报告，由振兴委员会审核备案。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	2、&ldquo;中华老字号&rdquo;所在单位出现严重的违法违规、失信行为，或未按规定提交年度经营情况报告的，经振兴委员会核定后责令其整改。6个月内未见明显效果的，振兴委员会可以暂停或取消相应的&ldquo;中华老字号&rdquo;称号，并予以公示。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	有关&ldquo;中华老字号&rdquo;的具体管理办法另行制定。</p>

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_15'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=15;
  	var __dedeqrcode_aid=55;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',55)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',55)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(55);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/54.html'>厦门中国台湾同志会宣言 (1925年4月18日)</a> </li>
     <li>下一篇：<a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/56.html'>厦门中国台湾同志会宣言 (1925年4月24日)</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=55" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=55&title=商务部关于实施“振兴老字号工程”的通知/附件" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=55" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="55" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=55">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=55&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '55');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      <li><a href='/a/xiangguanziliao/xiangguanjianjie/' class='thisclass'>相关简介</a></li>
      <li><a href='/a/xiangguanziliao/xiangguanziliao/'>相关资料</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      
     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/57.html">厦门中国台湾同志会宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/58.html">General notes about pre-Qin and</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/55.html">商务部关于实施“振兴老</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/51.html">1901–2014年黄土高原1 km分</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/56.html">厦门中国台湾同志会宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/53.html">1952年安徽省人民政府三年</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/52.html">1909年、1927年、1937年南京</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/54.html">厦门中国台湾同志会宣言</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
