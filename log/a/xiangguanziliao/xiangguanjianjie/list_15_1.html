<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>相关资料  /  相关简介_万婕源码网</title>
<meta name="keywords" content="" />
<meta name="description" content="" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/list.php?tid=15">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/list.php?tid=15";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
</head>
<body class="articlelist">
<style type="text/css">
/* 小仙元码暗色主题 - 头部样式 */
.header_top {
    background: #1a1a1a !important;
    border-bottom: 1px solid #333 !important;
    padding: 10px 0 !important;
}
.header_top .time {
    color: #00d4ff !important;
    font-weight: bold !important;
}
.header_top .toplinks {
    color: #ccc !important;
}
.header_top .toplinks a {
    color: #ccc !important;
    transition: color 0.3s ease !important;
}
.header_top .toplinks a:hover {
    color: #00d4ff !important;
}

.header {
    background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%) !important;
    border-bottom: 2px solid #00d4ff !important;
}
.header .title h1 a img {
    filter: brightness(1.2) !important;
}

/* 导航菜单 */
.module {
    background: transparent !important;
    border: none !important;
}
#navMenu {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-radius: 10px !important;
    padding: 0 20px !important;
}
#navMenu ul li a {
    color: #ccc !important;
    background: transparent !important;
    border: none !important;
    padding: 15px 20px !important;
    border-radius: 8px !important;
    margin: 5px !important;
    transition: all 0.3s ease !important;
}
#navMenu ul li a:hover, #navMenu ul li.hover a {
    background: #00d4ff !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
}

/* 搜索框 */
.search {
    background: transparent !important;
}
.search .form {
    background: #333 !important;
    border: 1px solid #555 !important;
    border-radius: 25px !important;
    padding: 5px !important;
}
.search .form h4 {
    color: #00d4ff !important;
    background: transparent !important;
}
.search .form input[type="text"] {
    background: transparent !important;
    border: none !important;
    color: #fff !important;
    padding: 8px 15px !important;
}
.search .form input[type="submit"] {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
    border: none !important;
    color: #fff !important;
    border-radius: 20px !important;
    padding: 8px 15px !important;
    cursor: pointer !important;
}
</style>

<div class="header_top">
    <div class="w960 center">
     <span id="time" class="time">小仙元码 - 精品游戏资源分享平台</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div>
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li class='hover'><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
 <div class="pleft">
  <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xiangguanziliao/'>相关资料</a> > <a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a> </div>
  <!-- /place -->
  <div class="listbox">
   <ul class="e2">
    <li> <a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/51.html' class='preview'><img src='http://www.dedecms.com/demoimg/uploads/210823/1-210R31F639108.jpg'/></a>
     [<b><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></b>] <a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/51.html" class="title">1901–2014年黄土高原1 km分辨率月均气温和月降水</a> <span class="info"> <small>日期：</small>2021-08-23 17:21:51 <small>点击：</small>163 <small>好评：</small>0 </span>
     <p class="intro"> 气候变化已成为全球科学研究的热点，显著影响着人类的生存和发展。黄土高原地区（33434116N，1005411433E）横贯黄河中上游，年降水量从西北部的200 mm到东南部750 mm [1] ，年平均气温从西... </p>
    </li><li> <a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/52.html' class='preview'><img src='http://www.dedecms.com/demoimg/uploads/210823/1-210R31FQc17.jpg'/></a>
     [<b><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></b>] <a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/52.html" class="title">1909年、1927年、1937年南京城市历史地名数据集</a> <span class="info"> <small>日期：</small>2021-08-23 17:21:26 <small>点击：</small>63 <small>好评：</small>0 </span>
     <p class="intro"> 中华民国时期是中国近代史上一个重要的过渡期和转型期 [1] ，城市地名作为一种文化形态，见证了城市的发展变迁，对探索不同时期空间格局具有重大意义，地名的变迁记录了城市演... </p>
    </li><li> <a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/53.html' class='preview'><img src='http://www.dedecms.com/demoimg/uploads/210823/1-210R3160344Q3.jpg'/></a>
     [<b><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></b>] <a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/53.html" class="title">1952年安徽省人民政府三年财政经济工作报告</a> <span class="info"> <small>日期：</small>2021-08-23 17:21:12 <small>点击：</small>97 <small>好评：</small>0 </span>
     <p class="intro"> 主席、各位代表、各位同志： ﻿ 我完全同意曾希圣主席的报告。现在我将安徽三年来财政经济工作的情况，作报告如下： ﻿ 第一、农业生产：由于我们在全省完成了土地改革，废除了... </p>
    </li><li> <a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/54.html' class='preview'><img src='http://www.dedecms.com/demoimg/uploads/210823/1-210R31613404J.jpg'/></a>
     [<b><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></b>] <a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/54.html" class="title">厦门中国台湾同志会宣言 (1925年4月18日)</a> <span class="info"> <small>日期：</small>2021-08-23 17:21:03 <small>点击：</small>58 <small>好评：</small>0 </span>
     <p class="intro"> 五月九日已迫近了，大逆非人道之二十一个条，尚未撤废；旅大期满后，也已经两年了。中国的同胞们！我们台湾人也是汉民族。我们的祖先，是福建、漳州、泉州、广东、潮州的出身... </p>
    </li><li> <a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/55.html' class='preview'><img src='http://www.dedecms.com/demoimg/uploads/210823/1-210R31H045H9.jpg'/></a>
     [<b><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></b>] <a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/55.html" class="title">商务部关于实施“振兴老字号工程”的通知/附件</a> <span class="info"> <small>日期：</small>2021-08-23 17:20:22 <small>点击：</small>165 <small>好评：</small>0 </span>
     <p class="intro"> 一、名称 [ 编辑 ] 中华老字号 China Time-honored Brand 二、定义 [ 编辑 ] 历史悠久，拥有世代传承的产品、技艺或服务，具有鲜明的中华民族传统文化背景和深厚的文化底蕴，取得社会广泛... </p>
    </li><li> <a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/56.html' class='preview'><img src='http://www.dedecms.com/demoimg/uploads/210823/1-210R31F612G6.jpg'/></a>
     [<b><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></b>] <a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/56.html" class="title">厦门中国台湾同志会宣言 (1925年4月24日)</a> <span class="info"> <small>日期：</small>2021-08-23 17:20:12 <small>点击：</small>125 <small>好评：</small>0 </span>
     <p class="intro"> 国耻！国耻！莫忘国耻！旧事重提，记忆尚新。回收旅大，取消二十一个条，撤废一切不平等条约；这些事，件件横在我们眼前，一无解决。可叹！人们只有五分间热情而已。呜乎！中... </p>
    </li><li> <a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/57.html' class='preview'><img src='http://www.dedecms.com/demoimg/uploads/210823/1-210R31F312413.png'/></a>
     [<b><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></b>] <a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/57.html" class="title">厦门中国台湾同志会宣言 (1925年4月24日)</a> <span class="info"> <small>日期：</small>2021-08-23 17:20:00 <small>点击：</small>195 <small>好评：</small>0 </span>
     <p class="intro"> 国耻！国耻！莫忘国耻！旧事重提，记忆尚新。回收旅大，取消二十一个条，撤废一切不平等条约；这些事，件件横在我们眼前，一无解决。可叹！人们只有五分间热情而已。呜乎！中... </p>
    </li><li> <a href='/a/xiangguanziliao/xiangguanjianjie/2021/0810/58.html' class='preview'><img src='http://www.dedecms.com/demoimg/uploads/210823/1-210R31G132O4.png'/></a>
     [<b><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></b>] <a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/58.html" class="title">General notes about pre-Qin and Han texts</a> <span class="info"> <small>日期：</small>2021-08-23 17:19:48 <small>点击：</small>172 <small>好评：</small>0 </span>
     <p class="intro"> It should be remembered that what remains today in writing of the pre-Qin schools should not necessarily be considered representative of their relative importance at the time. During the warring states period, Mencius complained that the wo... </p>
    </li>
   </ul>
  </div>
  <!-- /listbox -->
  <div class="dede_pages">
   <ul class="pagelist">
    <li><span class="pageinfo">共 <strong>1</strong>页<strong>8</strong>条记录</span></li>

   </ul>
  </div>
  <!-- /pages -->
 </div>
 <!-- /pleft -->
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      <li><a href='/a/xiangguanziliao/xiangguanjianjie/' class='thisclass'>相关简介</a></li>
      <li><a href='/a/xiangguanziliao/xiangguanziliao/'>相关资料</a></li>
      
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      
     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1 light">
   <dl class="tbox">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/57.html">厦门中国台湾同志会宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/58.html">General notes about pre-Qin and</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/55.html">商务部关于实施“振兴老</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/51.html">1901–2014年黄土高原1 km分</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/56.html">厦门中国台湾同志会宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/53.html">1952年安徽省人民政府三年</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/52.html">1909年、1927年、1937年南京</a></li>
<li><a href="/a/xiangguanziliao/xiangguanjianjie/2021/0810/54.html">厦门中国台湾同志会宣言</a></li>

     </ul>
    </dd>
   </dl>
  </div>
    <a href='http://2v.dedecms.com/' id='__dedeqrcode_1'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=1;
  	var __dedeqrcode_aid=15;
  	var __dedeqrcode_type='list';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script>
 </div>
 <!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->
</body>
</html>
