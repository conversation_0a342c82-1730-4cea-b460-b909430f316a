<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>2004–2016年中国生态系统研究网络（CERN）水体p_万婕源码网</title>
<meta name="keywords" content="2004,–,2016,年中国,生态,系统研究,网络,CER" />
<meta name="description" content="摘要关键词 [ 编辑 ] 引 言 [ 编辑 ] 水体pH可以有效地表示其酸碱性，过酸或者过碱的水体对于植物生长和动物生活均存在一定的负面作用。降水的pH＜5.6即达到酸沉降水平 [1] ，会对农作" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=66">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=66";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li class='hover'><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xiangguanziliao/'>相关资料</a> > <a href='/a/xiangguanziliao/xiangguanziliao/'>相关资料</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>2004–2016年中国生态系统研究网络（CERN）水体p</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:17<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=66&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">摘要关键词 [ 编辑 ] 引 言 [ 编辑 ] 水体pH可以有效地表示其酸碱性，过酸或者过碱的水体对于植物生长和动物生活均存在一定的负面作用。降水的pH＜5.6即达到酸沉降水平 [1] ，会对农作</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="摘要&amp;关键词" style="padding: 0px; margin: 0px;">摘要&amp;关键词</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=1" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：摘要&amp;关键词">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="引_言" style="padding: 0px; margin: 0px;">引 言</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=4" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：引 言">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	水体pH可以有效地表示其酸碱性，过酸或者过碱的水体对于植物生长和动物生活均存在一定的负面作用。降水的pH＜5.6即达到酸沉降水平<sup class="reference" id="cite_ref-ref1_1-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref1-1" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[1]</a></sup>，会对农作物、草地、森林等植被生长带来危害<sup class="reference" id="cite_ref-ref3_2-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref3-2" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[2]</a></sup><sup class="reference" id="cite_ref-ref2_3-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref2-3" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[3]</a></sup>，还会破坏土壤结构<sup class="reference" id="cite_ref-ref5_4-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref5-4" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[4]</a></sup><sup class="reference" id="cite_ref-ref4_5-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref4-5" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[5]</a></sup>，导致土壤和地表水酸化等<sup class="reference" id="cite_ref-ref6_6-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref6-6" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[6]</a></sup>。根据我国地表水环境质量标准GB3838-2002规定，日常用水pH范围为6.00&ndash;9.00。地下水环境质量标准GB14848-93规定，pH在6.50&ndash;8.50满足生活生产和植物生长的基本用水需要，如不能达标，也可以使用pH值是＜6.50或者＞8.50的地下水。灌溉用水水质标准一般规定pH范围是5.50&ndash;8.50，使用过酸、过碱或盐含量较高的水会造成农作物减产。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	矿化度是指水体中的溶解性固体的总量，我国目前对于矿化度的环境标准规范主要应用在地下水上，对其他水体未做明确说明。地下水环境质量标准GB14848-93将地下水矿化度分为5级，即&le;300 mg&middot;L-1、300&ndash;500 mg&middot;L-1、500&ndash;1000 mg&middot;L-1、1000&ndash;2000 mg&middot;L-1、＞2000 mg&middot;L-1。水体的矿化度对于植物根系<sup class="reference" id="cite_ref-ref7_7-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref7-7" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[7]</a></sup>和土壤动物生长存活具有直接影响，矿化度太高可造成土壤盐碱化、物理结构改变和土壤动物总量减少等不良后果<sup class="reference" id="cite_ref-ref8_8-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref8-8" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[8]</a></sup>。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	本文介绍了中国生态系统研究网络（CERN）34个生态站2004&ndash;2016年降水、地表水、地下水pH和矿化度的监测数据情况，为科学工作者或用户充分利用本数据提供详细说明。</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="1_数据采集和处理方法" style="padding: 0px; margin: 0px;">1 数据采集和处理方法</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=5" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：1 数据采集和处理方法">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="1.1_数据采集方法" style="padding: 0px; margin: 0px;">1.1 数据采集方法</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=6" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：1.1 数据采集方法">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	各生态站降水按月份或季节采集样品，无降水或降水不足月份未采样。地表水、地下水按月份或季节采集样品，然后按照《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E9%99%86%E5%9C%B0%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E6%B0%B4%E7%8E%AF%E5%A2%83%E8%A7%82%E6%B5%8B%E8%A7%84%E8%8C%83&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="陆地生态系统水环境观测规范（页面不存在）">陆地生态系统水环境观测规范</a>》<sup class="reference" id="cite_ref-ref9_9-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref9-9" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[9]</a></sup>的统一方法测定。其中pH采用电位计法，收集水样后过滤，室温条件下通过校正液校准电位计后，测定水样的pH值。矿化度采用加和法或重量法测定，加和法即通过测定水样中主要的8种阴、阳离子（K+、Na+、Ca2+、Mg2+、SO42- 、CL&minus;、HCO3&minus; 、CO32&minus; ）的总和，得出水样的矿化度，重量法即过滤后通过蒸干得到固体物质的总量，计算出水体的矿化度。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="1.2_数据处理方法" style="padding: 0px; margin: 0px;">1.2 数据处理方法</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=7" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：1.2 数据处理方法">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	水体经人工观测员在现场采集样品后，在实验室测定记录并录入计算机进行电子化。生态站每年集中整理数据并按相应的格式汇交到CERN水分分中心。CERN水分分中心对所有数据进行再次检查，合格的数据汇交到CERN综合中心，相关人员进行数据格式审核，无误后最终导入到数据库，以供使用。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	数据集经质量控制后，去除了明显的异常值。整理后的pH、矿化度数据按照水体类型（降水、地表水、地下水）分别汇集到Excel文件中的不同数据表单内，各表单内数据按照生态站名称和时间排序；生态站基本信息，如代码、分布区域、经度、纬度、气候带、降水量等也单独汇集到一个数据表单内。</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="2_数据样本描述" style="padding: 0px; margin: 0px;">2 数据样本描述</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=8" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：2 数据样本描述">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	本数据集的数据储存于Excel文件中的4个数据表单中，其中降水、地表水、地下水pH和矿化度数据分别存放于3个数据表单中。还有1个表单是生态站基本情况表，给出了34个生态站的基础信息。两种表中包含的具体字段名称和详细信息见表1和表2。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	<b style="padding: 0px; margin: 0px;">表1<span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span></b>典型农田生态系统不同水体数据表内容</p>
<table class="wikitable" style="padding: 0px; margin: 1em 0px; font-size: 15.008px; background-color: rgb(249, 249, 249); color: rgb(32, 33, 34); border-style: solid; border-color: rgb(170, 170, 170); border-collapse: collapse; caret-color: rgb(32, 33, 34); font-family: sans-serif; text-indent: 0px; text-size-adjust: auto;">
	<tbody style="padding: 0px; margin: 0px;">
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">序号</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">字段名称</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">量纲</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">数据类型</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">示例</b></td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				1</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				生态站代码</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				ASA</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				2</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				年份</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数字型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				2004</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				3</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				月份</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数字型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				3</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				4</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				水质采样点代码</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				ASAFZ10CLB_01</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				5</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				采样点名称</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				川地气象观测场雨水采集器</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				6</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				pH值</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数字型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				8.56</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				7</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				矿化度</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				mg&middot;L-1</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数字型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				36.94</td>
		</tr>
	</tbody>
</table>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	&nbsp;</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<b style="padding: 0px; margin: 0px;">表2<span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span></b>典型农田生态系统信息表内容</p>
<table class="wikitable" style="padding: 0px; margin: 1em 0px; font-size: 15.008px; background-color: rgb(249, 249, 249); color: rgb(32, 33, 34); border-style: solid; border-color: rgb(170, 170, 170); border-collapse: collapse; caret-color: rgb(32, 33, 34); font-family: sans-serif; text-indent: 0px; text-size-adjust: auto;">
	<tbody style="padding: 0px; margin: 0px;">
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">序号</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">字段名称</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">量纲</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">数据类型</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">示例</b></td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				1</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				生态站</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				海伦</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				2</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				代码</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				HLA</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				3</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				生态系统类型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				农田</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				4</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				经度</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				E</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数字型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				126&deg;55&prime;</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				5</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				纬度</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				N</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数字型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				47&deg;27&prime;</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				6</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				气候带</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				中温带亚湿润地区</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				7</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				年降水量</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				mm</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数字型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				500</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				8</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				年平均气温</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				℃</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数字型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				1.5</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				9</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				土壤类型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				黑土</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				10</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数据类型（个数）</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				降水（22）</td>
		</tr>
	</tbody>
</table>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	各生态站根据降水频率的不同，降水样品的采集频率也不同。降水频率较少的地区，如阿克苏站、阜康站等，只在发生降水的月份取样；降水充沛的生态站每月至少取样一次，如常熟站；其余生态站每季度取样一次。地表水和地下水的取样频率在不同生态站也存在一定差异，如安塞站、常熟站等按照月份取样，阿克苏站、哀牢山站等按照季节取样。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	图1展示了取样频率较高的常熟生态系统2004&ndash;2016年降水、地表水、地下水pH测定值。可以看出降水pH最低值出现在2011年，接近酸沉降值5.6，其余年份降水pH均高于6.0。地表水和地下水pH值在7.3&ndash;8.3范围内波动，2005&ndash;2009年地表水pH低于地下水，2016年地表水pH大于地下水，其余年份两者相差较小。2008&ndash;2016年，地表水和地下水pH均高于降水pH值。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	<a class="external text" href="http://www.csdata.org/static/publish/FB/08/6E/2900ED42EBBC1A062062EA7F85/xml_2/math/image1.png" rel="nofollow" style="padding: 0px 13px 0px 0px; margin: 0px; color: rgb(102, 51, 102); text-decoration-line: none; background-image: url(&quot;https://zh.wikisource.org/w/skins/Vector/resources/common/images/external-link-ltr-icon.svg?48e54&quot;); background-position: right center; background-repeat: no-repeat;">图片</a></p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<b style="padding: 0px; margin: 0px;">图1<span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span></b>常熟（CSA）农田生态系统2004&ndash;2016年降水、地表水、地下水酸碱度变化</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	常熟站降水、地表水、地下水矿化度的变化如图2所示，2005&ndash;2016年间降水矿化度远低于地表水和地下水矿化度值，矿化度主要是水中的可溶性离子含量的高低指标，地表水和地下水由于和土壤直接接触，且农田生态系统中存在施肥和地表径流等的影响，均是造成其矿化度较高的原因。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	<a class="external text" href="http://www.csdata.org/static/publish/FB/08/6E/2900ED42EBBC1A062062EA7F85/xml_2/math/image2.png" rel="nofollow" style="padding: 0px 13px 0px 0px; margin: 0px; color: rgb(102, 51, 102); text-decoration-line: none; background-image: url(&quot;https://zh.wikisource.org/w/skins/Vector/resources/common/images/external-link-ltr-icon.svg?48e54&quot;); background-position: right center; background-repeat: no-repeat;">图片</a></p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<b style="padding: 0px; margin: 0px;">图2<span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span></b>常熟（CSA）农田生态系统2004&ndash;2016年降水、地表水、地下水矿化度变化</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	&nbsp;</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="3_数据质量控制和评估" style="padding: 0px; margin: 0px;">3 数据质量控制和评估</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=9" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：3 数据质量控制和评估">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	监测数据采取三级质量控质体系，即野外台站根据统一监测规范和检测方法采集样品并进行分析测定，其工作包括分析测试人员的准确测定、及时完整记录和录入等环节，这是质量控制的最关键部分。CERN水分分中心在台站汇交数据后，为避免出现人工录入或者其他方面的误差，对数据进行质量检测。如果出现明显不符合逻辑的情况，反馈给台站进行再次确认。综合中心通过软件对数据进行质量控制，同时还规范检查数据格式并保存。</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="4_数据价值" style="padding: 0px; margin: 0px;">4 数据价值</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=10" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：4 数据价值">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	水体的pH和矿化度可以从酸雨、地表水酸化、土壤酸化、灌溉盐含量超标、土壤盐碱化和板结等方面对生态系统产生影响。本数据集提供的CERN典型生态系统34个生态站2004&ndash;2016年降水、地表水、地下水酸碱度和矿化度数据，在一定程度上表征出中国典型生态系统的水体整体情况，可以为研究生态系统水利用或者循环提供一定的数据信息支持。</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="5_数据使用方法和建议" style="padding: 0px; margin: 0px;">5 数据使用方法和建议</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2004%E2%80%932016%E5%B9%B4%E4%B8%AD%E5%9B%BD%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E7%A0%94%E7%A9%B6%E7%BD%91%E7%BB%9C%EF%BC%88CERN%EF%BC%89%E6%B0%B4%E4%BD%93pH%E5%92%8C%E7%9F%BF%E5%8C%96%E5%BA%A6%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=11" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：5 数据使用方法和建议">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	需要使用本数据集的读者，可以登录Science Data Bank（<a class="external free" href="http://www.sciencedb.cn/dataSet/handle/891%EF%BC%89%E4%B8%8B%E8%BD%BD%E3%80%82%E6%9C%AC%E6%95%B0%E6%8D%AE%E9%9B%86%E6%98%AF%E5%AF%B9CERN" rel="nofollow" style="padding: 0px 13px 0px 0px; margin: 0px; color: rgb(102, 51, 102); text-decoration-line: none; background-image: url(&quot;https://zh.wikisource.org/w/skins/Vector/resources/common/images/external-link-ltr-icon.svg?48e54&quot;); overflow-wrap: break-word; background-position: right center; background-repeat: no-repeat;">http://www.sciencedb.cn/dataSet/handle/891）下载。本数据集是对CERN</a><span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span>34个典型生态系统水体pH和矿化度数据整理后形成的数据库。数据经过一定的质量控制除去了部分明显的异常值，并根据水体种类、生态系统代码和时间进行重新排列。数据用户在分析使用中可以结合更加详细的实验设计，配合数据结构模型，解读中国典型生态系统水体的利用和循环情况。</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	&nbsp;</h2>

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_6'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=6;
  	var __dedeqrcode_aid=66;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',66)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',66)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(66);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/xiangguanziliao/xiangguanziliao/2021/0810/65.html'>2004年中国人权事业的进展</a> </li>
     <li>下一篇：没有了 </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=66" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=66&title=2004–2016年中国生态系统研究网络（CERN）水体p" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=66" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="66" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=66">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=66&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '66');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></li>
      <li><a href='/a/xiangguanziliao/xiangguanziliao/' class='thisclass'>相关资料</a></li>
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/59.html">南北共同宣言</a>
       <p>根据全民族渴望祖国和平统一的崇高意志，朝鲜民主主义人民共...</p>
      </li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/64.html">2004年美国的人权纪录</a>
       <p>关于生命、自由和人身安全 [ 编辑 ] 在美国，暴力犯罪泛滥成灾...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/60.html">换盏灯 爱地球 585白炽灯汰</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/63.html">2005–2015年三江站水分监测</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/61.html">2015年美国的人权纪录</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/64.html">2004年美国的人权纪录</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/62.html">22007年龙岩市环境状况公报</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/65.html">2004年中国人权事业的进展</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/59.html">南北共同宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/66.html">2004–2016年中国生态系统研</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
