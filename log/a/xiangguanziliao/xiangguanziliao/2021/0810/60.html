<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>换盏灯 爱地球 585白炽灯汰换计划_万婕源码网</title>
<meta name="keywords" content="换,盏灯,爱,地球,585,白炽灯,汰换,计划,换," />
<meta name="description" content="换盏灯 爱地球 585白炽灯汰换计划 换盏灯 爱地球 585白炽灯汰换计划 根据统计，国内白炽灯的销售量每年约有2,218万颗，年用电量约10.8亿度，为推动照明节能及创造优质生活的光环境，" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=60">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=60";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li class='hover'><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xiangguanziliao/'>相关资料</a> > <a href='/a/xiangguanziliao/xiangguanziliao/'>相关资料</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>换盏灯 爱地球 585白炽灯汰换计划</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:19<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=60&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">换盏灯 爱地球 585白炽灯汰换计划 换盏灯 爱地球 585白炽灯汰换计划 根据统计，国内白炽灯的销售量每年约有2,218万颗，年用电量约10.8亿度，为推动照明节能及创造优质生活的光环境，</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	换盏灯 爱地球 585白炽灯汰换计划</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	换盏灯 爱地球</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	585白炽灯汰换计划</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	根据统计，国内白炽灯的销售量每年约有2,218万颗，年用电量约10.8亿度，为推动照明节能及创造优质生活的光环境，政府将积极推动&ldquo;585白炽灯汰换计划&rdquo;，预期以5年时间，逐步推动国内白炽灯汰换为省电灯泡或其他高效率灯具，若全部完成汰换，估计每年可省下约8亿度电，减少近50万公吨二氧化碳排放，相当于1,342座大安森林公园二氧化碳吸附量，或相当于造林2,784万颗树的效益。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	能源局指出，照明用电约260亿度电，约占全国总用电2,214亿度之12%，未来若全面采用具高度节能潜力之高效率(如省电灯泡、T5萤光灯管、复金属灯、LED等)照明，节能潜力可达30~80%间。目前能源局已将照明能源效率纳入管理，并推动节能标章产品认证，今年亦将启动全面照明改造，由白炽灯汰换做起，并规划系列提升照明效率及LED应用示范计划与宣导活动。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	能源局表示，白炽灯平均使用寿命(约500~1000小时)较短，约只有省电灯泡(约3000~6000小时)的1/3至1/6，而同样照度的白炽灯比省电灯泡耗电5倍以上；另一方面，白炽灯表面温度高，夏天室内使用更增加冷气耗用量；此外，全球先进国家皆推动汰换白炽灯，以减缓地球暖化效应，身为地球村一员，台湾也积极参与此国际节能减碳潮流。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	能源局进一步表示，针对白炽灯汰换计划，政府已研拟整体配套策略，针对生产面，政府预计于今(2008)年年底公告白炽灯能源效率标准，并拟于2012年施行，届时不符标准规定之白炽灯将无法再进口或制造销售；在用户端部分，已于2007年由政府依据&ldquo;加强政府机关及学校节约能源措施&rdquo;带头示范白炽灯汰换采用高效率灯具；而公有市场也将于2009年以前全面汰换白炽灯改用省电灯泡；其次，另针对特定用户，推动相关业界参与自愿节能协议及提供节能技术服务，其中亦将针对如饭店、旅馆、住家、农业、市场及医院等白炽灯使用率较高之场所，积极鼓励汰换白炽灯；对于一般使用民众，能源局将持续透过媒体宣导、卖场促销活动等，带动消费者改用省电灯具。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	能源局特别指出，汰换白炽灯不仅可响应减碳爱地球的行动，也可省下了可观的电费。例如以住家或花卉农场常用的100W白炽灯替换为20W的省电灯泡，节约用电达80%，一年可节省312元的电费；若是一般在摊商常见的250W的白炽灯替换成55W的省电灯泡，节约用电达78%，一年可节省498元电费。虽然省电灯泡的价格较白炽灯高，但不到一年就可以回收成本。加上省电灯泡使用寿命更比白炽灯高出3至6倍，因此汰换省电灯泡不仅省电、省钱又可减少温室气体二氧化碳排放。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	能源局最后表示，透过推动全面性节能减碳运动，盼凝聚全民共识，大家一起换盏灯爱地球，让台湾与世界同步保护地球。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	经济部能源局发言人：王副局长运铭 电话02-2773-4729 ；行动电话：0910-216-359</p>
<div style="padding: 0px; margin: 0px; color: rgb(51, 51, 51); font-family: Verdana, Arial, Tahoma; font-size: 14px;">
	&nbsp;</div>

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_20'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=20;
  	var __dedeqrcode_aid=60;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',60)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',60)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(60);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/xiangguanziliao/xiangguanziliao/2021/0810/59.html'>南北共同宣言</a> </li>
     <li>下一篇：<a href='/a/xiangguanziliao/xiangguanziliao/2021/0810/61.html'>2015年美国的人权纪录</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=60" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=60&title=换盏灯 爱地球 585白炽灯汰换计划" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=60" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="60" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=60">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=60&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '60');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></li>
      <li><a href='/a/xiangguanziliao/xiangguanziliao/' class='thisclass'>相关资料</a></li>
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/59.html">南北共同宣言</a>
       <p>根据全民族渴望祖国和平统一的崇高意志，朝鲜民主主义人民共...</p>
      </li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/64.html">2004年美国的人权纪录</a>
       <p>关于生命、自由和人身安全 [ 编辑 ] 在美国，暴力犯罪泛滥成灾...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/60.html">换盏灯 爱地球 585白炽灯汰</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/63.html">2005–2015年三江站水分监测</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/61.html">2015年美国的人权纪录</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/64.html">2004年美国的人权纪录</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/62.html">22007年龙岩市环境状况公报</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/65.html">2004年中国人权事业的进展</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/59.html">南北共同宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/66.html">2004–2016年中国生态系统研</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
	<!-- 
		为了支持织梦团队的发展,请您保留织梦内容管理系统的链接信息.
		我们对支持织梦团队发展的朋友表示真心的感谢!织梦因您更精彩!
	-->
    <div class="footer_left"></div>
    <div class="footer_body">
	<p class="powered">    
		Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)--国内专业的PHP网站管理系统，轻松建站的利器。" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br /><div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div></p>        
<!-- /powered -->
   </div>
   <div class="footer_right"></div>
</div>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
