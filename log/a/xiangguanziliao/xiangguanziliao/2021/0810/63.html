<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>2005–2015年三江站水分监测数据集_万婕源码网</title>
<meta name="keywords" content="2005,–,2015年,三江,站,水分,监测,数据,集,摘" />
<meta name="description" content="摘要关键词 [ 编辑 ] 摘要：三江站水分监测数据集选自中国科学院三江平原沼泽湿地生态试验站的水文观测数据，包括20052015年的地下水水位、沼泽湿地积水水深、水面蒸发。数据采集" />
<link href="/templets/default/style/dedecms.css" rel="stylesheet" media="screen" type="text/css" />
<style type="text/css">
/* 小仙元码暗色主题样式 - 文章详情页 */
* { box-sizing: border-box; }
html { background: #0a0a0a; }
body {
    font: 14px -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: #fff !important;
    line-height: 1.6;
}
a { color: #00d4ff !important; text-decoration: none; transition: all 0.3s ease; }
a:hover { color: #0099cc !important; }

/* 面包屑导航 */
.position {
    background: #1a1a1a !important;
    padding: 15px 20px !important;
    border-bottom: 1px solid #333 !important;
    margin-bottom: 20px !important;
}
.position a { color: #00d4ff !important; }

/* 文章容器 */
.w960 { background: transparent !important; }
.tbox {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    margin-bottom: 30px !important;
}
.tbox dt {
    background: linear-gradient(135deg, #2d2d2d 0%, #404040 100%) !important;
    border: none !important;
    height: 50px !important;
    border-radius: 15px 15px 0 0 !important;
}
.tbox dt strong {
    color: #00d4ff !important;
    line-height: 50px !important;
    font-size: 1.3rem !important;
    padding-left: 20px !important;
}
.tbox dd {
    background: #1a1a1a !important;
    border: none !important;
    padding: 30px !important;
    border-radius: 0 0 15px 15px !important;
}

/* 文章标题 */
.arc_title {
    color: #fff !important;
    font-size: 2.5rem !important;
    margin-bottom: 20px !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 15px !important;
}

/* 文章信息 */
.arc_info {
    color: #ccc !important;
    margin-bottom: 20px !important;
    padding: 15px 0 !important;
    border-bottom: 1px solid #333 !important;
}
.arc_info span { margin-right: 20px !important; }

/* 文章内容 */
.arc_content, .arc_body {
    color: #ccc !important;
    line-height: 1.8 !important;
    margin: 30px 0 !important;
}
.arc_content h1, .arc_content h2, .arc_content h3, .arc_content h4, .arc_content h5, .arc_content h6,
.arc_body h1, .arc_body h2, .arc_body h3, .arc_body h4, .arc_body h5, .arc_body h6 {
    color: #fff !important;
    margin: 30px 0 20px 0 !important;
}
.arc_content h2, .arc_body h2 { color: #00d4ff !important; }
.arc_content p, .arc_body p { margin-bottom: 20px !important; }
.arc_content img, .arc_body img {
    max-width: 100% !important;
    border-radius: 8px !important;
    margin: 20px 0 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* 文章缩略图 */
.arc_pic {
    text-align: center !important;
    margin: 20px 0 !important;
}
.arc_pic img {
    max-width: 100% !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

/* 标签 */
.arc_tags { margin-top: 30px !important; }
.arc_tags a, .tags a {
    background: #333 !important;
    color: #00d4ff !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    text-decoration: none !important;
    display: inline-block !important;
    margin: 5px 10px 5px 0 !important;
    transition: all 0.3s ease !important;
}
.arc_tags a:hover, .tags a:hover {
    background: #00d4ff !important;
    color: #fff !important;
    transform: translateY(-2px) !important;
}

/* 侧边栏 */
.sidebar { background: transparent !important; }
.sidebar .tbox { margin-bottom: 25px !important; }
.sidebar ul li {
    background: none !important;
    border-bottom: 1px solid #333 !important;
    padding: 8px 0 !important;
}
.sidebar ul li a { color: #ccc !important; }
.sidebar ul li a:hover { color: #00d4ff !important; }

/* 评论区域 */
.feedback {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border: 1px solid #333 !important;
    border-radius: 15px !important;
    padding: 30px !important;
    margin-top: 30px !important;
}
.feedback h3 {
    color: #00d4ff !important;
    border-bottom: 2px solid #00d4ff !important;
    padding-bottom: 10px !important;
    margin-bottom: 20px !important;
}

/* 评论表单 */
.feedback textarea {
    background: #333 !important;
    border: 1px solid #555 !important;
    color: #fff !important;
    border-radius: 8px !important;
    padding: 15px !important;
    width: 100% !important;
    resize: vertical !important;
}
.feedback textarea:focus {
    border-color: #00d4ff !important;
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.2) !important;
    outline: none !important;
}

/* 按钮样式 */
.btn, input[type="submit"], input[type="button"], button {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
    border: none !important;
    color: #fff !important;
    border-radius: 25px !important;
    padding: 12px 24px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: bold !important;
}
.btn:hover, input[type="submit"]:hover, input[type="button"]:hover, button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .w960 { padding: 0 15px !important; }
    .arc_title { font-size: 1.8rem !important; }
    .tbox dd { padding: 20px !important; }
    .arc_info { flex-direction: column !important; gap: 10px !important; }
}
</style>
<meta http-equiv="mobile-agent" content="format=xhtml;url=/m/view.php?aid=63">
<script type="text/javascript">if(window.location.toString().indexOf('pref=padindex') != -1){}else{if(/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))){if(window.location.href.indexOf("?mobile")<0){try{if(/Android|Windows Phone|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)){window.location.href="/m/view.php?aid=63";}else if(/iPad/i.test(navigator.userAgent)){}else{}}catch(e){}}}}</script>
<script language="javascript" type="text/javascript" src="/include/dedeajax2.js"></script>

<script language="javascript" type="text/javascript">
<!--
function CheckLogin(){
	  var taget_obj = document.getElementById('_ajax_feedback');
	  myajax = new DedeAjax(taget_obj,false,false,'','','');
	  myajax.SendGet2("/member/ajax_feedback.php");
	  DedeXHTTP = null;
}
function postBadGood(ftype,fid)
{
	var taget_obj = document.getElementById(ftype+fid);
	var saveid = GetCookie('badgoodid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==fid && hasid) continue;
			else {
				if(saveids[i]==fid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==10 && hasid) break;
				if(j==9 && !hasid) break;
			}
		}
		if(hasid) { alert('您刚才已表决过了喔！'); return false;}
		else saveid += ','+fid;
		SetCookie('badgoodid',saveid,1);
	}
	else
	{
		SetCookie('badgoodid',fid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/feedback.php?aid="+fid+"&action="+ftype+"&fid="+fid);
}
function postDigg(ftype,aid)
{
	var taget_obj = document.getElementById('newdigg');
	var saveid = GetCookie('diggid');
	if(saveid != null)
	{
		var saveids = saveid.split(',');
		var hasid = false;
		saveid = '';
		j = 1;
		for(i=saveids.length-1;i>=0;i--)
		{
			if(saveids[i]==aid && hasid) continue;
			else {
				if(saveids[i]==aid && !hasid) hasid = true;
				saveid += (saveid=='' ? saveids[i] : ','+saveids[i]);
				j++;
				if(j==20 && hasid) break;
				if(j==19 && !hasid) break;
			}
		}
		if(hasid) { alert("您已经顶过该帖，请不要重复顶帖 ！"); return; }
		else saveid += ','+aid;
		SetCookie('diggid',saveid,1);
	}
	else
	{
		SetCookie('diggid',aid,1);
	}
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	var url = "/plus/digg_ajax.php?action="+ftype+"&id="+aid;
	myajax.SendGet2(url);
}
function getDigg(aid)
{
	var taget_obj = document.getElementById('newdigg');
	myajax = new DedeAjax(taget_obj,false,false,'','','');
	myajax.SendGet2("/plus/digg_ajax.php?id="+aid);
	DedeXHTTP = null;
}
-->
</script>
</head>
<body class="articleview">
<div class="header_top">  
    <div class="w960 center">  
     <span id="time" class="time">织梦CMS - 轻松建站从此开始！</span>
     <div class="toplinks"><a href="/plus/heightsearch.php" target="_blank">高级搜索</a>|<a href="/data/sitemap.html" target="_blank">网站地图</a>|<a href="/tags.php">TAG标签</a><a href="/data/rssmap.html" class="rss">RSS订阅</a><span>[<a href=""onclick="this.style.behavior='url(#default#homepage)';this.setHomePage('http://*************:97');">设为首页</a>] [<a href="javascript:window.external.AddFavorite('http://*************:97','万婕源码网')">加入收藏</a>]</span></div>
    </div> 
</div>
<div class="header">
	<div class="top w960 center">
      <div class="title">
        <h1><a href="http://*************:97"><img src="/templets/default/images/logo.gif" height="54" width="216" alt="万婕源码网"/></a> </h1>
      </div>

	</div><!-- //top -->
	<!-- //菜单 -->
	<div class="module blue mT10 wrapper w963">
  	<div class="top">
    	<!-- //如果不使用currentstyle，可以在channel标签加入 cacheid='channeltoplist' 属性提升性能 -->
    <div id="navMenu">
    	<ul>
      	<li><a href='/'><span>主页</span></a></li>
      	
      	<li><a href='/a/xianqinlianghan/'  rel='dropmenu1'><span>先秦两汉</span></a></li>
      	
      	<li><a href='/a/handaizhihou/'  rel='dropmenu6'><span>汉代之后</span></a></li>
      	<li class='hover'><a href='/a/xiangguanziliao/'  rel='dropmenu12'><span>相关资料</span></a></li>
      	<li><a href='/a/tuji/' ><span>图集</span></a></li>
      	
      	<li><a href='/a/tushuguan/' ><span>图书馆</span></a></li>
      	
      	<li><a href='/a/zidian/' ><span>字典</span></a></li>
      	
    	</ul>
    </div>	
    <div class="search">
      <form  name="formsearch" action="/plus/search.php">
        <div class="form">
          <h4>搜索</h4>
           <input type="hidden" name="kwtype" value="0" />
           <input name="q" type="text" class="search-keyword" id="search-keyword" value="在这里搜索..." onfocus="if(this.value=='在这里搜索...'){this.value='';}"  onblur="if(this.value==''){this.value='在这里搜索...';}" />
           <select name="searchtype" class="search-option" id="search-option">
               <option value="title" selected='1'>检索标题</option>
               <option value="titlekeyword">智能模糊</option>
           </select>
          <button type="submit" class="search-submit">搜索</button>
        </div>
        </form>
        <div class="tags">
          <h4>热门标签:</h4>
          <ul>
          
          </ul>
        </div>
    </div><!-- //search -->
		</div>
	</div>
</div><!-- //header -->
<!-- /header -->
<div class="w960 center clear mt1">
<div class="pleft">
 <div class="place"> <strong>当前位置:</strong> <a href='http://*************:97/'>主页</a> > <a href='/a/xiangguanziliao/'>相关资料</a> > <a href='/a/xiangguanziliao/xiangguanziliao/'>相关资料</a> </div>
 <!-- /place -->
 <div class="viewbox">
  <div class="title">
   <h2>2005–2015年三江站水分监测数据集</h2>
  </div>
  <!-- /title -->
  <div class="info"> <small>时间:</small>2021-08-23 17:18<small>来源:</small>未知 <small>作者:</small>admin <small>点击:</small>
   <script src="/plus/count.php?view=yes&aid=63&mid=1" type='text/javascript' language="javascript"></script>
   次</div>
  <!-- /info -->
  
  <div class="intro">摘要关键词 [ 编辑 ] 摘要：三江站水分监测数据集选自中国科学院三江平原沼泽湿地生态试验站的水文观测数据，包括20052015年的地下水水位、沼泽湿地积水水深、水面蒸发。数据采集</div>
  
  <div class="content">
   <table width='100%'>
    <tr>
     <td>
      <h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="摘要&amp;关键词" style="padding: 0px; margin: 0px;">摘要&amp;关键词</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=1" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：摘要&amp;关键词">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	摘要：三江站水分监测数据集选自中国科学院三江平原沼泽湿地生态试验站的水文观测数据，包括2005&ndash;2015年的地下水水位、沼泽湿地积水水深、水面蒸发。数据采集和质量控制严格按照中国生态系统研究网络（CERN）制定的《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E9%99%86%E5%9C%B0%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E6%B0%B4%E7%8E%AF%E5%A2%83%E8%A7%82%E6%B5%8B%E8%A7%84%E8%8C%83&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="陆地生态系统水环境观测规范（页面不存在）">陆地生态系统水环境观测规范</a>》进行，对开展沼泽湿地生态系统水文特征研究具有良好的数据支撑作用。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	关键词：水分监测；沼泽湿地；水环境</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="Abstract_&amp;_Keywords" style="padding: 0px; margin: 0px;">Abstract &amp; Keywords</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=2" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：Abstract &amp; Keywords">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	Abstract:&nbsp;The water monitoring data of Sanjiang Station are selected from the hydrological observation data of Sanjiang Plain Marsh Wetland Ecological Experimental Station of Chinese Academy of Sciences, including groundwater level from 2005 to 2015, water depth of swampy wetland and evaporation of water surface. Data acquisition and quality control are carried out strictly in accordance with the Code of Water Environment observation of Terrestrial ecosystem, which published by Chinese Ecosystem Research Network (CERN), and plays a good role in supporting the study of hydrological characteristics of swampy wetland ecosystem.</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	Keywords:&nbsp;water monitoring;&nbsp;swampy wetland;&nbsp;water environment</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	&nbsp;</h2>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="引_言" style="padding: 0px; margin: 0px;">引 言</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=5" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：引 言">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	湿地是地球上重要的天然蓄水库和物种基因库，具有涵养水源、调蓄洪水、补充地下水、调节小气候和净化水质等水文功能<sup class="reference" id="cite_ref-ref1_1-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref1-1" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[1]</a></sup><sup class="reference" id="cite_ref-ref2_2-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref2-2" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[2]</a></sup><span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span>。水文过程是湿地形成、发育、演替直至消亡的最重要的驱动机制<sup class="reference" id="cite_ref-ref3_3-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref3-3" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[3]</a></sup><sup class="reference" id="cite_ref-ref4_4-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref4-4" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[4]</a></sup><span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span>。地下水一般是指埋藏于地表以下能自由流动的水体，包括潜水和承压水。而地下水位通常是指土壤水分达到饱和状态时的深度，其变化可反映地下水的运动状态<sup class="reference" id="cite_ref-ref5_5-0" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref5-5" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[5]</a></sup>。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	三江平原位于我国黑龙江省东部，地区面积为10.57&times;104 km2，已经成为我国重要的商品粮基地，农田主要为水稻田，主要采用浅层地下水灌溉。大面积地下水开采活动已经导致地下水水位持续下降，供需矛盾日益突出。三江平原也是我国沼泽湿地的主要集中分布区。近年来，三江平原湿地面积已减少了近82%，由于湿地的持续退化和缩小，已经造成本区域内可利用的地下水资源量快速减少，并且区域内地下水补给能力也出现持续降低的趋势。中国科学院三江平原沼泽湿地生态试验站以三江平原沼泽湿地为主要观测对象，三江站是对气象、水文、土壤、生物要素进行长期全面观测的国家级野外台站。本数据集的数据采集方法依据《<a class="new" href="https://zh.wikisource.org/w/index.php?title=%E9%99%86%E5%9C%B0%E7%94%9F%E6%80%81%E7%B3%BB%E7%BB%9F%E6%B0%B4%E7%8E%AF%E5%A2%83%E8%A7%82%E6%B5%8B%E8%A7%84%E8%8C%83&amp;action=edit&amp;redlink=1" style="padding: 0px; margin: 0px; color: rgb(165, 88, 88); text-decoration-line: none; background-image: none;" title="陆地生态系统水环境观测规范（页面不存在）">陆地生态系统水环境观测规范</a>》<sup class="reference" id="cite_ref-ref5_5-1" style="padding: 0px; margin: 0px; line-height: 1; unicode-bidi: isolate; white-space: nowrap;"><a href="https://zh.wikisource.org/wiki/2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86#cite_note-ref5-5" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none;">[5]</a></sup>进行，数据与其他国家级野外台站具有很好的可比性，为开展三江平原地下水资源的科学评价及水资源合理利用提供了重要参考依据。</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="1_数据采集和处理方法" style="padding: 0px; margin: 0px;">1 数据采集和处理方法</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=6" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：1 数据采集和处理方法">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="1.1_地下水水位" style="padding: 0px; margin: 0px;">1.1 地下水水位</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=7" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：1.1 地下水水位">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	数据采集方法为野外人工观测，观测频率为5&ndash;10日1次，按采样地根据质控后的地下水埋深数据计算月平均数据。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="1.2_沼泽湿地积水水深" style="padding: 0px; margin: 0px;">1.2 沼泽湿地积水水深</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=8" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：1.2 沼泽湿地积水水深">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	数据获取为定期进行实地观测记录，获取优势种物候数据。数据采集方法为野外人工观测，观测频率为1次/天，以草根层下的母质层顶为基准面观测湿地积水水深，根据质控后的湿地水深数据计算月平均数据。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="1.3_水面蒸发" style="padding: 0px; margin: 0px;">1.3 水面蒸发</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=9" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：1.3 水面蒸发">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	数据获取方法为野外人工观测，观测频率为2次/日，蒸发数据使用E601蒸发皿观测所得。</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="2_数据样本描述" style="padding: 0px; margin: 0px;">2 数据样本描述</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=10" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：2 数据样本描述">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	三江站地下水水位数据集包括三江站2005&ndash;2015年地下水采样地水位埋深数据，数据项包括地下水埋深、地面高程等（表1）。计量单位为米（m），地下水采样地分别为气象观测场地下水采样地、旱田辅助观测场地下水采样地、水稻田地下水采样地。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	<b style="padding: 0px; margin: 0px;">表1<span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span></b>三江站地下水水位观测数据样本</p>
<table class="wikitable" style="padding: 0px; margin: 1em 0px; font-size: 15.008px; background-color: rgb(249, 249, 249); color: rgb(32, 33, 34); border-style: solid; border-color: rgb(170, 170, 170); border-collapse: collapse; caret-color: rgb(32, 33, 34); font-family: sans-serif; text-indent: 0px; text-size-adjust: auto;">
	<tbody style="padding: 0px; margin: 0px;">
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">序号</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">字段名称</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">量纲</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">数据类型</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">实例</b></td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				1</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				年</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				2015</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				2</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				月</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				6</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				3</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				日</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				5</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				4</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				观测井代码</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				SJMQX01CDX_01</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				5</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				样地名称</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				三江站气象站地下水位观测井</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				6</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				植被名称</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				小叶章</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				7</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				地下水埋深</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				m</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				14</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				8</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				地面高程</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				m</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				55.6</td>
		</tr>
	</tbody>
</table>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	三江站沼泽湿地积水水深数据集包括三江站2005&ndash;2015年常年积水区综合观测场湿地积水水深数据，数据项包括积水水深、地面高程等（表2）。计量单位为厘米（cm）。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	<b style="padding: 0px; margin: 0px;">表2<span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span></b>三江站沼泽湿地积水水深观测数据样本</p>
<table class="wikitable" style="padding: 0px; margin: 1em 0px; font-size: 15.008px; background-color: rgb(249, 249, 249); color: rgb(32, 33, 34); border-style: solid; border-color: rgb(170, 170, 170); border-collapse: collapse; caret-color: rgb(32, 33, 34); font-family: sans-serif; text-indent: 0px; text-size-adjust: auto;">
	<tbody style="padding: 0px; margin: 0px;">
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">序号</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">字段名称</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">量纲</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">数据类型</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">实例</b></td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				1</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				年</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				2015</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				2</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				月</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				6</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				3</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				日</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				5</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				4</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				样地代码</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				SJMZH01CJS_01</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				5</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				样地名称</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				-</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				常年积水区地表积水水深观测样地</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				6</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				积水水深</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				cm</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				32.9</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				7</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				地面高程</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				m</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				55</td>
		</tr>
	</tbody>
</table>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	三江站水面蒸发数据集包括三江站2005&ndash;2015年气象观测场蒸发数据，数据项包括月蒸发量、水温等（表3）。计量单位为毫米（mm），气象观测场样地代码为SJMQX01CZF_01。</p>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	<br style="padding: 0px; margin: 0px;" />
	<b style="padding: 0px; margin: 0px;">表3<span class="Apple-converted-space" style="padding: 0px; margin: 0px;">&nbsp;</span></b>三江站水面蒸发观测数据样本</p>
<table class="wikitable" style="padding: 0px; margin: 1em 0px; font-size: 15.008px; background-color: rgb(249, 249, 249); color: rgb(32, 33, 34); border-style: solid; border-color: rgb(170, 170, 170); border-collapse: collapse; caret-color: rgb(32, 33, 34); font-family: sans-serif; text-indent: 0px; text-size-adjust: auto;">
	<tbody style="padding: 0px; margin: 0px;">
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">序号</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">字段名称</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">量纲</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">数据类型</b></td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				<b style="padding: 0px; margin: 0px;">实例</b></td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				1</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				年</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				2015</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				2</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				月</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				6</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				3</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				日</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				5</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				4</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				蒸发皿代码</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				SJMQX01CZF_01</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				5</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				样地名称</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				&nbsp;</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				字符型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				综合气象观测场E601蒸发皿</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				6</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				每天蒸发量</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				mm</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				4.09</td>
		</tr>
		<tr style="padding: 0px; margin: 0px;">
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				7</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				水温</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				℃</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				数值型</td>
			<td style="padding: 0.2em; margin: 0px; border-style: solid; border-color: rgb(170, 170, 170);">
				15.7</td>
		</tr>
	</tbody>
</table>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	&nbsp;</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="3_数据质量控制和评估" style="padding: 0px; margin: 0px;">3 数据质量控制和评估</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=11" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：3 数据质量控制和评估">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="3.1_地下水水位" style="padding: 0px; margin: 0px;">3.1 地下水水位</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=12" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：3.1 地下水水位">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	数据观测做到操作规范，记录准确，多年数据比对，删除异常值。旱田辅助观测场地下水采样地和气象观测场地下水采样地取2个观测点做对比观测。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="3.2_沼泽湿地积水水深" style="padding: 0px; margin: 0px;">3.2 沼泽湿地积水水深</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=13" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：3.2 沼泽湿地积水水深">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	数据观测做到操作规范，记录准确，多年数据比对，删除异常值。由于三江站冬季结冰，故停止积水水深观测。</p>
<h3 style="padding: 0.5em 0px 0px; margin: 0.3em 0px 0px; color: rgb(0, 0, 0); overflow: hidden; font-size: 1.2em; line-height: 1.6; font-family: sans-serif; text-size-adjust: auto;">
	<span class="mw-headline" id="3.3_水面蒸发" style="padding: 0px; margin: 0px;">3.3 水面蒸发</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; user-select: none; font-size: small; font-weight: normal; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=14" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：3.3 水面蒸发">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h3>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	严格执行E601蒸发器的维护要求，逐日水面蒸发量与逐日降水量对照。对突出偏大、偏小确属不合理的水面蒸发量，应参照有关因素和邻站资料予以改正。 由于三江站冬季结冰，所以停止蒸发观测。</p>
<h2 style="padding: 0px; margin: 1em 0px 0.25em; color: rgb(0, 0, 0); overflow: hidden; border-bottom: 1px solid rgb(162, 169, 177); font-weight: normal; font-family: &quot;Linux Libertine&quot;, Georgia, Times, serif; line-height: 1.3; text-size-adjust: auto;">
	<span class="mw-headline" id="4_数据价值" style="padding: 0px; margin: 0px;">4 数据价值</span><span class="mw-editsection" style="padding: 0px; margin: 0px 0px 0px 1em; font-family: sans-serif; user-select: none; font-size: small; vertical-align: baseline; line-height: 1em; unicode-bidi: isolate;"><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0.25em 0px 0px; color: rgb(84, 89, 93);">[</span><a href="https://zh.wikisource.org/w/index.php?title=2005%E2%80%932015%E5%B9%B4%E4%B8%89%E6%B1%9F%E7%AB%99%E6%B0%B4%E5%88%86%E7%9B%91%E6%B5%8B%E6%95%B0%E6%8D%AE%E9%9B%86&amp;action=edit&amp;section=15" style="padding: 0px; margin: 0px; color: rgb(11, 0, 128); text-decoration-line: none; background-image: none; white-space: nowrap;" title="编辑章节：4 数据价值">编辑</a><span class="mw-editsection-bracket" style="padding: 0px; margin: 0px 0px 0px 0.25em; color: rgb(84, 89, 93);">]</span></span></h2>
<p style="padding: 0px; margin: 0.5em 0px; caret-color: rgb(32, 33, 34); color: rgb(32, 33, 34); font-family: sans-serif; font-size: 15.008px; text-size-adjust: auto;">
	本数据集的数据采集、质量控制遵循中国生态系统研究网络（CERN）长期规范，与其他台站的数据具有很好的可比性。同时三江站具有规范的气象、水文、土壤等要素的长期观测数据，对开展沼泽湿地生态系统水文特征研究具有良好的数据支撑作用。</p>

      
      (责任编辑：admin)</td>
    </tr>
   </table>
  </div>
  <!-- /content -->
  <div class="dede_pages">
   <ul class="pagelist">
    
   </ul>
  </div>

<center>  <a href='http://2v.dedecms.com/' id='__dedeqrcode_3'>织梦二维码生成器</a>
  <script type="text/javascript">
  	var __dedeqrcode_id=3;
  	var __dedeqrcode_aid=63;
  	var __dedeqrcode_type='arc';
  	var __dedeqrcode_dir='/plus';
  </script>
  <script language="javascript" type="text/javascript" src="/plus/img/qrcode.js"></script></center>
  
  <!-- /pages -->
  <!-- //顶踩 -->
  <div class="newdigg" id="newdigg">
   <div class="diggbox digg_good" onmousemove="this.style.backgroundPosition='left bottom';" onmouseout="this.style.backgroundPosition='left top';" onclick="javascript:postDigg('good',63)">
    <div class="digg_act">顶一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
   <div class="diggbox digg_bad" onmousemove="this.style.backgroundPosition='right bottom';" onmouseout="this.style.backgroundPosition='right top';" onclick="javascript:postDigg('bad',63)">
    <div class="digg_act">踩一下</div>
    <div class="digg_num">(0)</div>
    <div class="digg_percent">
     <div class="digg_percent_bar"><span style="width:0%"></span></div>
     <div class="digg_percent_num">0%</div>
    </div>
   </div>
  </div>
  <script language="javascript" type="text/javascript">getDigg(63);</script>
  <!-- //顶踩部分的源码结束 -->
  <!-- //分享代码开始 -->
  
  <!-- //分享代码结束 -->
  <div class="boxoff"> <strong>------分隔线----------------------------</strong> </div>
  <div class="handle">
   <div class="context">
    <ul>
     <li>上一篇：<a href='/a/xiangguanziliao/xiangguanziliao/2021/0810/62.html'>22007年龙岩市环境状况公报</a> </li>
     <li>下一篇：<a href='/a/xiangguanziliao/xiangguanziliao/2021/0810/64.html'>2004年美国的人权纪录</a> </li>
    </ul>
   </div>
   <!-- /context -->
   <div class="actbox">
    <ul>
     <li id="act-fav"><a href="/plus/stow.php?aid=63" target="_blank">收藏</a></li>
     <li id="act-err"><a href="/plus/erraddsave.php?aid=63&title=2005–2015年三江站水分监测数据集" target="_blank">挑错</a></li>
     <li id="act-pus"><a href="/plus/recommend.php?aid=63" target="_blank">推荐</a></li>
     <li id="act-pnt"><a href="#" onClick="window.print();">打印</a></li>
    </ul>
   </div>
   <!-- /actbox -->
  </div>
  <!-- /handle -->
 </div>
 <!-- /viewbox -->
 <!-- //AJAX评论区 -->
 <!-- //主模板必须要引入/include/dedeajax2.js -->
<a name='postform'></a>
<div class="mt1">
		<dl class="tbox">
			<dt>
				<strong>发表评论</strong>
				<span class="more"></span>
			</dt>
			<dd>
				<div class="dede_comment_post">
          <form action="#" method="post" name="feedback">
          <input type="hidden" name="dopost" value="send" />
          <input type="hidden" name="comtype" value="comments">
          <input type="hidden" name="aid" value="63" />
          <input type="hidden" name="fid" id='feedbackfid' value="0" />
          <div class="dcmp-title">
						<small>请自觉遵守互联网相关的政策法规，严禁发布色情、暴力、反动的言论。</small>
					</div><!-- /dcmp-title -->
					<div class="dcmp-stand">
						<strong>评价:</strong>
							<input type="radio" name="feedbacktype" checked="1" value="feedback" id="dcmp-stand-neu" /><label for="dcmp-stand-neu"><img src="/templets/default/images/cmt-neu.gif" />中立</label>
							<input type="radio" name="feedbacktype" value="good" id="dcmp-stand-good" /><label for="dcmp-stand-good"><img src="/templets/default/images/cmt-good.gif" />好评</label>
							<input type="radio" name="feedbacktype" value="bad" id="dcmp-stand-bad" /><label for="dcmp-stand-bad"><img src="/templets/default/images/cmt-bad.gif" />差评</label>
					</div><!-- /dcmp-stand -->
                    <div class="clr"></div>
                    <div class="dcmp-mood">
						<strong>表情:</strong>
						<ul>            
								                   
						</ul>
					</div><!-- /dcmp-mood -->
					<div class="dcmp-content">
						<textarea cols="60" name="msg" rows="5" class="ipt-txt"></textarea>
					</div><!-- /dcmp-content -->
					<div class="dcmp-post"><!--未登录-->
							<div class="dcmp-userinfo" id="_ajax_feedback">
								用户名:<input type="text" name="username" size="16" class="ipt-txt" style="text-transform: uppercase;"/>
                                验证码:<input type="text" name="validate" size="4" class="ipt-txt" style="text-transform:uppercase;"/><img src= "/include/vdimgck.php" id="validateimg" style="cursor:pointer" onclick="this.src=this.src+\'?\'" title="点击我更换图片" alt="点击我更换图片" />
                <input type="checkbox" name="notuser" id="dcmp-submit-guest" /><label for="dcmp-submit-guest" />匿名? </label>
							</div>
              
							<div class="dcmp-submit">
								<button type="button" onClick='PostComment()'>发表评论</button>
							</div>
						</div>
        </form>
				</div>
			</dd>
		</dl>
	</div><!-- //评论表单区结束 -->

<!-- //评论内容区 -->
	<a name='commettop'></a>
	<div class="mt1">
			<dl class="tbox">
				<dt>
					<strong>最新评论</strong>
					<span class="more"><a href="/plus/feedback.php?aid=63">进入详细评论页&gt;&gt;</a></span>
				</dt>
				<!-- //这两个ID的区块必须存在，否则JS会出错 -->
				<dd id='commetcontentNew'></dd>
				<dd id='commetcontent'></dd>
			</dl>
	</div>
<!--
//由于评论载入时使用异步传输，因此必须在最后一步加载（DIGG和评论框须放在评论内容前面）
//如果一定需要提前的把myajax.SendGet改为myajax.SendGet2，但可能会引起页面阻滞
-->
<script language='javascript'>
function LoadCommets(page)
{
		var taget_obj = document.getElementById('commetcontent');
		var waithtml = "<div style='line-height:50px'><img src='/images/loadinglit.gif' />评论加载中...</div>";
		var myajax = new DedeAjax(taget_obj, true, true, '', 'x', waithtml);
		myajax.SendGet2("/plus/feedback_ajax.php?dopost=getlist&aid=63&page="+page);
		DedeXHTTP = null;
}
function PostComment()
{
		var f = document.feedback;
		var nface = '6';
		var nfeedbacktype = 'feedback';
		var nvalidate = '';
		var nnotuser = '';
		var nusername = '';
		var npwd = '';
		var taget_obj = $DE('commetcontentNew');
		var waithtml = "<div style='line-height:30px'><img src='/images/loadinglit.gif' />正在发送中...</div>";
		if(f.msg.value=='')
		{
			alert("评论内容不能为空！");
			return;
		}
		if(f.validate)
		{
			if(f.validate.value=='') {
				alert("请填写验证码！");
				return;
			}
			else {
				nvalidate = f.validate.value;
			}
		}
		if(f.msg.value.length > 500)
		{
			alert("你的评论是不是太长了？请填写500字以内的评论。");
			return;
		}
		if(f.feedbacktype) {
			for(var i=0; i < f.feedbacktype.length; i++)
				if(f.feedbacktype[i].checked) nfeedbacktype = f.feedbacktype[i].value;
		}
		if(f.face) {
			for(var j=0; j < f.face.length; j++)
				if(f.face[j].checked) nface = f.face[j].value;
		}
		if(f.notuser.checked) nnotuser = '1';
		if(f.username) nusername = f.username.value;
		if(f.pwd) npwd = f.pwd.value;
		
		var myajax = new DedeAjax(taget_obj, false, true, '', '', waithtml);
		myajax.sendlang = 'utf-8';
		myajax.AddKeyN('dopost', 'send');
		myajax.AddKeyN('aid', '63');
		myajax.AddKeyN('fid', f.fid.value);
		myajax.AddKeyN('face', nface);
		myajax.AddKeyN('feedbacktype', nfeedbacktype);
		myajax.AddKeyN('validate', nvalidate);
		myajax.AddKeyN('notuser', nnotuser);
		myajax.AddKeyN('username', nusername);
		myajax.AddKeyN('pwd', npwd);
		myajax.AddKeyN('msg', f.msg.value);
		myajax.SendPost2('/plus/feedback_ajax.php');
		f.msg.value = '';
		f.fid.value = 0;
		if(f.validate)
		{
			if($DE('validateimg')) $DE('validateimg').src = "/include/vdimgck.php?"+f.validate.value;
			f.validate.value = '';
		}
}
function quoteCommet(fid)
{
	document.feedback.fid.value = fid;
}
LoadCommets(1);
</script><!-- //评论内容区结束 -->
 </div>
<!-- //左边内容结束 -->
<!-- //右边内容开始 -->
<div class="pright"> 
 <div class="pright">
  <div>
   <dl class="tbox">
    <dt><strong>栏目列表</strong></dt>
    <dd>
     <ul class="d6">
      
      <li><a href='/a/xiangguanziliao/xiangguanjianjie/'>相关简介</a></li>
      <li><a href='/a/xiangguanziliao/xiangguanziliao/' class='thisclass'>相关资料</a></li>
     </ul>
    </dd>
   </dl>
  </div>
  <div class="commend mt1">
   <dl class="tbox light">
    <dt class='light'><strong>推荐内容</strong></dt>
    <dd class='light'>
     <ul class="d4">
      <li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/59.html">南北共同宣言</a>
       <p>根据全民族渴望祖国和平统一的崇高意志，朝鲜民主主义人民共...</p>
      </li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/64.html">2004年美国的人权纪录</a>
       <p>关于生命、自由和人身安全 [ 编辑 ] 在美国，暴力犯罪泛滥成灾...</p>
      </li>

     </ul>
    </dd>
   </dl>
  </div>
  <!-- /commend -->
  <div class="hot mt1">
   <dl class="tbox light">
    <dt class='light'><strong>热点内容</strong></dt>
    <dd class='light'>
     <ul class="c1 ico2">
      <li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/60.html">换盏灯 爱地球 585白炽灯汰</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/63.html">2005–2015年三江站水分监测</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/61.html">2015年美国的人权纪录</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/64.html">2004年美国的人权纪录</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/62.html">22007年龙岩市环境状况公报</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/65.html">2004年中国人权事业的进展</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/59.html">南北共同宣言</a></li>
<li><a href="/a/xiangguanziliao/xiangguanziliao/2021/0810/66.html">2004–2016年中国生态系统研</a></li>

     </ul>
    </dd>
   </dl>
  </div>
 </div>
 <!-- /pright -->
</div>

<style type="text/css">
/* 小仙元码暗色主题 - 底部样式 */
.footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    border-top: 2px solid #00d4ff !important;
    border-radius: 15px 15px 0 0 !important;
    padding: 40px 20px !important;
    margin-top: 50px !important;
    text-align: center !important;
}
.footer_body {
    background: transparent !important;
}
.footer .powered {
    color: #ccc !important;
    line-height: 1.8 !important;
}
.footer .powered a {
    color: #00d4ff !important;
    transition: color 0.3s ease !important;
}
.footer .powered a:hover {
    color: #fff !important;
}
.footer .copyright {
    color: #666 !important;
    margin-top: 15px !important;
    padding-top: 15px !important;
    border-top: 1px solid #333 !important;
}
</style>

<!-- //底部模板 -->
<div class="footer w960 center mt1 clear">
    <div class="footer_left"></div>
    <div class="footer_body">
        <div style="margin-bottom: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 15px;">🎮 小仙元码 - 精品游戏资源分享平台</h3>
            <p style="color: #ccc; margin-bottom: 10px;">致力于为游戏爱好者提供最新、最全的游戏资源</p>
            <p style="color: #666;">基于 https://wd.xxymw.com 网站样式制作</p>
        </div>

        <div style="display: flex; justify-content: center; gap: 30px; margin-bottom: 20px; flex-wrap: wrap;">
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">快速导航</h4>
                <p><a href="/" style="color: #ccc;">首页</a></p>
                <p><a href="/plus/heightsearch.php" style="color: #ccc;">高级搜索</a></p>
                <p><a href="/tags.php" style="color: #ccc;">标签云</a></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">联系我们</h4>
                <p style="color: #ccc;">QQ群：123456789</p>
                <p style="color: #ccc;">微信：xiaoxianyuanma</p>
                <p style="color: #ccc;">邮箱：<EMAIL></p>
            </div>
            <div style="text-align: center;">
                <h4 style="color: #00d4ff; margin-bottom: 10px;">友情提示</h4>
                <p style="color: #ccc;">请支持正版游戏</p>
                <p style="color: #ccc;">理性游戏，健康生活</p>
                <p style="color: #ccc;">适度游戏益脑，沉迷游戏伤身</p>
            </div>
        </div>

        <p class="powered">
            Powered by <a href="http://www.dedecms.com" title="织梦内容管理系统(DedeCMS)" target="_blank"><strong>DedeCMS_V57_UTF8_SP2_117</strong></a> &copy; 2004-2024 <a href="http://www.desdev.cn/" target="_blank">DesDev</a> Inc.<br />
            <div class="copyright">Copyright &copy; 2004-2024 上海卓卓网络科技有限公司&nbsp;&nbsp;</div>
        </p>
    </div>
    <div class="footer_right"></div>
</div>

<script type="text/javascript">
// 小仙元码主题增强脚本
document.addEventListener('DOMContentLoaded', function() {
    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // 添加图片懒加载效果
    const images = document.querySelectorAll('img');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.style.opacity = '0';
                img.style.transition = 'opacity 0.5s ease';
                img.onload = () => { img.style.opacity = '1'; };
                observer.unobserve(img);
            }
        });
    });
    images.forEach(img => imageObserver.observe(img));

    // 添加返回顶部按钮
    const backToTop = document.createElement('div');
    backToTop.innerHTML = '↑';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        font-weight: bold;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
        z-index: 1000;
    `;

    backToTop.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    document.body.appendChild(backToTop);
});
</script>
<!-- /footer -->
<script type="text/javascript">
var contentRtPicAD2      = document.getElementById("contentRtPicAD2");
var   stop      = contentRtPicAD2.offsetTop - 60,
    docBody   = document.documentElement || document.body.parentNode || document.body,
    hasOffset = window.pageYOffset !== undefined,
    scrollTop;
window.onscroll = function (e) {
  // cross-browser compatible scrollTop.
  scrollTop = hasOffset ? window.pageYOffset : docBody.scrollTop;

  if (scrollTop >= stop) {
    contentRtPicAD2.className = 'stick';
  } else {
    contentRtPicAD2.className = ''; 
  }
}
</script>
</body>
</html>
