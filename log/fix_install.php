<?php
/**
 * 修复安装脚本 - 解决SQL语法错误
 */

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <title>修复安装</title>
    <style>
        body { font-family: Arial, sans-serif; background: #1a1a1a; color: #fff; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: #2d2d2d; padding: 30px; border-radius: 10px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .step { margin: 15px 0; padding: 15px; background: #333; border-radius: 8px; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 修复DedeCMS模版SQL错误</h1>";

$success_count = 0;
$error_count = 0;

// 确保目录存在
$dirs = ['templets/default', 'templets/default/style', 'templets/default/js', 'templets/default/images'];
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        @mkdir($dir, 0755, true);
    }
}

echo "<div class='step'><h3>📄 复制修复后的模版文件</h3>";

// 文件映射
$files = [
    'default/index_new.htm' => 'templets/default/index.htm',
    'default/list_new.htm' => 'templets/default/list_default.htm',
    'default/article_new.htm' => 'templets/default/article_article.htm',
    'default/head_new.htm' => 'templets/default/head.htm',
    'default/footer_new.htm' => 'templets/default/footer.htm',
    'default/style/main.css' => 'templets/default/style/main.css',
    'default/js/main.js' => 'templets/default/js/main.js'
];

foreach ($files as $source => $target) {
    if (file_exists($source)) {
        if (copy($source, $target)) {
            echo "<p class='success'>✅ 复制成功: $source → $target</p>";
            $success_count++;
        } else {
            echo "<p class='error'>❌ 复制失败: $source → $target</p>";
            $error_count++;
        }
    } else {
        echo "<p class='error'>❌ 源文件不存在: $source</p>";
        $error_count++;
    }
}

echo "</div>";

// 创建图片占位符
echo "<div class='step'><h3>🖼️ 创建图片占位符</h3>";

$image_placeholders = [
    'templets/default/images/logo.png' => '<!-- Logo占位符 -->',
    'templets/default/images/banner.jpg' => '<!-- Banner占位符 -->',
    'templets/default/images/placeholder.jpg' => '<!-- 占位符图片 -->',
    'templets/default/images/avatar.png' => '<!-- 头像占位符 -->',
    'templets/default/images/qr-qq.png' => '<!-- QQ二维码占位符 -->',
    'templets/default/images/qr-wechat.png' => '<!-- 微信二维码占位符 -->',
    'templets/default/images/ad-banner.jpg' => '<!-- 广告横幅占位符 -->'
];

foreach ($image_placeholders as $file => $content) {
    if (file_put_contents($file, $content)) {
        echo "<p class='success'>✅ 创建占位符: $file</p>";
    } else {
        echo "<p class='error'>❌ 创建失败: $file</p>";
    }
}

echo "</div>";

echo "<div class='step'><h3>📊 修复完成</h3>";
if ($error_count == 0) {
    echo "<p class='success'>🎉 所有文件已成功安装并修复SQL语法错误！</p>";
} else {
    echo "<p class='error'>⚠️ 安装完成，但有 $error_count 个错误</p>";
}

echo "<h4>📝 后续步骤：</h4>
<ol>
    <li>登录DedeCMS后台</li>
    <li>进入"生成" → "更新主页HTML"</li>
    <li>进入"生成" → "更新栏目HTML"</li>
    <li>进入"生成" → "更新文档HTML"</li>
    <li>清除浏览器缓存</li>
</ol>";

echo "<h4>🔧 已修复的问题：</h4>
<ul>
    <li>修复了 typeid='{dede:field.typeid/}' 的SQL语法错误</li>
    <li>改为使用 typeid='[field:typeid/]' 语法</li>
    <li>创建了必要的图片占位符文件</li>
</ul>";

echo "</div></div></body></html>";
?>
